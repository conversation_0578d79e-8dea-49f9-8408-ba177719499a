{"cells": [{"cell_type": "code", "execution_count": 246, "id": "32969978-25b7-4027-90e6-2c8f63fd7f9a", "metadata": {}, "outputs": [], "source": ["%matplotlib qt "]}, {"cell_type": "code", "execution_count": 247, "id": "535e1234-8ab2-43cb-8464-9f7015fb115c", "metadata": {}, "outputs": [], "source": ["from roboticstoolbox import *\n", "from spatialmath import *\n", "from math import pi\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 248, "id": "cf065a66-033b-493e-a61e-041d38ac8b0f", "metadata": {}, "outputs": [], "source": ["import time\n", "from Arm_Lib import Arm_Device"]}, {"cell_type": "code", "execution_count": 249, "id": "bed9255e-006c-424a-9715-7f1c00d61995", "metadata": {}, "outputs": [], "source": ["DFbot = DHRobot(\n", "    [\n", "                    RevoluteMDH(d=0.04145,qlim=np.array([-np.pi/2,np.pi/2])),\n", "                    RevoluteMDH(alpha=np.pi/2,qlim=np.array([-np.pi,0])),\n", "                    RevoluteMDH(a=-0.08285,qlim=np.array([-np.pi/2,np.pi/2])),\n", "                    RevoluteMDH(a=-0.08285,qlim=np.array([0,np.pi])),\n", "                    RevoluteMDH(alpha=-np.pi/2,d=0.11,qlim=np.array([np.pi/2,3/2*np.pi])),  \n", " \n", "    ],\n", "    name=\"DF<PERSON>\",\n", ")"]}, {"cell_type": "code", "execution_count": 250, "id": "4ec63acb-05f6-4fa7-8b08-bacda61b03c2", "metadata": {}, "outputs": [{"data": {"text/plain": ["PyPlot3D backend, t = 0.05, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 250, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.plot([0,0,0,0,0])"]}, {"cell_type": "code", "execution_count": 251, "id": "bec5347d-8f7a-4b17-97df-a29a9f80ace8", "metadata": {}, "outputs": [], "source": ["state0=[0,0,0,0,0]"]}, {"cell_type": "code", "execution_count": 252, "id": "7c6935f1-8d62-46b0-89f2-5861c1b9e339", "metadata": {}, "outputs": [], "source": ["T0=DFbot.fkine(state0)"]}, {"cell_type": "code", "execution_count": 253, "id": "84ad530f-84f3-438a-9552-fa739d824bb7", "metadata": {}, "outputs": [], "source": ["deg=[x/np.pi*180 for x in state0]"]}, {"cell_type": "code", "execution_count": 254, "id": "0ce05ba6-6592-4151-b41a-a02649410b64", "metadata": {}, "outputs": [{"data": {"text/plain": ["[0.0, 0.0, 0.0, 0.0, 0.0]"]}, "execution_count": 254, "metadata": {}, "output_type": "execute_result"}], "source": ["deg"]}, {"cell_type": "code", "execution_count": 255, "id": "c8cb02a6-b558-4c5b-bda5-bc681d9b0915", "metadata": {}, "outputs": [], "source": ["def convert_deg(deg):\n", "    d1=deg[0]+90\n", "    d2=-deg[1]\n", "    d3=90-deg[2]\n", "    d4=180-deg[3]\n", "    d5=deg[4]+90\n", "    return [d1,d2,d3,d4,d5]"]}, {"cell_type": "code", "execution_count": 256, "id": "6cfb0370-eb84-4324-be9a-e1b99ad46862", "metadata": {}, "outputs": [], "source": ["state0_deg=convert_deg(deg)"]}, {"cell_type": "code", "execution_count": 257, "id": "514d5b5d-cf93-4bba-9c3c-52ad959051cf", "metadata": {}, "outputs": [{"data": {"text/plain": ["[90.0, -0.0, 90.0, 180.0, 90.0]"]}, "execution_count": 257, "metadata": {}, "output_type": "execute_result"}], "source": ["state0_deg"]}, {"cell_type": "code", "execution_count": 269, "id": "2ad23e41-438e-4242-ba95-ae1d068733ca", "metadata": {}, "outputs": [], "source": ["Arm = Arm_Device()\n", "time.sleep(1)\n", "d_claw=0\n", "Arm.Arm_serial_servo_write6(state0_deg[0],state0_deg[1],state0_deg[2],state0_deg[3],state0_deg[4],d_claw, 1500)\n", "time.sleep(0.1)"]}, {"cell_type": "code", "execution_count": null, "id": "a4a8bd10-bb06-4c65-ad46-dff1f54596d6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 259, "id": "58145afa-c137-4509-a1dc-70fe57d38a76", "metadata": {}, "outputs": [], "source": ["state1=[np.pi/6,-np.pi/3,np.pi/6,np.pi,0]"]}, {"cell_type": "code", "execution_count": 260, "id": "26a0befd-8e87-42ae-ade7-5882c40b4772", "metadata": {}, "outputs": [{"data": {"text/plain": ["PyPlot3D backend, t = 0.05, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 260, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.plot(state1)"]}, {"cell_type": "code", "execution_count": 261, "id": "e7617676-a381-4897-a3be-3528eb33c518", "metadata": {}, "outputs": [], "source": ["T1=DFbot.fkine(state1)"]}, {"cell_type": "code", "execution_count": 262, "id": "fa1fdc90-7567-49a8-9a7b-b5cee421f5e1", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m-0.75    \u001b[0m \u001b[38;5;1m-0.5     \u001b[0m \u001b[38;5;1m-0.433   \u001b[0m \u001b[38;5;4m-0.1456  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m-0.433   \u001b[0m \u001b[38;5;1m 0.866   \u001b[0m \u001b[38;5;1m-0.25    \u001b[0m \u001b[38;5;4m-0.08409 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.5     \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-0.866   \u001b[0m \u001b[38;5;4m 0.05936 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 262, "metadata": {}, "output_type": "execute_result"}], "source": ["T1"]}, {"cell_type": "code", "execution_count": 263, "id": "a8f10b8f-b94a-45e6-ad89-cbac988348b8", "metadata": {}, "outputs": [], "source": ["deg1=[x/np.pi*180 for x in state1]"]}, {"cell_type": "code", "execution_count": 264, "id": "5ddc573b-5327-4aab-a416-ce9a5fcf37c3", "metadata": {}, "outputs": [], "source": ["state1_deg=convert_deg(deg1)"]}, {"cell_type": "code", "execution_count": 265, "id": "4daac5ed-677b-4278-9498-352cca34648d", "metadata": {}, "outputs": [], "source": ["Arm = Arm_Device()\n", "time.sleep(1)\n", "d_claw=0\n", "Arm.Arm_serial_servo_write6(state1_deg[0],state1_deg[1],state1_deg[2],state1_deg[3],state0_deg[4],d_claw, 1500)\n", "time.sleep(1)"]}, {"cell_type": "code", "execution_count": null, "id": "d81d9bba-a0e5-4220-b24a-bf8633f651af", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 266, "id": "61172b37-b034-47e8-9d9b-04030b639f22", "metadata": {}, "outputs": [{"data": {"text/plain": ["PyPlot3D backend, t = 1.0000000000000002, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 266, "metadata": {}, "output_type": "execute_result"}], "source": ["import roboticstoolbox as rtb\n", "qt = rtb.tools.trajectory.jtraj(state0, state1, 20)\n", "DFbot.plot(qt.q, backend='pyplot', movie='panda3.gif')"]}, {"cell_type": "code", "execution_count": 232, "id": "7e5aed56-fab1-4940-bab5-e5e361b39ac5", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00],\n", "       [ 7.04377136e-04, -1.40875427e-03,  7.04377136e-04,\n", "         4.22626281e-03,  0.00000000e+00],\n", "       [ 5.18333604e-03, -1.03666721e-02,  5.18333604e-03,\n", "         3.11000162e-02,  0.00000000e+00],\n", "       [ 1.60378490e-02, -3.20756981e-02,  1.60378490e-02,\n", "         9.62270942e-02,  0.00000000e+00],\n", "       [ 3.47269981e-02, -6.94539962e-02,  3.47269981e-02,\n", "         2.08361989e-01,  0.00000000e+00],\n", "       [ 6.17202271e-02, -1.23440454e-01,  6.17202271e-02,\n", "         3.70321363e-01,  0.00000000e+00],\n", "       [ 9.66495936e-02, -1.93299187e-01,  9.66495936e-02,\n", "         5.79897561e-01,  0.00000000e+00],\n", "       [ 1.38462021e-01, -2.76924042e-01,  1.38462021e-01,\n", "         8.30772125e-01,  0.00000000e+00],\n", "       [ 1.85571550e-01, -3.71143101e-01,  1.85571550e-01,\n", "         1.11342930e+00,  0.00000000e+00],\n", "       [ 2.36011593e-01, -4.72023186e-01,  2.36011593e-01,\n", "         1.41606956e+00,  0.00000000e+00],\n", "       [ 2.87587182e-01, -5.75174365e-01,  2.87587182e-01,\n", "         1.72552309e+00,  0.00000000e+00],\n", "       [ 3.38027225e-01, -6.76054450e-01,  3.38027225e-01,\n", "         2.02816335e+00,  0.00000000e+00],\n", "       [ 3.85136755e-01, -7.70273509e-01,  3.85136755e-01,\n", "         2.31082053e+00,  0.00000000e+00],\n", "       [ 4.26949182e-01, -8.53898364e-01,  4.26949182e-01,\n", "         2.56169509e+00,  0.00000000e+00],\n", "       [ 4.61878548e-01, -9.23757097e-01,  4.61878548e-01,\n", "         2.77127129e+00,  0.00000000e+00],\n", "       [ 4.88871777e-01, -9.77743555e-01,  4.88871777e-01,\n", "         2.93323066e+00,  0.00000000e+00],\n", "       [ 5.07560927e-01, -1.01512185e+00,  5.07560927e-01,\n", "         3.04536556e+00,  0.00000000e+00],\n", "       [ 5.18415440e-01, -1.03683088e+00,  5.18415440e-01,\n", "         3.11049264e+00,  0.00000000e+00],\n", "       [ 5.22894398e-01, -1.04578880e+00,  5.22894398e-01,\n", "         3.13736639e+00,  0.00000000e+00],\n", "       [ 5.23598776e-01, -1.04719755e+00,  5.23598776e-01,\n", "         3.14159265e+00,  0.00000000e+00]])"]}, "execution_count": 232, "metadata": {}, "output_type": "execute_result"}], "source": ["qt.q"]}, {"cell_type": "code", "execution_count": 233, "id": "5e08a050-16c9-41b0-a282-08f10b75b103", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0.00070438, -0.00140875,  0.00070438,  0.00422626,  0.        ])"]}, "execution_count": 233, "metadata": {}, "output_type": "execute_result"}], "source": ["qt.q[1]"]}, {"cell_type": "code", "execution_count": null, "id": "d06202bd-fca2-4d95-aa7e-cd90fdb70454", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 270, "id": "2d10a464-5c6c-46f6-8921-1c586ec595cd", "metadata": {}, "outputs": [], "source": ["Arm = Arm_Device()\n", "time.sleep(1)\n", "for k in range(len(qt.q)):\n", "    deg=[x/np.pi*180 for x in qt.q[k]]\n", "    state_deg=convert_deg(deg)\n", "    d_claw=0\n", "    Arm.Arm_serial_servo_write6(state_deg[0],state_deg[1],state_deg[2],state_deg[3],state_deg[4],d_claw, 1500)\n", "    time.sleep(0.1)"]}, {"cell_type": "code", "execution_count": null, "id": "67abc075-391f-4e02-a38b-432d29278448", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 271, "id": "1202f75b-2b7d-4591-a0bf-abca2a4b0bc9", "metadata": {"scrolled": true}, "outputs": [], "source": ["state2=[0,0,0,np.pi/2,0]\n", "T2=DFbot.fkine(state2)\n", "state3=[0,0,0,0,0]\n", "T3=DFbot.fkine(state3)\n"]}, {"cell_type": "code", "execution_count": 272, "id": "1ed0cab5-c3a5-4560-82c0-ad7b2649b02d", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1657  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.1515  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 272, "metadata": {}, "output_type": "execute_result"}], "source": ["T3"]}, {"cell_type": "code", "execution_count": 274, "id": "42777906-8c6d-4e9f-90b5-599e60138baa", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["PyPlot3D backend, t = 0.05, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 274, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.plot(state3)"]}, {"cell_type": "code", "execution_count": 275, "id": "82211c09-381f-4fa9-86b6-a9dece37eb98", "metadata": {}, "outputs": [], "source": ["qt2 = rtb.tools.trajectory.ctraj(T2, T3, 2)"]}, {"cell_type": "code", "execution_count": 276, "id": "c011a722-7b54-40af-b16b-1b0067ae8621", "metadata": {}, "outputs": [{"data": {"text/plain": ["0:\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-1       \u001b[0m \u001b[38;5;4m-0.2757  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0.04145 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", "1:\n", "  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1657  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.1515  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 276, "metadata": {}, "output_type": "execute_result"}], "source": ["qt2"]}, {"cell_type": "code", "execution_count": 277, "id": "c6ed32f5-3cc5-44c0-b654-73d79452e4c4", "metadata": {}, "outputs": [], "source": ["pose_list=[state2]\n", "for w in range(2):\n", "    pose=DFbot.ikine_LM(qt2[w],q0=state2,ilimit=100, slimit=100,joint_limits=True)\n", "    pose_list.append(pose.q)"]}, {"cell_type": "code", "execution_count": 278, "id": "9fdadd2e-d08d-460e-8959-166de58456f5", "metadata": {}, "outputs": [], "source": ["pose_array=np.array(pose_list)"]}, {"cell_type": "code", "execution_count": 279, "id": "9c6ddf45-1103-4cb1-9e9b-7634c5304a9f", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         1.57079633e+00,  0.00000000e+00],\n", "       [ 3.14159265e+00, -3.08416570e+00, -1.14851829e-01,\n", "         1.62822124e+00,  3.14159265e+00],\n", "       [-2.28397923e-09,  4.54415571e-02, -9.08831274e-02,\n", "         4.54420334e-02,  2.28385133e-09]])"]}, "execution_count": 279, "metadata": {}, "output_type": "execute_result"}], "source": ["pose_array"]}, {"cell_type": "code", "execution_count": 280, "id": "df7633fa-3009-4865-9266-e48421291783", "metadata": {}, "outputs": [{"data": {"text/plain": ["PyPlot3D backend, t = 0.15000000000000002, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 280, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.plot(pose_array, backend='pyplot', movie='panda4.gif')"]}, {"cell_type": "code", "execution_count": 282, "id": "f3c2cfd3-67af-4acf-836e-f61c545d14c3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["参数传入范围不在0-180之内！\n"]}], "source": ["Arm = Arm_Device()\n", "time.sleep(1)\n", "for k in range(len(pose_array)):\n", "    deg=[x/np.pi*180 for x in pose_array[k]]\n", "    state_deg=convert_deg(deg)\n", "    d_claw=0\n", "    Arm.Arm_serial_servo_write6(state_deg[0],state_deg[1],state_deg[2],state_deg[3],state_deg[4],d_claw, 1500)\n", "    time.sleep(0.3)"]}, {"cell_type": "code", "execution_count": 245, "id": "1d93aa27-84ba-4fb7-bc79-60b9044a5f5e", "metadata": {}, "outputs": [{"data": {"text/plain": ["Trajectory created by jtraj: 20 time steps x 5 axes"]}, "execution_count": 245, "metadata": {}, "output_type": "execute_result"}], "source": ["qt"]}, {"cell_type": "code", "execution_count": 283, "id": "b6af59c1-a9e7-498a-ad4d-14250bd3aa8f", "metadata": {}, "outputs": [], "source": ["#关节空间运动\n", "qt = rtb.tools.trajectory.jtraj(state0, state1, 5)\n", "T_joint=[DFbot.fkine(x) for x in qt.q]"]}, {"cell_type": "code", "execution_count": 284, "id": "7ee42b7f-7cd8-4e72-a34f-54fad81c4b76", "metadata": {}, "outputs": [{"data": {"text/plain": ["[  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1657  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.1515  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", ",\n", "   \u001b[38;5;1m 0.9621  \u001b[0m \u001b[38;5;1m-0.05417 \u001b[0m \u001b[38;5;1m-0.2673  \u001b[0m \u001b[38;5;4m-0.1943  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.0522  \u001b[0m \u001b[38;5;1m 0.9985  \u001b[0m \u001b[38;5;1m-0.0145  \u001b[0m \u001b[38;5;4m-0.01054 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.2677  \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0.9635  \u001b[0m \u001b[38;5;4m 0.1609  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", ",\n", "   \u001b[38;5;1m 0.25    \u001b[0m \u001b[38;5;1m-0.2588  \u001b[0m \u001b[38;5;1m-0.933   \u001b[0m \u001b[38;5;4m-0.2492  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.06699 \u001b[0m \u001b[38;5;1m 0.9659  \u001b[0m \u001b[38;5;1m-0.25    \u001b[0m \u001b[38;5;4m-0.06678 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.9659  \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0.2588  \u001b[0m \u001b[38;5;4m 0.1328  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", ",\n", "   \u001b[38;5;1m-0.6248  \u001b[0m \u001b[38;5;1m-0.4523  \u001b[0m \u001b[38;5;1m-0.6364  \u001b[0m \u001b[38;5;4m-0.1796  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m-0.3169  \u001b[0m \u001b[38;5;1m 0.8918  \u001b[0m \u001b[38;5;1m-0.3228  \u001b[0m \u001b[38;5;4m-0.09107 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.7136  \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-0.7006  \u001b[0m \u001b[38;5;4m 0.06871 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", ",\n", "   \u001b[38;5;1m-0.75    \u001b[0m \u001b[38;5;1m-0.5     \u001b[0m \u001b[38;5;1m-0.433   \u001b[0m \u001b[38;5;4m-0.1456  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m-0.433   \u001b[0m \u001b[38;5;1m 0.866   \u001b[0m \u001b[38;5;1m-0.25    \u001b[0m \u001b[38;5;4m-0.08409 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.5     \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-0.866   \u001b[0m \u001b[38;5;4m 0.05936 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", "]"]}, "execution_count": 284, "metadata": {}, "output_type": "execute_result"}], "source": ["T_joint"]}, {"cell_type": "code", "execution_count": 206, "id": "1d3047a0-a7fc-4f28-bcab-43c03b10f8e6", "metadata": {}, "outputs": [], "source": ["#笛卡尔空间运动"]}, {"cell_type": "code", "execution_count": 285, "id": "774cf251-8e55-4a0e-a18a-a96f1b399f64", "metadata": {}, "outputs": [], "source": ["qt2 = rtb.tools.trajectory.ctraj(T0, T1, 5)"]}, {"cell_type": "code", "execution_count": 286, "id": "40c37273-2d7d-4653-b6ce-b86725bca0c8", "metadata": {}, "outputs": [{"data": {"text/plain": ["0:\n", "  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1657  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.1515  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", "1:\n", "  \u001b[38;5;1m 0.9366  \u001b[0m \u001b[38;5;1m-0.04197 \u001b[0m \u001b[38;5;1m-0.3479  \u001b[0m \u001b[38;5;4m-0.1629  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.008162\u001b[0m \u001b[38;5;1m 0.9951  \u001b[0m \u001b[38;5;1m-0.09807 \u001b[0m \u001b[38;5;4m-0.01182 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.3503  \u001b[0m \u001b[38;5;1m 0.08901 \u001b[0m \u001b[38;5;1m 0.9324  \u001b[0m \u001b[38;5;4m 0.1385  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", "2:\n", "  \u001b[38;5;1m 0.3     \u001b[0m \u001b[38;5;1m-0.2536  \u001b[0m \u001b[38;5;1m-0.9196  \u001b[0m \u001b[38;5;4m-0.1557  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m-0.1196  \u001b[0m \u001b[38;5;1m 0.9464  \u001b[0m \u001b[38;5;1m-0.3     \u001b[0m \u001b[38;5;4m-0.04204 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.9464  \u001b[0m \u001b[38;5;1m 0.2     \u001b[0m \u001b[38;5;1m 0.2536  \u001b[0m \u001b[38;5;4m 0.1054  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", "3:\n", "  \u001b[38;5;1m-0.5308  \u001b[0m \u001b[38;5;1m-0.4612  \u001b[0m \u001b[38;5;1m-0.711   \u001b[0m \u001b[38;5;4m-0.1485  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m-0.3549  \u001b[0m \u001b[38;5;1m 0.8828  \u001b[0m \u001b[38;5;1m-0.3077  \u001b[0m \u001b[38;5;4m-0.07226 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.7696  \u001b[0m \u001b[38;5;1m 0.08901 \u001b[0m \u001b[38;5;1m-0.6323  \u001b[0m \u001b[38;5;4m 0.07231 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", "4:\n", "  \u001b[38;5;1m-0.75    \u001b[0m \u001b[38;5;1m-0.5     \u001b[0m \u001b[38;5;1m-0.433   \u001b[0m \u001b[38;5;4m-0.1456  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m-0.433   \u001b[0m \u001b[38;5;1m 0.866   \u001b[0m \u001b[38;5;1m-0.25    \u001b[0m \u001b[38;5;4m-0.08409 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.5     \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-0.866   \u001b[0m \u001b[38;5;4m 0.05936 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 286, "metadata": {}, "output_type": "execute_result"}], "source": ["qt2"]}, {"cell_type": "code", "execution_count": null, "id": "038d7dbf-55e3-41e1-957e-6439da31881e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}