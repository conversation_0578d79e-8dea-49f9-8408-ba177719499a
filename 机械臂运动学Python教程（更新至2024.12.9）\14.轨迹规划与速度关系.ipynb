{"cells": [{"cell_type": "code", "execution_count": 26, "id": "2fbb490e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using matplotlib backend: MacOSX\n"]}], "source": ["import roboticstoolbox as rtb\n", "from spatialmath import *\n", "import numpy as np\n", "%matplotlib auto"]}, {"cell_type": "code", "execution_count": 27, "id": "9790cb63", "metadata": {}, "outputs": [], "source": ["DFbot = rtb.DHRobot(\n", "    [\n", "                    rtb.RevoluteDH(d=0.04145,alpha=np.pi/2,qlim=np.array([-np.pi,np.pi])),\n", "                    rtb.RevoluteDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),\n", "                    rtb.RevoluteDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),\n", "                    rtb.RevoluteDH(alpha=-np.pi/2,qlim=np.array([-np.pi,np.pi])),\n", "                    rtb.RevoluteDH(d=0.11,qlim=np.array([-np.pi,3/2*np.pi])),\n", "                  \n", "    ],\n", "    name=\"DF<PERSON>\",\n", ")"]}, {"cell_type": "code", "execution_count": 10, "id": "92697cc3", "metadata": {}, "outputs": [], "source": ["state0=[0,0,0,0,0]\n", "state1=[0,-np.pi/6,0,np.pi/6,0]"]}, {"cell_type": "code", "execution_count": 4, "id": "4de6aa7c", "metadata": {}, "outputs": [], "source": ["#梯形"]}, {"cell_type": "code", "execution_count": 11, "id": "a5fe81fe", "metadata": {}, "outputs": [], "source": ["from roboticstoolbox import trapezoidal\n", "qt = trapezoidal(0.1, 0.12, 10)\n", "#DFbot.plot(qt.q, backend='pyplot', movie='panda3.gif')"]}, {"cell_type": "code", "execution_count": 12, "id": "c1b06d76", "metadata": {}, "outputs": [], "source": ["qt.plot()"]}, {"cell_type": "code", "execution_count": 13, "id": "b38949b7", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.1       , 0.10055556, 0.10222222, 0.105     , 0.10833333,\n", "       0.11166667, 0.115     , 0.11777778, 0.11944444, 0.12      ])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["qt.q"]}, {"cell_type": "code", "execution_count": 49, "id": "6854a835", "metadata": {}, "outputs": [], "source": ["#五次多项式"]}, {"cell_type": "code", "execution_count": 14, "id": "a213ab81", "metadata": {}, "outputs": [], "source": ["from roboticstoolbox import quintic\n", "qt = quintic(0.1, 0.12, 10)\n", "#DFbot.plot(qt.q, backend='pyplot', movie='panda3.gif')"]}, {"cell_type": "code", "execution_count": 16, "id": "ffa976ec", "metadata": {}, "outputs": [], "source": ["qt.plot()"]}, {"cell_type": "code", "execution_count": 17, "id": "4437dba2", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.1       , 0.10023066, 0.10152822, 0.10419753, 0.10793375,\n", "       0.11206625, 0.11580247, 0.11847178, 0.11976934, 0.12      ])"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["qt.q"]}, {"cell_type": "code", "execution_count": 31, "id": "c92811b5", "metadata": {}, "outputs": [], "source": ["#关节坐标运动"]}, {"cell_type": "code", "execution_count": 18, "id": "255f23cb", "metadata": {}, "outputs": [], "source": ["import roboticstoolbox as rtb\n", "qt = rtb.tools.trajectory.jtraj(state0, state1, 10)\n", "#DFbot.plot(qt.q, backend='pyplot', movie='panda3.gif')"]}, {"cell_type": "code", "execution_count": 19, "id": "9ababe84", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0.        ,  0.        ,  0.        ,  0.        ,  0.        ],\n", "       [ 0.        , -0.00603856,  0.        ,  0.00603856,  0.        ],\n", "       [ 0.        , -0.04000877,  0.        ,  0.04000877,  0.        ],\n", "       [ 0.        , -0.1098911 ,  0.        ,  0.1098911 ,  0.        ],\n", "       [ 0.        , -0.20770509,  0.        ,  0.20770509,  0.        ],\n", "       [ 0.        , -0.31589369,  0.        ,  0.31589369,  0.        ],\n", "       [ 0.        , -0.41370767,  0.        ,  0.41370767,  0.        ],\n", "       [ 0.        , -0.48359001,  0.        ,  0.48359001,  0.        ],\n", "       [ 0.        , -0.51756022,  0.        ,  0.51756022,  0.        ],\n", "       [ 0.        , -0.52359878,  0.        ,  0.52359878,  0.        ]])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["qt.q"]}, {"cell_type": "code", "execution_count": 20, "id": "3d8f8998", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00],\n", "       [ 0.00000000e+00, -1.53225065e-01,  0.00000000e+00,\n", "         1.53225065e-01,  0.00000000e+00],\n", "       [ 0.00000000e+00, -4.69251760e-01,  0.00000000e+00,\n", "         4.69251760e-01,  0.00000000e+00],\n", "       [ 0.00000000e+00, -7.75701890e-01,  0.00000000e+00,\n", "         7.75701890e-01,  0.00000000e+00],\n", "       [ 0.00000000e+00, -9.57656654e-01,  0.00000000e+00,\n", "         9.57656654e-01,  0.00000000e+00],\n", "       [ 0.00000000e+00, -9.57656654e-01,  0.00000000e+00,\n", "         9.57656654e-01,  0.00000000e+00],\n", "       [ 0.00000000e+00, -7.75701890e-01,  0.00000000e+00,\n", "         7.75701890e-01,  0.00000000e+00],\n", "       [ 0.00000000e+00, -4.69251760e-01,  0.00000000e+00,\n", "         4.69251760e-01,  0.00000000e+00],\n", "       [ 0.00000000e+00, -1.53225065e-01,  0.00000000e+00,\n", "         1.53225065e-01,  0.00000000e+00],\n", "       [ 0.00000000e+00, -1.77635684e-15,  0.00000000e+00,\n", "         1.77635684e-15,  0.00000000e+00]])"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["qt.qd"]}, {"cell_type": "code", "execution_count": 21, "id": "0999ba82", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00],\n", "       [ 0.00000000e+00, -2.41329477e+00,  0.00000000e+00,\n", "         2.41329477e+00,  0.00000000e+00],\n", "       [ 0.00000000e+00, -3.01661846e+00,  0.00000000e+00,\n", "         3.01661846e+00,  0.00000000e+00],\n", "       [ 0.00000000e+00, -2.32710567e+00,  0.00000000e+00,\n", "         2.32710567e+00,  0.00000000e+00],\n", "       [ 0.00000000e+00, -8.61890989e-01,  0.00000000e+00,\n", "         8.61890989e-01,  0.00000000e+00],\n", "       [ 0.00000000e+00,  8.61890989e-01,  0.00000000e+00,\n", "        -8.61890989e-01,  0.00000000e+00],\n", "       [ 0.00000000e+00,  2.32710567e+00,  0.00000000e+00,\n", "        -2.32710567e+00,  0.00000000e+00],\n", "       [ 0.00000000e+00,  3.01661846e+00,  0.00000000e+00,\n", "        -3.01661846e+00,  0.00000000e+00],\n", "       [ 0.00000000e+00,  2.41329477e+00,  0.00000000e+00,\n", "        -2.41329477e+00,  0.00000000e+00],\n", "       [ 0.00000000e+00, -3.55271368e-15,  0.00000000e+00,\n", "         3.55271368e-15,  0.00000000e+00]])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["qt.qdd"]}, {"cell_type": "code", "execution_count": 22, "id": "01374436", "metadata": {}, "outputs": [], "source": ["qt.plot()"]}, {"cell_type": "code", "execution_count": 23, "id": "5ed52b1d", "metadata": {}, "outputs": [], "source": ["#笛卡尔运动"]}, {"cell_type": "code", "execution_count": 7, "id": "7f3a61c0", "metadata": {}, "outputs": [], "source": ["state0=[0,0,0,0,0]\n", "T0=DFbot.fkine(state0)\n", "state1=[0,-np.pi/6,0,np.pi/6,0]\n", "T1=DFbot.fkine(state1)\n", "qt = rtb.tools.trajectory.ctraj(T0,T1, 10)\n", "#DFbot.plot(qt, backend='pyplot', movie='panda3.gif')"]}, {"cell_type": "code", "execution_count": 27, "id": "397f6452", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["0:\n", "  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1657  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.1515  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", "1:\n", "  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1651  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.1538  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", "2:\n", "  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1632  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.1607  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", "3:\n", "  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1602  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.1722  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", "4:\n", "  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1565  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.186   \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", "5:\n", "  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1528  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.1998  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", "6:\n", "  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1491  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.2136  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", "7:\n", "  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.146   \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.2251  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", "8:\n", "  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1441  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.232   \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n", "9:\n", "  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1435  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.2343  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["qt"]}, {"cell_type": "code", "execution_count": 28, "id": "2b2c796b", "metadata": {}, "outputs": [], "source": ["pose_list=[]\n", "for i in range(9):\n", "    sol = DFbot.ikine_LM(qt[i+1],q0=[0,0,0,0,0],ilimit=100, slimit=100,joint_limits=True,tol=0.005)\n", "    pose_list.append(sol.q)"]}, {"cell_type": "code", "execution_count": 29, "id": "409f7025", "metadata": {}, "outputs": [{"data": {"text/plain": ["[array([ 0.00000000e+00, -1.38748482e-02, -2.23533042e-05,  1.38301416e-02,\n", "         0.00000000e+00]),\n", " array([ 0.00000000e+00, -5.53277358e-02, -9.01863287e-05,  5.51473631e-02,\n", "         0.00000000e+00]),\n", " array([ 0.        , -0.12284116, -0.00021033,  0.12242049,  0.        ]),\n", " array([ 0.        , -0.19890941, -0.00037678,  0.19815584,  0.        ]),\n", " array([ 0.        , -0.26707859, -0.00057877,  0.26592104,  0.        ]),\n", " array([ 0.        , -0.32563242, -0.00082398,  0.32398447,  0.        ]),\n", " array([ 0.        , -0.36653713, -0.00106372,  0.3644097 ,  0.        ]),\n", " array([ 0.        , -0.38763348, -0.001223  ,  0.38518747,  0.        ]),\n", " array([ 0.        , -0.39410357, -0.00127861,  0.39154634,  0.        ])]"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["pose_list"]}, {"cell_type": "code", "execution_count": 2, "id": "a2aca6dd", "metadata": {}, "outputs": [], "source": ["#速度关系"]}, {"cell_type": "code", "execution_count": 28, "id": "83fe14a9", "metadata": {}, "outputs": [], "source": ["state0=[0,0,0,0,0]\n", "T0=DFbot.fkine(state0)\n", "state1=[0.000001,0,0,0,0]\n", "T1=DFbot.fkine(state1)"]}, {"cell_type": "code", "execution_count": 29, "id": "593ee779", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1657  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.1515  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["T0"]}, {"cell_type": "code", "execution_count": 30, "id": "d317ce92", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m-1e-06   \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1657  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 1e-06   \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-1.657e-07\u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.1515  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["T1"]}, {"cell_type": "code", "execution_count": 31, "id": "e15a2212", "metadata": {}, "outputs": [], "source": ["from IPython.display import display,Latex,Math\n", "%matplotlib inline\n", " \n", "from IPython.core.interactiveshell import InteractiveShell\n", "sh = InteractiveShell.instance()\n", " \n", "def number_to_str(n,cut=5):\n", "  ns=str(n)\n", "  format_='{0:.'+str(cut)+'f}'\n", "  if 'e' in ns or ('.' in ns and len(ns)>cut+1):\n", "    return format_.format(n)\n", "  else:\n", "    return str(n)\n", " \n", "def matrix_to_latex(mat,style='bmatrix'):\n", "  if type(mat)==np.matrixlib.defmatrix.matrix:\n", "    mat=mat.A\n", "  head=r'\\begin{'+style+'}'\n", "  tail=r'\\end{'+style+'}'\n", "  if len(mat.shape)==1:\n", "    body=r'\\\\'.join([str(el) for el in mat])\n", "    return head+body+tail\n", "  elif len(mat.shape)==2:\n", "    lines=[]\n", "    for row in mat:\n", "      lines.append('&'.join([number_to_str(el) for el in row])+r'\\\\')\n", "    s=head+' '.join(lines)+tail\n", "    return s\n", "  return None\n", " \n", "sh.display_formatter.formatters['text/latex'].type_printers[np.ndarray]=matrix_to_latex"]}, {"cell_type": "code", "execution_count": null, "id": "beb8c93c", "metadata": {}, "outputs": [], "source": ["#我们需要知道(Vx Vy Vz Wx Wy Wz)"]}, {"cell_type": "code", "execution_count": 32, "id": "d7de9f6c", "metadata": {}, "outputs": [{"data": {"text/latex": ["\\begin{bmatrix}-0.00000&-1.00000&-0.00000&0.00000\\\\ 1.00000&-0.00000&0.00000&-0.16570\\\\ 0.0&0.0&0.0&0.0\\\\ 0.0&0.0&0.0&0.0\\\\\\end{bmatrix}"], "text/plain": ["array([[-5.00044450e-07, -1.00000000e+00, -5.03786535e-33,\n", "         8.28503932e-08],\n", "       [ 1.00000000e+00, -5.00044450e-07,  1.22789013e-27,\n", "        -1.65700000e-01],\n", "       [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00],\n", "       [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00]])"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["drq=(T1-T0)/0.000001\n", "drq"]}, {"cell_type": "code", "execution_count": 19, "id": "3b70ee32", "metadata": {}, "outputs": [], "source": ["#x,y,z上的速度(Vx Vy Vz)比较好看出来"]}, {"cell_type": "code", "execution_count": 20, "id": "b95fe9a9", "metadata": {}, "outputs": [{"data": {"text/latex": ["\\begin{bmatrix}8.285039321265231e-08\\\\-0.16569999999997237\\\\0.0\\\\0.0\\end{bmatrix}"], "text/plain": ["array([ 8.28503932e-08, -1.65700000e-01,  0.00000000e+00,  0.00000000e+00])"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["drq[:,-1]"]}, {"cell_type": "code", "execution_count": null, "id": "0aafdfc4", "metadata": {}, "outputs": [], "source": ["#x,y,z上的角速度比较难看出来   drqR=s(w)*R    s(w)=drqR*inv(R)"]}, {"cell_type": "code", "execution_count": 66, "id": "a3076410", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1657  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.1515  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["T0"]}, {"cell_type": "code", "execution_count": 14, "id": "97c00c4a", "metadata": {}, "outputs": [], "source": ["R=np.array(T0)[0:3,0:3]"]}, {"cell_type": "code", "execution_count": 15, "id": "cd564455", "metadata": {}, "outputs": [{"data": {"text/latex": ["\\begin{bmatrix}1.0&0.0&0.0\\\\ 0.0&1.0&0.0\\\\ 0.0&0.0&1.0\\\\\\end{bmatrix}"], "text/plain": ["array([[1., 0., 0.],\n", "       [0., 1., 0.],\n", "       [0., 0., 1.]])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["R"]}, {"cell_type": "code", "execution_count": 16, "id": "de4f1ce5", "metadata": {}, "outputs": [{"data": {"text/latex": ["\\begin{bmatrix}-0.00000&-1.00000&-0.00000\\\\ 1.00000&-0.00000&0.00000\\\\ 0.0&0.0&0.0\\\\\\end{bmatrix}"], "text/plain": ["array([[-5.00044450e-07, -1.00000000e+00, -5.03786535e-33],\n", "       [ 1.00000000e+00, -5.00044450e-07,  1.22789013e-27],\n", "       [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00]])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["w=drq[0:3,0:3].dot(R.T)\n", "w"]}, {"cell_type": "code", "execution_count": 17, "id": "a8908a53", "metadata": {}, "outputs": [{"data": {"text/latex": ["\\begin{bmatrix}-6.1394506673293575e-28\\\\-2.5189326767135026e-33\\\\0.9999999999998334\\end{bmatrix}"], "text/plain": ["array([-6.13945067e-28, -2.51893268e-33,  1.00000000e+00])"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["from spatialmath.base import *\n", "vex(w)"]}, {"cell_type": "code", "execution_count": null, "id": "1d0f66bf", "metadata": {}, "outputs": [], "source": ["#"]}, {"cell_type": "code", "execution_count": 18, "id": "808b8c5a", "metadata": {}, "outputs": [{"data": {"text/latex": ["\\begin{bmatrix}0.0&-0.11&-0.11&-0.11&0.0\\\\ -0.16570&-0.00000&-0.00000&0.0&0.0\\\\ 0.0&-0.16570&-0.08285&0.0&0.0\\\\ 0.0&0.0&0.0&0.0&0.0\\\\ 0.0&-1.0&-1.0&-1.0&0.0\\\\ 1.0&0.00000&0.00000&0.00000&1.0\\\\\\end{bmatrix}"], "text/plain": ["array([[ 0.00000000e+00, -1.10000000e-01, -1.10000000e-01,\n", "        -1.10000000e-01,  0.00000000e+00],\n", "       [-1.65700000e-01, -1.01461987e-17, -5.07309937e-18,\n", "         0.00000000e+00,  0.00000000e+00],\n", "       [ 0.00000000e+00, -1.65700000e-01, -8.28500000e-02,\n", "         0.00000000e+00,  0.00000000e+00],\n", "       [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00],\n", "       [ 0.00000000e+00, -1.00000000e+00, -1.00000000e+00,\n", "        -1.00000000e+00,  0.00000000e+00],\n", "       [ 1.00000000e+00,  6.12323400e-17,  6.12323400e-17,\n", "         6.12323400e-17,  1.00000000e+00]])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["J=DFbot.jacob0([0, 0, 0, 0, 0])\n", "J"]}, {"cell_type": "code", "execution_count": 21, "id": "c5680697", "metadata": {}, "outputs": [{"data": {"text/latex": ["\\begin{bmatrix}-0.0&-0.11&-0.11&-0.11&0.0\\\\ -0.16570&-0.00000&-0.00000&0.0&0.0\\\\ -0.0&-0.16570&-0.08285&0.0&0.0\\\\ 0.0&0.0&0.0&0.0&0.0\\\\ 0.0&-1.0&-1.0&-1.0&0.0\\\\ 1.0&0.00000&0.00000&0.00000&1.0\\\\\\end{bmatrix}"], "text/plain": ["array([[-0.00000000e+00, -1.10000000e-01, -1.10000000e-01,\n", "        -1.10000000e-01,  0.00000000e+00],\n", "       [-1.65700000e-01, -1.01461987e-17, -5.07309937e-18,\n", "         0.00000000e+00,  0.00000000e+00],\n", "       [-0.00000000e+00, -1.65700000e-01, -8.28500000e-02,\n", "         0.00000000e+00,  0.00000000e+00],\n", "       [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00],\n", "       [ 0.00000000e+00, -1.00000000e+00, -1.00000000e+00,\n", "        -1.00000000e+00,  0.00000000e+00],\n", "       [ 1.00000000e+00,  6.12323400e-17,  6.12323400e-17,\n", "         6.12323400e-17,  1.00000000e+00]])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["J=DFbot.jacobe([0, 0, 0, 0, 0])\n", "J"]}, {"cell_type": "code", "execution_count": 24, "id": "ebf9f65c", "metadata": {}, "outputs": [{"data": {"text/latex": ["\\begin{bmatrix}0.14350&-0.05500&-0.05500&-0.05500&0.0\\\\ -0.08285&-0.09526&-0.09526&-0.09526&0.0\\\\ 0.00000&-0.16570&-0.08285&0.0&0.0\\\\ 0.00000&0.86603&0.86603&0.86603&0.00000\\\\ -0.00000&-0.50000&-0.50000&-0.50000&-0.00000\\\\ 1.0&0.00000&0.00000&0.00000&1.0\\\\\\end{bmatrix}"], "text/plain": ["array([[ 1.43500409e-01, -5.50000000e-02, -5.50000000e-02,\n", "        -5.50000000e-02,  0.00000000e+00],\n", "       [-8.28500000e-02, -9.52627944e-02, -9.52627944e-02,\n", "        -9.52627944e-02,  0.00000000e+00],\n", "       [ 3.57554500e-34, -1.65700000e-01, -8.28500000e-02,\n", "         0.00000000e+00,  0.00000000e+00],\n", "       [ 2.12494080e-33,  8.66025404e-01,  8.66025404e-01,\n", "         8.66025404e-01,  2.12494080e-33],\n", "       [-6.35179545e-34, -5.00000000e-01, -5.00000000e-01,\n", "        -5.00000000e-01, -6.35179545e-34],\n", "       [ 1.00000000e+00,  6.12323400e-17,  6.12323400e-17,\n", "         6.12323400e-17,  1.00000000e+00]])"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["J=DFbot.jacob0([np.pi/3, 0, 0, 0, 0])\n", "J"]}, {"cell_type": "code", "execution_count": 25, "id": "8f8728be", "metadata": {}, "outputs": [{"data": {"text/latex": ["\\begin{bmatrix}0.00000&-0.11&-0.11&-0.11&0.0\\\\ -0.16570&-0.00000&-0.00000&0.0&0.0\\\\ 0.00000&-0.16570&-0.08285&0.0&0.0\\\\ 0.0&0.0&0.0&0.0&0.0\\\\ 0.0&-1.0&-1.0&-1.0&0.0\\\\ 1.0&0.00000&0.00000&0.00000&1.0\\\\\\end{bmatrix}"], "text/plain": ["array([[ 1.38777878e-17, -1.10000000e-01, -1.10000000e-01,\n", "        -1.10000000e-01,  0.00000000e+00],\n", "       [-1.65700000e-01, -1.01461987e-17, -5.07309937e-18,\n", "         0.00000000e+00,  0.00000000e+00],\n", "       [ 3.57554500e-34, -1.65700000e-01, -8.28500000e-02,\n", "         0.00000000e+00,  0.00000000e+00],\n", "       [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00],\n", "       [ 0.00000000e+00, -1.00000000e+00, -1.00000000e+00,\n", "        -1.00000000e+00,  0.00000000e+00],\n", "       [ 1.00000000e+00,  6.12323400e-17,  6.12323400e-17,\n", "         6.12323400e-17,  1.00000000e+00]])"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["J=DFbot.jacobe([np.pi/3, 0, 0, 0, 0])\n", "J"]}, {"cell_type": "code", "execution_count": null, "id": "b46e020e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}