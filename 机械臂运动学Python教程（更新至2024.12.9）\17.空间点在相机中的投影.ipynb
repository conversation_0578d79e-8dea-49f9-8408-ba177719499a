{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Central camera projection model"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["%matplotlib notebook"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import math\n", "from spatialmath import SE3\n", "from spatialmath.base import e2h, h2e\n", "from roboticstoolbox import *"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["DFbot = DHRobot(\n", "    [                 \n", "                    RevoluteMDH(d=0.04145,qlim=np.array([-np.pi,np.pi])),            \n", "                    RevoluteMDH(alpha=np.pi/2,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteMDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteMDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteMDH(alpha=-np.pi/2,qlim=np.array([0,np.pi])),\n", "                    RevoluteMDH(a=0.05,d=0.06,qlim=np.array([-np.pi,np.pi]))\n", "                  \n", "    ],\n", "    name=\"DF<PERSON>\",\n", ")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"scrolled": false}, "outputs": [{"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "/* global mpl */\n", "window.mpl = {};\n", "\n", "mpl.get_websocket_type = function () {\n", "    if (typeof WebSocket !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof MozWebSocket !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert(\n", "            'Your browser does not have WebSocket support. ' +\n", "                'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "                'Firefox 4 and 5 are also supported but you ' +\n", "                'have to enable WebSockets in about:config.'\n", "        );\n", "    }\n", "};\n", "\n", "mpl.figure = function (figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = this.ws.binaryType !== undefined;\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById('mpl-warnings');\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent =\n", "                'This browser does not support binary websocket messages. ' +\n", "                'Performance may be slow.';\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = document.createElement('div');\n", "    this.root.setAttribute('style', 'display: inline-block');\n", "    this._root_extra_style(this.root);\n", "\n", "    parent_element.appendChild(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen = function () {\n", "        fig.send_message('supports_binary', { value: fig.supports_binary });\n", "        fig.send_message('send_image_mode', {});\n", "        if (fig.ratio !== 1) {\n", "            fig.send_message('set_device_pixel_ratio', {\n", "                device_pixel_ratio: fig.ratio,\n", "            });\n", "        }\n", "        fig.send_message('refresh', {});\n", "    };\n", "\n", "    this.imageObj.onload = function () {\n", "        if (fig.image_mode === 'full') {\n", "            // Full images could contain transparency (where diff images\n", "            // almost always do), so we need to clear the canvas so that\n", "            // there is no ghosting.\n", "            fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "        }\n", "        fig.context.drawImage(fig.imageObj, 0, 0);\n", "    };\n", "\n", "    this.imageObj.onunload = function () {\n", "        fig.ws.close();\n", "    };\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "};\n", "\n", "mpl.figure.prototype._init_header = function () {\n", "    var titlebar = document.createElement('div');\n", "    titlebar.classList =\n", "        'ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix';\n", "    var titletext = document.createElement('div');\n", "    titletext.classList = 'ui-dialog-title';\n", "    titletext.setAttribute(\n", "        'style',\n", "        'width: 100%; text-align: center; padding: 3px;'\n", "    );\n", "    titlebar.appendChild(titletext);\n", "    this.root.appendChild(titlebar);\n", "    this.header = titletext;\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._root_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._init_canvas = function () {\n", "    var fig = this;\n", "\n", "    var canvas_div = (this.canvas_div = document.createElement('div'));\n", "    canvas_div.setAttribute('tabindex', '0');\n", "    canvas_div.setAttribute(\n", "        'style',\n", "        'border: 1px solid #ddd;' +\n", "            'box-sizing: content-box;' +\n", "            'clear: both;' +\n", "            'min-height: 1px;' +\n", "            'min-width: 1px;' +\n", "            'outline: 0;' +\n", "            'overflow: hidden;' +\n", "            'position: relative;' +\n", "            'resize: both;' +\n", "            'z-index: 2;'\n", "    );\n", "\n", "    function on_keyboard_event_closure(name) {\n", "        return function (event) {\n", "            return fig.key_event(event, name);\n", "        };\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'keydown',\n", "        on_keyboard_event_closure('key_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'keyup',\n", "        on_keyboard_event_closure('key_release')\n", "    );\n", "\n", "    this._canvas_extra_style(canvas_div);\n", "    this.root.appendChild(canvas_div);\n", "\n", "    var canvas = (this.canvas = document.createElement('canvas'));\n", "    canvas.classList.add('mpl-canvas');\n", "    canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'pointer-events: none;' +\n", "            'position: relative;' +\n", "            'z-index: 0;'\n", "    );\n", "\n", "    this.context = canvas.getContext('2d');\n", "\n", "    var backingStore =\n", "        this.context.backingStorePixelRatio ||\n", "        this.context.webkitBackingStorePixelRatio ||\n", "        this.context.mozBackingStorePixelRatio ||\n", "        this.context.msBackingStorePixelRatio ||\n", "        this.context.oBackingStorePixelRatio ||\n", "        this.context.backingStorePixelRatio ||\n", "        1;\n", "\n", "    this.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband_canvas = (this.rubberband_canvas = document.createElement(\n", "        'canvas'\n", "    ));\n", "    rubberband_canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'left: 0;' +\n", "            'pointer-events: none;' +\n", "            'position: absolute;' +\n", "            'top: 0;' +\n", "            'z-index: 1;'\n", "    );\n", "\n", "    // Apply a ponyfill if ResizeObserver is not implemented by browser.\n", "    if (this.ResizeObserver === undefined) {\n", "        if (window.ResizeObserver !== undefined) {\n", "            this.ResizeObserver = window.ResizeObserver;\n", "        } else {\n", "            var obs = _JSXTOOLS_RESIZE_OBSERVER({});\n", "            this.ResizeObserver = obs.ResizeObserver;\n", "        }\n", "    }\n", "\n", "    this.resizeObserverInstance = new this.ResizeObserver(function (entries) {\n", "        var nentries = entries.length;\n", "        for (var i = 0; i < nentries; i++) {\n", "            var entry = entries[i];\n", "            var width, height;\n", "            if (entry.contentBoxSize) {\n", "                if (entry.contentBoxSize instanceof Array) {\n", "                    // Chrome 84 implements new version of spec.\n", "                    width = entry.contentBoxSize[0].inlineSize;\n", "                    height = entry.contentBoxSize[0].blockSize;\n", "                } else {\n", "                    // Firefox implements old version of spec.\n", "                    width = entry.contentBoxSize.inlineSize;\n", "                    height = entry.contentBoxSize.blockSize;\n", "                }\n", "            } else {\n", "                // Chrome <84 implements even older version of spec.\n", "                width = entry.contentRect.width;\n", "                height = entry.contentRect.height;\n", "            }\n", "\n", "            // Keep the size of the canvas and rubber band canvas in sync with\n", "            // the canvas container.\n", "            if (entry.devicePixelContentBoxSize) {\n", "                // Chrome 84 implements new version of spec.\n", "                canvas.setAttribute(\n", "                    'width',\n", "                    entry.devicePixelContentBoxSize[0].inlineSize\n", "                );\n", "                canvas.setAttribute(\n", "                    'height',\n", "                    entry.devicePixelContentBoxSize[0].blockSize\n", "                );\n", "            } else {\n", "                canvas.setAttribute('width', width * fig.ratio);\n", "                canvas.setAttribute('height', height * fig.ratio);\n", "            }\n", "            /* This rescales the canvas back to display pixels, so that it\n", "             * appears correct on HiDPI screens. */\n", "            canvas.style.width = width + 'px';\n", "            canvas.style.height = height + 'px';\n", "\n", "            rubberband_canvas.setAttribute('width', width);\n", "            rubberband_canvas.setAttribute('height', height);\n", "\n", "            // And update the size in Python. We ignore the initial 0/0 size\n", "            // that occurs as the element is placed into the DOM, which should\n", "            // otherwise not happen due to the minimum size styling.\n", "            if (fig.ws.readyState == 1 && width != 0 && height != 0) {\n", "                fig.request_resize(width, height);\n", "            }\n", "        }\n", "    });\n", "    this.resizeObserverInstance.observe(canvas_div);\n", "\n", "    function on_mouse_event_closure(name) {\n", "        /* User Agent sniffing is bad, but WebKit is busted:\n", "         * https://bugs.webkit.org/show_bug.cgi?id=144526\n", "         * https://bugs.webkit.org/show_bug.cgi?id=181818\n", "         * The worst that happens here is that they get an extra browser\n", "         * selection when dragging, if this check fails to catch them.\n", "         */\n", "        var UA = navigator.userAgent;\n", "        var isWebKit = /AppleWebKit/.test(UA) && !/Chrome/.test(UA);\n", "        if(isWebKit) {\n", "            return function (event) {\n", "                /* This prevents the web browser from automatically changing to\n", "                 * the text insertion cursor when the button is pressed. We\n", "                 * want to control all of the cursor setting manually through\n", "                 * the 'cursor' event from matplotlib */\n", "                event.preventDefault()\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        } else {\n", "            return function (event) {\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        }\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'mousedown',\n", "        on_mouse_event_closure('button_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseup',\n", "        on_mouse_event_closure('button_release')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'dblclick',\n", "        on_mouse_event_closure('dblclick')\n", "    );\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    canvas_div.addEventListener(\n", "        'mousemove',\n", "        on_mouse_event_closure('motion_notify')\n", "    );\n", "\n", "    canvas_div.addEventListener(\n", "        'mouseenter',\n", "        on_mouse_event_closure('figure_enter')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseleave',\n", "        on_mouse_event_closure('figure_leave')\n", "    );\n", "\n", "    canvas_div.addEventListener('wheel', function (event) {\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        on_mouse_event_closure('scroll')(event);\n", "    });\n", "\n", "    canvas_div.appendChild(canvas);\n", "    canvas_div.appendChild(rubberband_canvas);\n", "\n", "    this.rubberband_context = rubberband_canvas.getContext('2d');\n", "    this.rubberband_context.strokeStyle = '#000000';\n", "\n", "    this._resize_canvas = function (width, height, forward) {\n", "        if (forward) {\n", "            canvas_div.style.width = width + 'px';\n", "            canvas_div.style.height = height + 'px';\n", "        }\n", "    };\n", "\n", "    // Disable right mouse context menu.\n", "    canvas_div.addEventListener('contextmenu', function (_e) {\n", "        event.preventDefault();\n", "        return false;\n", "    });\n", "\n", "    function set_focus() {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'mpl-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'mpl-button-group';\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'mpl-button-group';\n", "            continue;\n", "        }\n", "\n", "        var button = (fig.buttons[name] = document.createElement('button'));\n", "        button.classList = 'mpl-widget';\n", "        button.setAttribute('role', 'button');\n", "        button.setAttribute('aria-disabled', 'false');\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "\n", "        var icon_img = document.createElement('img');\n", "        icon_img.src = '_images/' + image + '.png';\n", "        icon_img.srcset = '_images/' + image + '_large.png 2x';\n", "        icon_img.alt = tooltip;\n", "        button.appendChild(icon_img);\n", "\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    var fmt_picker = document.createElement('select');\n", "    fmt_picker.classList = 'mpl-widget';\n", "    toolbar.appendChild(fmt_picker);\n", "    this.format_dropdown = fmt_picker;\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = document.createElement('option');\n", "        option.selected = fmt === mpl.default_extension;\n", "        option.innerHTML = fmt;\n", "        fmt_picker.appendChild(option);\n", "    }\n", "\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "};\n", "\n", "mpl.figure.prototype.request_resize = function (x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', { width: x_pixels, height: y_pixels });\n", "};\n", "\n", "mpl.figure.prototype.send_message = function (type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "};\n", "\n", "mpl.figure.prototype.send_draw_message = function () {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({ type: 'draw', figure_id: this.id }));\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "};\n", "\n", "mpl.figure.prototype.handle_resize = function (fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] !== fig.canvas.width || size[1] !== fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1], msg['forward']);\n", "        fig.send_message('refresh', {});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_rubberband = function (fig, msg) {\n", "    var x0 = msg['x0'] / fig.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / fig.ratio;\n", "    var x1 = msg['x1'] / fig.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / fig.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0,\n", "        0,\n", "        fig.canvas.width / fig.ratio,\n", "        fig.canvas.height / fig.ratio\n", "    );\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "};\n", "\n", "mpl.figure.prototype.handle_figure_label = function (fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "};\n", "\n", "mpl.figure.prototype.handle_cursor = function (fig, msg) {\n", "    fig.canvas_div.style.cursor = msg['cursor'];\n", "};\n", "\n", "mpl.figure.prototype.handle_message = function (fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "};\n", "\n", "mpl.figure.prototype.handle_draw = function (fig, _msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "};\n", "\n", "mpl.figure.prototype.handle_image_mode = function (fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "};\n", "\n", "mpl.figure.prototype.handle_history_buttons = function (fig, msg) {\n", "    for (var key in msg) {\n", "        if (!(key in fig.buttons)) {\n", "            continue;\n", "        }\n", "        fig.buttons[key].disabled = !msg[key];\n", "        fig.buttons[key].setAttribute('aria-disabled', !msg[key]);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_navigate_mode = function (fig, msg) {\n", "    if (msg['mode'] === 'PAN') {\n", "        fig.buttons['Pan'].classList.add('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    } else if (msg['mode'] === 'ZOOM') {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.add('active');\n", "    } else {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message('ack', {});\n", "};\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function (fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            var img = evt.data;\n", "            if (img.type !== 'image/png') {\n", "                /* FIXME: We get \"Resource interpreted as Image but\n", "                 * transferred with MIME type text/plain:\" errors on\n", "                 * Chrome.  But how to set the MIME type?  It doesn't seem\n", "                 * to be part of the websocket stream */\n", "                img.type = 'image/png';\n", "            }\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src\n", "                );\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                img\n", "            );\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        } else if (\n", "            typeof evt.data === 'string' &&\n", "            evt.data.slice(0, 21) === 'data:image/png;base64'\n", "        ) {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig['handle_' + msg_type];\n", "        } catch (e) {\n", "            console.log(\n", "                \"No handler for the '\" + msg_type + \"' message type: \",\n", "                msg\n", "            );\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\n", "                    \"Exception inside the 'handler_\" + msg_type + \"' callback:\",\n", "                    e,\n", "                    e.stack,\n", "                    msg\n", "                );\n", "            }\n", "        }\n", "    };\n", "};\n", "\n", "function getModifiers(event) {\n", "    var mods = [];\n", "    if (event.ctrlKey) {\n", "        mods.push('ctrl');\n", "    }\n", "    if (event.altKey) {\n", "        mods.push('alt');\n", "    }\n", "    if (event.shiftKey) {\n", "        mods.push('shift');\n", "    }\n", "    if (event.metaKey) {\n", "        mods.push('meta');\n", "    }\n", "    return mods;\n", "}\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * https://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s(original) {\n", "    return Object.keys(original).reduce(function (obj, key) {\n", "        if (typeof original[key] !== 'object') {\n", "            obj[key] = original[key];\n", "        }\n", "        return obj;\n", "    }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function (event, name) {\n", "    if (name === 'button_press') {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    // from https://stackoverflow.com/q/1114465\n", "    var boundingRect = this.canvas.getBoundingClientRect();\n", "    var x = (event.clientX - boundingRect.left) * this.ratio;\n", "    var y = (event.clientY - boundingRect.top) * this.ratio;\n", "\n", "    this.send_message(name, {\n", "        x: x,\n", "        y: y,\n", "        button: event.button,\n", "        step: event.step,\n", "        modifiers: getModifiers(event),\n", "        guiEvent: simple<PERSON>eys(event),\n", "    });\n", "\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (_event, _name) {\n", "    // Handle any extra behaviour associated with a key event\n", "};\n", "\n", "mpl.figure.prototype.key_event = function (event, name) {\n", "    // Prevent repeat events\n", "    if (name === 'key_press') {\n", "        if (event.key === this._key) {\n", "            return;\n", "        } else {\n", "            this._key = event.key;\n", "        }\n", "    }\n", "    if (name === 'key_release') {\n", "        this._key = null;\n", "    }\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.key !== 'Control') {\n", "        value += 'ctrl+';\n", "    }\n", "    else if (event.altKey && event.key !== 'Alt') {\n", "        value += 'alt+';\n", "    }\n", "    else if (event.shiftKey && event.key !== 'Shift') {\n", "        value += 'shift+';\n", "    }\n", "\n", "    value += 'k' + event.key;\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, { key: value, guiEvent: simpleKeys(event) });\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function (name) {\n", "    if (name === 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message('toolbar_button', { name: name });\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function (tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "\n", "///////////////// REMAINING CONTENT GENERATED BY embed_js.py /////////////////\n", "// prettier-ignore\n", "var _JSXTOOLS_RESIZE_OBSERVER=function(A){var t,i=new WeakMap,n=new WeakMap,a=new WeakMap,r=new WeakMap,o=new Set;function s(e){if(!(this instanceof s))throw new TypeError(\"Constructor requires 'new' operator\");i.set(this,e)}function h(){throw new TypeError(\"Function is not a constructor\")}function c(e,t,i,n){e=0 in arguments?Number(arguments[0]):0,t=1 in arguments?Number(arguments[1]):0,i=2 in arguments?Number(arguments[2]):0,n=3 in arguments?Number(arguments[3]):0,this.right=(this.x=this.left=e)+(this.width=i),this.bottom=(this.y=this.top=t)+(this.height=n),Object.freeze(this)}function d(){t=requestAnimationFrame(d);var s=new WeakMap,p=new Set;o.forEach((function(t){r.get(t).forEach((function(i){var r=t instanceof window.SVGElement,o=a.get(t),d=r?0:parseFloat(o.paddingTop),f=r?0:parseFloat(o.paddingRight),l=r?0:parseFloat(o.paddingBottom),u=r?0:parseFloat(o.paddingLeft),g=r?0:parseFloat(o.borderTopWidth),m=r?0:parseFloat(o.borderRightWidth),w=r?0:parseFloat(o.borderBottomWidth),b=u+f,F=d+l,v=(r?0:parseFloat(o.borderLeftWidth))+m,W=g+w,y=r?0:t.offsetHeight-W-t.clientHeight,E=r?0:t.offsetWidth-v-t.clientWidth,R=b+v,z=F+W,M=r?t.width:parseFloat(o.width)-R-E,O=r?t.height:parseFloat(o.height)-z-y;if(n.has(t)){var k=n.get(t);if(k[0]===M&&k[1]===O)return}n.set(t,[M,O]);var S=Object.create(h.prototype);S.target=t,S.contentRect=new c(u,d,M,O),s.has(i)||(s.set(i,[]),p.add(i)),s.get(i).push(S)}))})),p.forEach((function(e){i.get(e).call(e,s.get(e),e)}))}return s.prototype.observe=function(i){if(i instanceof window.Element){r.has(i)||(r.set(i,new Set),o.add(i),a.set(i,window.getComputedStyle(i)));var n=r.get(i);n.has(this)||n.add(this),cancelAnimationFrame(t),t=requestAnimationFrame(d)}},s.prototype.unobserve=function(i){if(i instanceof window.Element&&r.has(i)){var n=r.get(i);n.has(this)&&(n.delete(this),n.size||(r.delete(i),o.delete(i))),n.size||r.delete(i),o.size||cancelAnimationFrame(t)}},A.DOMRectReadOnly=c,A.ResizeObserver=s,A.ResizeObserverEntry=h,A}; // eslint-disable-line\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Left button pans, Right button zooms\\nx/y fixes axis, CTRL fixes aspect\", \"fa fa-arrows\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\\nx/y fixes axis\", \"fa fa-square-o\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pgf\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\", \"webp\"];\n", "\n", "mpl.default_extension = \"png\";/* global mpl */\n", "\n", "var comm_websocket_adapter = function (comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.binaryType = comm.kernel.ws.binaryType;\n", "    ws.readyState = comm.kernel.ws.readyState;\n", "    function updateReadyState(_event) {\n", "        if (comm.kernel.ws) {\n", "            ws.readyState = comm.kernel.ws.readyState;\n", "        } else {\n", "            ws.readyState = 3; // Closed state.\n", "        }\n", "    }\n", "    comm.kernel.ws.addEventListener('open', updateReadyState);\n", "    comm.kernel.ws.addEventListener('close', updateReadyState);\n", "    comm.kernel.ws.addEventListener('error', updateReadyState);\n", "\n", "    ws.close = function () {\n", "        comm.close();\n", "    };\n", "    ws.send = function (m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function (msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        var data = msg['content']['data'];\n", "        if (data['blob'] !== undefined) {\n", "            data = {\n", "                data: new Blob(msg['buffers'], { type: data['blob'] }),\n", "            };\n", "        }\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(data);\n", "    });\n", "    return ws;\n", "};\n", "\n", "mpl.mpl_figure_comm = function (comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = document.getElementById(id);\n", "    var ws_proxy = comm_websocket_adapter(comm);\n", "\n", "    function ondownload(figure, _format) {\n", "        window.open(figure.canvas.toDataURL());\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy, ondownload, element);\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element;\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error('Failed to find cell for figure', id, fig);\n", "        return;\n", "    }\n", "    fig.cell_info[0].output_area.element.on(\n", "        'cleared',\n", "        { fig: fig },\n", "        fig._remove_fig_handler\n", "    );\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function (fig, msg) {\n", "    var width = fig.canvas.width / fig.ratio;\n", "    fig.cell_info[0].output_area.element.off(\n", "        'cleared',\n", "        fig._remove_fig_handler\n", "    );\n", "    fig.resizeObserverInstance.unobserve(fig.canvas_div);\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable();\n", "    fig.parent_element.innerHTML =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "    fig.close_ws(fig, msg);\n", "};\n", "\n", "mpl.figure.prototype.close_ws = function (fig, msg) {\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "};\n", "\n", "mpl.figure.prototype.push_to_output = function (_remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width / this.ratio;\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message('ack', {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () {\n", "        fig.push_to_output();\n", "    }, 1000);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'btn-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'btn-group';\n", "    var button;\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'btn-group';\n", "            continue;\n", "        }\n", "\n", "        button = fig.buttons[name] = document.createElement('button');\n", "        button.classList = 'btn btn-default';\n", "        button.href = '#';\n", "        button.title = name;\n", "        button.innerHTML = '<i class=\"fa ' + image + ' fa-lg\"></i>';\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message pull-right';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = document.createElement('div');\n", "    buttongrp.classList = 'btn-group inline pull-right';\n", "    button = document.createElement('button');\n", "    button.classList = 'btn btn-mini btn-primary';\n", "    button.href = '#';\n", "    button.title = 'Stop Interaction';\n", "    button.innerHTML = '<i class=\"fa fa-power-off icon-remove icon-large\"></i>';\n", "    button.addEventListener('click', function (_evt) {\n", "        fig.handle_close(fig, {});\n", "    });\n", "    button.addEventListener(\n", "        'mouseover',\n", "        on_mouseover_closure('Stop Interaction')\n", "    );\n", "    buttongrp.appendChild(button);\n", "    var titlebar = this.root.querySelector('.ui-dialog-titlebar');\n", "    titlebar.insertBefore(buttongrp, titlebar.firstChild);\n", "};\n", "\n", "mpl.figure.prototype._remove_fig_handler = function (event) {\n", "    var fig = event.data.fig;\n", "    if (event.target !== this) {\n", "        // Ignore bubbled events from children.\n", "        return;\n", "    }\n", "    fig.close_ws(fig, {});\n", "};\n", "\n", "mpl.figure.prototype._root_extra_style = function (el) {\n", "    el.style.boxSizing = 'content-box'; // override notebook setting of border-box.\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (el) {\n", "    // this is important to make the div 'focusable\n", "    el.setAttribute('tabindex', 0);\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    } else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (event, _name) {\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which === 13) {\n", "        this.canvas_div.blur();\n", "        // select the cell after this one\n", "        var index = IPython.notebook.find_cell_index(this.cell_info[0]);\n", "        IPython.notebook.select(index + 1);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    fig.ondownload(fig, null);\n", "};\n", "\n", "mpl.find_output_cell = function (html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i = 0; i < ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code') {\n", "            for (var j = 0; j < cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] === html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "};\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel !== null) {\n", "    IPython.notebook.kernel.comm_manager.register_target(\n", "        'matplotlib',\n", "        mpl.mpl_figure_comm\n", "    );\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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************************************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\" width=\"640\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["PyPlot3D backend, t = 0.05, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.plot([0,0,0,0,0,0],block=False)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["mtx=np.array([[919.21981864,   0.        , 356.41270451],\n", "       [  0.        , 913.16565134, 236.9305    ],\n", "       [  0.        ,   0.        ,   1.        ]])"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["#投影矩阵"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["P0 = np.array([ [1, 0, 0, 0],\n", "                [0, 1, 0, 0],\n", "                [0, 0, 1, 0]])"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["K=mtx@P0"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["#位姿矩阵"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m-0.866   \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-0.5     \u001b[0m \u001b[38;5;4m-0.2168  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.5     \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-0.866   \u001b[0m \u001b[38;5;4m 0.09734 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["T1=DFbot.fkine([np.pi/2,-np.pi/6,0,np.pi,0,0])\n", "T1"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["PyPlot3D backend, t = 0.05, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.plot([np.pi/2,-np.pi/6,0,np.pi,0,0])"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["P= np.array([0, -0.28, 0])"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0.  ],\n", "       [-0.28],\n", "       [ 0.  ],\n", "       [ 1.  ]])"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["P1=e2h(P)\n", "P1"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[-8.36449319e-17, -8.66025404e-01,  5.00000000e-01,\n", "        -2.36425000e-01],\n", "       [-1.00000000e+00,  3.06161700e-17, -1.14261102e-16,\n", "         4.73612267e-18],\n", "       [ 8.36449319e-17, -5.00000000e-01, -8.66025404e-01,\n", "        -2.41032470e-02],\n", "       [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         1.00000000e+00]])"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["extrinsic = np.linalg.inv(T1)\n", "extrinsic"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["P3d=K@extrinsic@P1"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[46.87948964],\n", "       [27.45947563],\n", "       [ 0.11589675]])"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["P3d"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[404.49355513],\n", "       [236.9305    ],\n", "       [  1.        ]])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["P3d/P3d[-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 2}