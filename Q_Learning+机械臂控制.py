


import time
import random
from roboticstoolbox import *
from spatialmath import *
from math import pi
import numpy as np
from matplotlib import pyplot as plt

DFbot = DHRobot(
    [
        RevoluteDH(d=0.04145, alpha=np.pi / 2, qlim=np.array([-np.pi, np.pi])),
        RevoluteDH(a=-0.08285, qlim=np.array([-np.pi, np.pi])),
        RevoluteDH(a=-0.08285, qlim=np.array([-np.pi, np.pi])),

    ],
    name="DFbot",
)
print("start_time")

DFbot.plot([0,0,0],block=True)
# DFbot.plot([np.pi/3,np.pi/3,-np.pi/3*2],block=True)
# DFbot.fkine([np.pi/3,np.pi/3,-np.pi/3*2])

class Env():
    def __init__(self,num_deg,target,robot,unit):
        self.num_deg=num_deg           #锟截斤拷锟斤拷锟斤拷
        self.d = tuple([0]*self.num_deg) #锟斤拷始锟斤拷锟角度ｏ拷锟斤拷锟捷筹拷始锟斤拷锟侥关斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟矫硷拷锟斤拷锟角度筹拷始锟斤拷
        self.target=target
        self.robot=robot#锟斤拷锟剿讹拷学锟斤拷式锟斤拷装锟斤拷去
        self.unit=unit#锟斤拷位转锟斤拷锟斤拷锟斤拷
        
        
    def step(self, action):
        """6锟斤拷锟斤拷执锟斤拷action锟斤拷3锟斤拷锟斤拷锟缴度ｏ拷每锟斤拷锟斤拷锟缴讹拷锟斤拷转1锟斤拷锟斤拷位锟斤拷转1锟斤拷锟斤拷位"""
        change = [[0, 1], [0, -1], [1, 1],[1, -1],[2, 1],[2, -1]]#/6锟斤拷锟斤拷执锟斤拷action锟斤拷0锟截斤拷, 1锟斤拷锟斤拷转锟斤拷锟斤拷锟斤拷说每一锟斤拷锟斤拷锟斤拷锟竭伙拷锟角凤拷转锟斤拷
        index=change[action][0] #锟斤拷取锟斤拷锟狡关斤拷
        move=change[action][1]  #锟斤拷取锟斤拷锟斤拷转锟斤拷锟斤拷锟斤拷说每一锟斤拷锟斤拷锟斤拷锟竭伙拷锟角凤拷转
        dtemp=list(self.d)      #转锟斤拷为锟叫憋拷
        dtemp[index] = dtemp[index] + move #锟斤拷取锟斤拷锟斤拷
        self.d=tuple(dtemp)               #转锟斤拷为元锟介，锟芥储锟斤拷去
        pose=self.robot.fkine([x*self.unit for x in self.d])#通锟斤拷取锟斤拷锟斤拷转锟斤拷为锟窖匡拷锟斤拷锟斤拷锟斤拷

        states = self.d
        reward = -1 #每锟斤拷一锟斤拷锟斤拷锟斤拷锟斤拷1锟斤拷
        terminal = False   #锟秸硷拷目锟斤拷未锟斤到
        distance= sum([x*x for x in np.array(pose)[:,-1][0:3]-np.array(self.target)])
        ##print(pose)
        if distance<=0.005:
            terminal = True
            reward=100
        check_boundary=[((self.d[i]*self.unit>=self.robot.qlim[0][i]) and (self.d[i]*self.unit<=self.robot.qlim[1][i])) for i in range(len(self.d)) ]            
        #print(check_boundary)
        if not (all(check_boundary)):
            reward = -1000
            terminal = True
            
        return reward, states, terminal

    def reset(self):
        self.d = tuple([0]*self.num_deg)



class Q_table():
    def __init__(self, steps, actions, alpha, gamma,unit):
        self.d=tuple([0]*3)
        self.steps=steps             
        self.actions = actions      
        self.table = dict() # initialize all Q(d,a) dictionary
        self.alpha = alpha  #学习锟斤拷
        self.gamma = gamma  #锟桔匡拷锟斤拷锟斤拷
        self.unit=unit      #锟斤拷位转锟斤拷锟斤拷锟斤拷
        
    def _init_table(self):         #锟斤拷始锟斤拷q锟斤拷
        steps=self.steps           #锟斤拷锟斤拷始锟斤拷锟斤拷锟斤拷锟斤拷值锟斤拷steps
        d0=np.mgrid[-steps:(steps+1):1]  #d1锟截节ｏ拷锟斤拷锟斤拷-steps锟斤拷steps锟侥诧拷锟斤拷
        d1=np.mgrid[-steps:(steps+1):1]  #d2锟截斤拷锟斤拷锟斤拷-steps锟斤拷steps锟侥诧拷锟斤拷
        d2=np.mgrid[-steps:(steps+1):1]  #锟斤拷锟斤拷-steps锟斤拷steps锟侥诧拷锟斤拷
        for i1 in d0.flatten():          # 锟截斤拷0锟斤拷锟斤拷锟叫匡拷锟斤拷位锟矫ｏ拷flatten锟斤拷为锟斤拷确锟斤拷d为一维锟斤拷锟介）
            for i2 in d1.flatten():      #锟截斤拷1锟斤拷锟斤拷锟叫匡拷锟斤拷位锟斤拷
                for i3 in d2.flatten():  #锟截斤拷2锟斤拷锟斤拷锟叫匡拷锟斤拷位锟斤拷
                    for i4 in range(self.actions):       #锟斤拷锟叫匡拷锟杰的讹拷锟斤拷
                        self.table[tuple([i1,i2,i3,i4])]=0      #锟斤拷锟斤拷q锟斤拷

    def _epsilon(self,num_episode):
        #return 0.1
        # version for better convergence:
        # """At the beginning epsilon is 0.2, after 300 episodes decades to 0.05, and eventually go to 0."""
        return 20. / (num_episode + 100) 

    
    def take_action(self, s, num_episode):
        """epsilon-greedy action selection"""
        if random.random() < self._epsilon(num_episode): 
            return int(random.random() * 6)    #锟劫诧拷锟斤拷时锟斤拷去锟斤拷取锟斤拷锟矫的斤拷
        else:
            actions_value = [self.table[(s[0],s[1],s[2],a)] for a in range(self.actions)]
            return actions_value.index(max(actions_value))              #锟斤拷知锟斤拷锟斤拷啥
    
   
    def excute_policy(self,s):
            actions_value = [self.table[(s[0],s[1],s[2],a)] for a in range(self.actions)]
            return actions_value.index(max(actions_value))
            
    #锟斤拷状态s锟斤拷锟揭碉拷锟斤拷一锟斤拷锟斤拷锟侥硷拷值锟斤拷锟斤拷值
    def max_q(self, s):
        actions_value = [self.table[(s[0],s[1],s[2],a)] for a in range(self.actions)]
        return max(actions_value)
    
    #锟斤拷锟斤拷Q-table
    def update(self, a, s0, s1, r, is_terminated):
        q_current = self.table[(s0[0],s0[1],s0[2],a)]
    
        if not is_terminated:
            q_target = r + self.gamma * self.max_q(s1) #gamma 0只锟斤拷锟侥硷拷时锟斤拷锟斤拷锟斤拷1未锟斤拷锟侥斤拷锟斤拷锟酵硷拷时锟侥斤拷锟斤拷同锟斤拷锟斤拷要锟斤拷0锟斤拷9锟斤拷锟斤拷未锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟桔匡拷
      
        else:
            q_target = r
        self.table[(s0[0],s0[1],s0[2],a)] += self.alpha * (q_target - q_current) # alpha 0锟斤拷锟角诧拷学习 1锟斤拷锟斤拷锟斤拷全锟斤拷锟斤拷锟铰的撅拷锟斤拷 0.1平锟斤拷锟斤拷史锟斤拷锟斤拷


def find_path_to_target(start_state, target_position):

    s0 = list(start_state)
    is_terminated = False
    path = []
    
    while not is_terminated:
        action = table.excute_policy(s0)
        r, s1, is_terminated = env.step(action)
        
        # 记录路径
        joint_angles = [x * env.unit for x in s1]  # 转换为实际角度
        path.append({
            'step': len(path),
            'action': action,
            'joint_angles_rad': joint_angles,
            'joint_angles_deg': np.degrees(joint_angles),
            'reward': r
        })
        
        s0 = s1
        
        # 防止无限循环
        if len(path) > 20:
            break
    
    return path

# # 使用示例
# path = find_path_to_target([0, 0, 0], [-0.04, -0.07, 0.04])
# for step in path:
#     print(f"步骤{step['step']}: 动作{step['action']}, "
#           f"关节角度: {step['joint_angles_deg']:.1f}°")
    
env = Env(3,[-0.04,-0.07,0.04],DFbot,np.pi/6)   #锟斤拷始锟斤拷
table = Q_table(8, 6, 0.1,0.9,np.pi/6)        
table._init_table()

table.table


for num_episode in range(500):
        episodic_reward = 0
        is_terminated = False
        s0 = [0, 0,0]
        while not is_terminated:
            # within one episode
            action = table.take_action(s0, num_episode)
            r, s1, is_terminated = env.step(action)  #r锟斤拷锟斤拷锟斤拷值锟斤拷s1锟斤拷锟斤拷锟斤拷状态锟斤拷is_terminated锟斤拷锟斤拷锟角凤拷锟斤拷止
            ##print(r, s1, action,is_terminated)
            table.update(action, s0, s1, r, is_terminated)
            episodic_reward += r
            print
            # env.render(frames=100)
            s0 = s1
        print("Episode: {}, Score: {}".format(num_episode, episodic_reward))
        env.reset()


table.table

print("\n===start test===")
s0 = [0, 0, 0]
is_terminated = False 

while not is_terminated:
    action = table.excute_policy(s0)
    print(f"select action: {action}")
    r, s1, is_terminated = env.step(action)
    print(f"status: {s0} -> {s1},reward: {r},stop: {is_terminated}")
    episodic_reward += r  
    s0 = s1

print("last score: {}".format(episodic_reward))
env.reset()



T2=DFbot.fkine([np.pi/3,-np.pi/3,np.pi/3*2])
print(T2)
target=[-0.04,-0.07,0.04]
sum([x*x for x in np.array(T2)[:,-1][0:3]-np.array(target) ])