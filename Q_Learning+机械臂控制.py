


import time
import random
from roboticstoolbox import *
from spatialmath import *
from math import pi
import numpy as np
from matplotlib import pyplot as plt

DFbot = DHRobot(
    [
        RevoluteDH(d=0.04145, alpha=np.pi / 2, qlim=np.array([-np.pi, np.pi])),
        RevoluteDH(a=-0.08285, qlim=np.array([-np.pi, np.pi])),
        RevoluteDH(a=-0.08285, qlim=np.array([-np.pi, np.pi])),

    ],
    name="DFbot",
)
print(1111)

DFbot.plot([0,0,0],block=True)
# DFbot.plot([np.pi/3,np.pi/3,-np.pi/3*2],block=True)
# DFbot.fkine([np.pi/3,np.pi/3,-np.pi/3*2])

class Env():
    def __init__(self,num_deg,target,robot,unit):
        self.num_deg=num_deg           #锟截斤拷锟斤拷锟斤拷
        self.d = tuple([0]*self.num_deg) #锟斤拷始锟斤拷锟角度ｏ拷锟斤拷锟捷筹拷始锟斤拷锟侥关斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟矫硷拷锟斤拷锟角度筹拷始锟斤拷
        self.target=target#锟斤拷锟斤拷锟斤拷目锟斤拷锟�
        self.robot=robot#锟斤拷锟剿讹拷学锟斤拷式锟斤拷装锟斤拷去
        self.unit=unit#锟斤拷位转锟斤拷锟斤拷锟斤拷
        
        
    def step(self, action):
        """6锟斤拷锟斤拷执锟斤拷action锟斤拷3锟斤拷锟斤拷锟缴度ｏ拷每锟斤拷锟斤拷锟缴讹拷锟斤拷转1锟斤拷锟斤拷位锟斤拷转1锟斤拷锟斤拷位"""
        change = [[0, 1], [0, -1], [1, 1],[1, -1],[2, 1],[2, -1]]#/6锟斤拷锟斤拷执锟斤拷action锟斤拷0锟截斤拷, 1锟斤拷锟斤拷转锟斤拷锟斤拷锟斤拷说每一锟斤拷锟斤拷锟斤拷锟竭伙拷锟角凤拷转锟斤拷
        index=change[action][0] #锟斤拷取锟斤拷锟狡关斤拷
        move=change[action][1]  #锟斤拷取锟斤拷锟斤拷转锟斤拷锟斤拷锟斤拷说每一锟斤拷锟斤拷锟斤拷锟竭伙拷锟角凤拷转
        dtemp=list(self.d)      #转锟斤拷为锟叫憋拷
        dtemp[index] = dtemp[index] + move #锟斤拷取锟斤拷锟斤拷
        self.d=tuple(dtemp)               #转锟斤拷为元锟介，锟芥储锟斤拷去
        pose=self.robot.fkine([x*self.unit for x in self.d])#通锟斤拷取锟斤拷锟斤拷转锟斤拷为锟窖匡拷锟斤拷锟斤拷锟斤拷

        states = self.d
        reward = -1 #每锟斤拷一锟斤拷锟斤拷锟斤拷锟斤拷1锟斤拷
        terminal = False   #锟秸硷拷目锟斤拷未锟斤到
        distance= sum([x*x for x in np.array(pose)[:,-1][0:3]-np.array(self.target)])#取锟斤拷锟斤拷锟斤拷锟斤拷辖堑锟斤拷锟斤拷锟斤拷锟斤拷辏拷锟饺∥伙拷锟斤拷锟较拷锟斤拷锟斤拷锟斤拷锟饺★拷锟侥匡拷锟轿伙拷玫木锟斤拷锟�
        ##print(pose)
        if distance<=0.005:#锟叫讹拷锟角凤拷咏锟侥匡拷锟轿伙拷茫锟�
            terminal = True
            reward=100
        check_boundary=[((self.d[i]*self.unit>=self.robot.qlim[0][i]) and (self.d[i]*self.unit<=self.robot.qlim[1][i])) for i in range(len(self.d)) ]            
        #print(check_boundary)
        if not (all(check_boundary)):#锟斤拷锟斤拷欠癯锟斤拷锟斤拷平嵌锟�
            reward = -1000
            terminal = True
            
        return reward, states, terminal

    def reset(self):
        self.d = tuple([0]*self.num_deg)



class Q_table():
    def __init__(self, steps, actions, alpha, gamma,unit):
        self.d=tuple([0]*3)
        self.steps=5                 #每锟斤拷锟截斤拷锟斤拷锟斤拷锟竭碉拷锟斤拷嗖斤拷锟�
        self.actions = actions       #锟斤拷执锟叫的讹拷锟斤拷锟斤拷锟斤拷3锟斤拷锟截斤拷*2锟斤拷锟斤拷锟斤拷
        self.table = dict() # initialize all Q(d,a) dictionary
        self.alpha = alpha  #学习锟斤拷
        self.gamma = gamma  #锟桔匡拷锟斤拷锟斤拷
        self.unit=unit      #锟斤拷位转锟斤拷锟斤拷锟斤拷
        
    def _init_table(self):         #锟斤拷始锟斤拷q锟斤拷
        steps=self.steps           #锟斤拷锟斤拷始锟斤拷锟斤拷锟斤拷锟斤拷值锟斤拷steps
        d0=np.mgrid[-steps:(steps+1):1]  #d1锟截节ｏ拷锟斤拷锟斤拷-steps锟斤拷steps锟侥诧拷锟斤拷
        d1=np.mgrid[-steps:(steps+1):1]  #d2锟截斤拷锟斤拷锟斤拷-steps锟斤拷steps锟侥诧拷锟斤拷
        d2=np.mgrid[-steps:(steps+1):1]  #锟斤拷锟斤拷-steps锟斤拷steps锟侥诧拷锟斤拷
        for i1 in d0.flatten():          # 锟截斤拷0锟斤拷锟斤拷锟叫匡拷锟斤拷位锟矫ｏ拷flatten锟斤拷为锟斤拷确锟斤拷d为一维锟斤拷锟介）
            for i2 in d1.flatten():      #锟截斤拷1锟斤拷锟斤拷锟叫匡拷锟斤拷位锟斤拷
                for i3 in d2.flatten():  #锟截斤拷2锟斤拷锟斤拷锟叫匡拷锟斤拷位锟斤拷
                    for i4 in range(self.actions):       #锟斤拷锟叫匡拷锟杰的讹拷锟斤拷
                        self.table[tuple([i1,i2,i3,i4])]=0      #锟斤拷锟斤拷q锟斤拷

    def _epsilon(self,num_episode):
        #return 0.1
        # version for better convergence:
        # """At the beginning epsilon is 0.2, after 300 episodes decades to 0.05, and eventually go to 0."""
        return 20. / (num_episode + 100) #实锟街革拷锟矫的伙拷锟秸猴拷锟斤拷锟斤拷锟斤拷为越锟接斤拷锟斤拷锟斤拷系锟斤拷盏悖拷锟斤拷锟街翟叫�

    #锟斤拷状态s锟铰诧拷锟斤拷epsi-greedy锟斤拷锟斤拷选锟斤拷一锟斤拷锟斤拷锟斤拷action锟斤拷锟斤拷锟斤拷锟斤拷裕锟�
    def take_action(self, s, num_episode):
        """epsilon-greedy action selection"""
        if random.random() < self._epsilon(num_episode): #锟斤拷锟斤拷一锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷小锟斤拷锟斤拷锟街碉拷锟斤拷锟绞继斤拷锟�
            return int(random.random() * 6)    #锟劫诧拷锟斤拷时锟斤拷去锟斤拷取锟斤拷锟矫的斤拷
        else:
            actions_value = [self.table[(s[0],s[1],s[2],a)] for a in range(self.actions)]
            return actions_value.index(max(actions_value))              #锟斤拷知锟斤拷锟斤拷啥
    
    #锟斤拷状态s锟铰凤拷锟斤拷选取锟斤拷值锟斤拷锟斤拷action锟斤拷没锟斤拷锟斤拷锟斤拷裕锟�
    def excute_policy(self,s):
            actions_value = [self.table[(s[0],s[1],s[2],a)] for a in range(self.actions)]
            return actions_value.index(max(actions_value))
            
    #锟斤拷状态s锟斤拷锟揭碉拷锟斤拷一锟斤拷锟斤拷锟侥硷拷值锟斤拷锟斤拷值
    def max_q(self, s):
        actions_value = [self.table[(s[0],s[1],s[2],a)] for a in range(self.actions)]
        return max(actions_value)
    
    #锟斤拷锟斤拷Q-table
    def update(self, a, s0, s1, r, is_terminated):
        q_current = self.table[(s0[0],s0[1],s0[2],a)]
        #锟斤拷锟矫伙拷锟斤拷锟街癸拷锟斤拷透锟斤拷锟�
        if not is_terminated:
            q_target = r + self.gamma * self.max_q(s1) #gamma 0只锟斤拷锟侥硷拷时锟斤拷锟斤拷锟斤拷1未锟斤拷锟侥斤拷锟斤拷锟酵硷拷时锟侥斤拷锟斤拷同锟斤拷锟斤拷要锟斤拷0锟斤拷9锟斤拷锟斤拷未锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟桔匡拷
        #锟斤拷锟斤拷锟街癸拷耍锟斤拷锟斤拷锟街滴�0
        else:
            q_target = r
        self.table[(s0[0],s0[1],s0[2],a)] += self.alpha * (q_target - q_current) # alpha 0锟斤拷锟角诧拷学习 1锟斤拷锟斤拷锟斤拷全锟斤拷锟斤拷锟铰的撅拷锟斤拷 0.1平锟斤拷锟斤拷史锟斤拷锟斤拷



env = Env(3,[-0.04,-0.07,0.04],DFbot,np.pi/6)   #锟斤拷始锟斤拷
table = Q_table(6, 6, 0.1,0.9,np.pi/6)         #锟斤拷始锟斤拷q锟斤拷锟睫改诧拷锟斤拷锟斤拷锟斤拷锟斤拷每锟斤拷锟侥凤拷围
table._init_table()

table.table


for num_episode in range(500):
        episodic_reward = 0
        is_terminated = False
        s0 = [0, 0,0]
        while not is_terminated:
            # within one episode
            action = table.take_action(s0, num_episode)
            r, s1, is_terminated = env.step(action)  #r锟斤拷锟斤拷锟斤拷值锟斤拷s1锟斤拷锟斤拷锟斤拷状态锟斤拷is_terminated锟斤拷锟斤拷锟角凤拷锟斤拷止
            ##print(r, s1, action,is_terminated)
            table.update(action, s0, s1, r, is_terminated)
            episodic_reward += r
            print
            # env.render(frames=100)
            s0 = s1
            print(11111)
        print("Episode: {}, Score: {}".format(num_episode, episodic_reward))
        env.reset()


table.table