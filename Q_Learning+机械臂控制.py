import time
import random
from roboticstoolbox import *
from spatialmath import *
from math import pi
import numpy as np
from matplotlib import pyplot as plt

DFbot = DHRobot(
    [
        RevoluteDH(d=0.04145, alpha=np.pi / 2, qlim=np.array([-np.pi, np.pi])),
        RevoluteDH(a=-0.08285, qlim=np.array([-np.pi, np.pi])),
        RevoluteDH(a=-0.08285, qlim=np.array([-np.pi, np.pi])),

    ],
    name="DFbot",
)


DFbot.plot([0,0,0],block=True)
# DFbot.plot([np.pi/3,np.pi/3,-np.pi/3*2],block=True)
# DFbot.fkine([np.pi/3,np.pi/3,-np.pi/3*2])

class Env():
    def __init__(self, num_deg, target, robot, unit):
        self.num_deg = num_deg
        self.d = tuple([0] * self.num_deg)
        self.target = target
        self.robot = robot
        self.unit = unit

    def step(self, action):
        """6个可执行action，3个自由度，每个自由度正转1个单位或反转1个单位"""
        change = [[0, 1], [0, -1], [1, 1], [1, -1], [2, 1], [2, -1]]
        index = change[action][0]
        move = change[action][1]
        dtemp = list(self.d)
        dtemp[index] = dtemp[index] + move
        self.d = tuple(dtemp)
        pose = self.robot.fkine([x * self.unit for x in self.d])

        states = self.d
        reward = -1
        terminal = False
        distance = sum([x * x for x in np.array(pose)[:, -1][0:3] - np.array(self.target)])
        ##print(pose)
        if distance <= 0.005:
            terminal = True
            reward = 100
        check_boundary = [
            ((self.d[i] * self.unit >= self.robot.qlim[0][i]) and (self.d[i] * self.unit <= self.robot.qlim[1][i])) for
            i in range(len(self.d))]
        # print(check_boundary)
        if not (all(check_boundary)):
            reward = -1000
            terminal = True

        return reward, states, terminal

    def reset(self):
        self.d = tuple([0] * self.num_deg)
