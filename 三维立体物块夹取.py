import numpy as np
import cv2

ball_color = 'green'



color_dist = {'red': {'Lower': np.array([150, 43, 46]), 'Upper': np.array([180, 255, 255])},
'blue': {'Lower': np.array([100, 100, 100]), 'Upper': np.array([124, 255, 255])},
'green': {'Lower': np.array([35, 43, 35]), 'Upper': np.array([90, 255, 255])},
}

cap = cv2.VideoCapture(0)
cv2.namedWindow('camera', cv2.WINDOW_AUTOSIZE)


while cap.isOpened():
    ret, frame = cap.read()
    if ret:
        if frame is not None:
            gs_frame = cv2.G<PERSON>sianBlur(frame, (5, 5), 0) # 高斯模糊
            hsv = cv2.cvtColor(gs_frame, cv2.COLOR_BGR2HSV) # 转化成HSV图像
            erode_hsv = cv2.erode(hsv, None, iterations=2) # 腐蚀
            inRange_hsv = cv2.inRange(erode_hsv, color_dist[ball_color]['Lower'], color_dist[ball_color]['Upper'])
            cnts = cv2.findContours(inRange_hsv.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[-2]
            try:
                c = max(cnts, key=cv2.contourArea)
                rect = cv2.minAreaRect(c)
                box = cv2.boxPoints(rect)
                cv2.drawContours(frame, [np.int0(box)], -1, (0, 255, 255), 2)
                cv2.putText(frame, str(np.int0(box[0][0]))+','+str(np.int0(box[0][1])),(np.int0(box[0][0]),np.int0(box[0][1])),cv2.FONT_HERSHEY_COMPLEX_SMALL,0.75,(0,0,255),1)
                cv2.putText(frame, str(np.int0(box[1][0]))+','+str(np.int0(box[1][1])),(np.int0(box[1][0]),np.int0(box[1][1])),cv2.FONT_HERSHEY_COMPLEX_SMALL,0.75,(0,0,255),1)
                cv2.putText(frame, str(np.int0(box[2][0]))+','+str(np.int0(box[2][1])),(np.int0(box[2][0]),np.int0(box[2][1])),cv2.FONT_HERSHEY_COMPLEX_SMALL,0.75,(0,0,255),1)
                cv2.putText(frame, str(np.int0(box[3][0]))+','+str(np.int0(box[3][1])),(np.int0(box[3][0]),np.int0(box[3][1])),cv2.FONT_HERSHEY_COMPLEX_SMALL,0.75,(0,0,255),1)
                cv2.putText(frame, str(np.int0(box[3][0]/2+box[1][0]/2))+','+str(np.int0(box[3][1]/2+box[1][1]/2)),(np.int0(box[3][0]/2+box[1][0]/2),np.int0(box[3][1]/2+box[1][1]/2)),cv2.FONT_HERSHEY_COMPLEX_SMALL,0.75,(0,0,255),1)
                cv2.imshow('camera', frame)
                cv2.waitKey(1)
            except:
                cv2.imshow('camera', frame)
                cv2.waitKey(1)
                pass
        else:
            print("无画面")
    else:
        print("无法读取摄像头！")

cap.release()
cv2.waitKey(0)
cv2.destroyAllWindows()