from roboticstoolbox import *
from roboticstoolbox import RevoluteDH, DHRobot
import roboticstoolbox as rtb
from spatialmath import *
import numpy as np
from math import pi
import serial1
from serial1 import SerialComm
import serial
# DFbot = DHRobot(
#     [
#         #只写参数不为零的
#         RevoluteDH(d=0.065,alpha=np.pi/2,qlim=np.array([-np.pi*2/3,np.pi*2/3])),
#         RevoluteDH(a=-0.1,qlim=np.array([-np.pi/2,np.pi/2]),offset=-np.pi/2),
#         RevoluteDH(a=-0.095,qlim=np.array([-np.pi*2/3,np.pi*2/3])),
#         RevoluteDH(alpha=-np.pi/2,qlim=np.array([-np.pi/2,np.pi/2]),offset=np.pi/2),
#         RevoluteDH(d=0.1,qlim=np.array([-np.pi/2,np.pi/2])),
#
#     ],
#     name = "DFbot",
# )
# print(DFbot)
#
# DFbot.plot([0,0,0,0,0])
#
# DFbot.plot([0,-np.pi/10,-np.pi/10,-np.pi/10,0])
# import matplotlib.pyplot as plt
# plt.show(block=True) # 如果Robotics Toolbox使用Matplotlib作为绘图后端


# DFbot = DHRobot(
#     [
#         RevoluteMDH(d=0.04145,qlim=np.array([-np.pi,np.pi])),
#         RevoluteMDH(alpha=np.pi/2,qlim=np.array([-np.pi,np.pi])),
#         RevoluteMDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),
#         RevoluteMDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),
#         RevoluteMDH(alpha=-np.pi/2,d=0.075,qlim=np.array([-np.pi,np.pi]))
#     ],
#     name="DFbot",
# )
# DFbot.plot([0,0,0,0,0],block=False)
# import matplotlib.pyplot as plt
# plt.show(block=True) # 如果Robotics Toolbox使用Matplotlib作为绘图后端

DFbot = rtb.DHRobot(
    [
#         #只写参数不为零的
        RevoluteDH(d=0.040,alpha=np.pi/2,qlim=np.array([-np.pi*2/3,np.pi*2/3])),
        RevoluteDH(a=-0.1,qlim=np.array([-np.pi,0]),offset=-np.pi/2),
        RevoluteDH(a=-0.095,qlim=np.array([-np.pi*2/3,np.pi*2/3])),
        RevoluteDH(alpha=-np.pi/2,qlim=np.array([0,np.pi]),offset=np.pi/2),
        RevoluteDH(d=0.1,qlim=np.array([-np.pi/2,np.pi/2])),

     ],
    name = "DFbot",
)
# #正运动学
# DFbot.plot([0,-np.pi/6,0,np.pi/6,0])
# #可以得到一个矩阵
# T=DFbot.fkine([0,-np.pi/6,0,np.pi/6,0])
state0 = [0,0,0,0,0]
T0=DFbot.fkine(state0)
state1=[0,np.pi/6,0,np.pi/2,0]
T1 = DFbot.fkine(state1)
print(T1)


T2=np.array(T1)
T2[0,-1]=-0.25

T2[2,-1]=0.1

sol = DFbot.ikine_LM(T2,q0=[0,0,0,0,0],ilimit=100, slimit=100,joint_limits=True)

DFbot.plot(sol.q)
import matplotlib.pyplot as plt
plt.show(block=True) # 如果Robotics Toolbox使用Matplotlib作为绘图后端

state2 = sol.q
print(state2)
deg2 = [x*180/np.pi for x in state2]
print(deg2)




# 创建SerialComm对象
serial_comm = SerialComm(port='COM13', baudrate=115200)

# 示例数据
data_to_send = [10, deg2[4], deg2[3], deg2[2], deg2[1], deg2[0]]
for i in range(len(data_to_send)):
    data_to_send[i] = int(data_to_send[i])
    if data_to_send[i] > 0:
        # 如果是负数，我们将其转换为字节表示，并设置最高位为1
        # 注意：这里的处理方式取决于你想要的结果格式。
        # 对于这个例子，我们将数值限制在8位范围内进行演示。
        if data_to_send[i] >= -128 and data_to_send[i] <= 127:
            # 将数值限制在8位有符号整数范围内
            data_to_send[i] = data_to_send[i] & 0xFF | 0x80
        else:
            print(f"警告: 数值 {data_to_send[i]} 超出了8位有符号整数范围.")
    if data_to_send[i] < 0:
        data_to_send[i] = -data_to_send[i]

# 发送数据
serial_comm.send_data(data_to_send)

# 关闭串口连接
serial_comm.close()