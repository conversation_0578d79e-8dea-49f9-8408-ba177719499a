# import socket
# socket_obj=socket.socket(socket.AF_INET,socket.SOCK_STREAM)
#
#
# socket_obj.listen(5)
#
# client_socket,client_addr=socket_obj.accept()
# info=socket_obj.recv(1024).decode('utf-8')
# while info!='bye':
#     if info!='':
#         print('shujvwei',info)
#
#     data=input('请输入要发送的数据：')
#
#     socket_obj.send(data.encode('utf-8'))
#     if data=='bye':
#         break
#     info=socket_obj.recv(1024).decode('utf-8')
#
# client_socket.close()
# socket_obj.close()