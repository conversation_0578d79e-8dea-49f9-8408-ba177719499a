import numpy as np
import math
from spatialmath import SE3
from spatialmath.base import e2h, h2e
from roboticstoolbox import *

DFbot = DHRobot(
    [
        RevoluteMDH(d=0.065, qlim=np.array([-np.pi*2/3, np.pi*2/3])),
        RevoluteMDH(alpha=np.pi / 2, qlim=np.array([-np.pi/2, np.pi/2]),offset=-np.pi/2),
        RevoluteMDH(a=-0.1, qlim=np.array([-np.pi*2/3, np.pi*2/3])),
        RevoluteMDH(a=-0.095, qlim=np.array([-np.pi/2, np.pi/2]),offset=np.pi/2),
        RevoluteMDH(d=0.04,alpha=-np.pi/2, qlim=np.array([-np.pi/2, np.pi/2])),
        RevoluteMDH(a=0.05, qlim=np.array([-np.pi, np.pi]))

    ],
    name="<PERSON><PERSON>bot",
)


mtx=np.array([[641.43945303     ,    0.         , 307.9468488 ],
              [  0.             ,   663.28643594,231.53404289 ],
              [  0.             ,         0.    ,    1.      ]])


P0 = np.array([ [1, 0, 0, 0],
                [0, 1, 0, 0],
                [0, 0, 1, 0]])


K=mtx@P0

T1=DFbot.fkine([np.pi/2,-np.pi/6,0,np.pi,0,0])
DFbot.plot([0,np.pi/3,0,np.pi/2,0,0],block=True)


P= np.array([0, -0.28, 0])

P1=e2h(P)
extrinsic = np.linalg.inv(T1)#外参矩阵，相机位姿矩阵的逆矩阵

P3d=K@extrinsic@P1
P3d = P3d/P3d[-1]
print(P3d)