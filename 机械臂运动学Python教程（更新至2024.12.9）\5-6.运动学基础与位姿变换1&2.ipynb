{"cells": [{"cell_type": "markdown", "id": "3300ed98", "metadata": {}, "source": ["##位姿变换"]}, {"cell_type": "code", "execution_count": 8, "id": "e1e74537", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using matplotlib backend: MacOSX\n"]}], "source": ["import roboticstoolbox as rtb\n", "from spatialmath import *\n", "import numpy as np\n", "%matplotlib auto"]}, {"cell_type": "markdown", "id": "72a6bee8", "metadata": {}, "source": ["SE 平移旋转一起"]}, {"cell_type": "code", "execution_count": 2, "id": "986c160c", "metadata": {}, "outputs": [], "source": ["T=SE2(1, 2, np.pi/3)"]}, {"cell_type": "code", "execution_count": 3, "id": "bbf519d0", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 0.5     \u001b[0m \u001b[38;5;1m-0.866   \u001b[0m \u001b[38;5;4m 1       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.866   \u001b[0m \u001b[38;5;1m 0.5     \u001b[0m \u001b[38;5;4m 2       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["T"]}, {"cell_type": "code", "execution_count": 4, "id": "a08d187f", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on class SE2 in module spatialmath.pose2d:\n", "\n", "class SE2(SO2)\n", " |  SE2(x=None, y=None, theta=None, *, unit='rad', check=True)\n", " |  \n", " |     SE(2) matrix class\n", " |  \n", " |     This subclass represents rigid-body motion (pose) in 2D space.  Internally\n", " |     it is a 3x3 homogeneous transformation matrix belonging to the group SE(2).\n", " |  \n", " |  .. inheritance-diagram:: spatialmath.pose2d.SE2\n", " |     :top-classes: collections.UserList\n", " |     :parts: 1\n", " |  \n", " |  Method resolution order:\n", " |      SE2\n", " |      SO2\n", " |      spatialmath.baseposematrix.BasePoseMatrix\n", " |      spatialmath.baseposelist.BasePoseList\n", " |      collections.UserList\n", " |      collections.abc.MutableSequence\n", " |      collections.abc.Sequence\n", " |      collections.abc.Reversible\n", " |      collections.abc.Collection\n", " |      collections.abc.Sized\n", " |      collections.abc.Iterable\n", " |      collections.abc.Container\n", " |      abc.ABC\n", " |      builtins.object\n", " |  \n", " |  Methods defined here:\n", " |  \n", " |  SE3(self, z=0)\n", " |      Create SE(3) from SE(2)\n", " |      \n", " |      :param z: default z coordinate, defaults to 0\n", " |      :type z: float\n", " |      :return: SE(2) with same rotation but zero translation\n", " |      :rtype: SE2 instance\n", " |      \n", " |      \"Lifts\" 2D rigid-body motion to 3D, rotation in the xy-plane (about the z-axis) and\n", " |      z-coordinate is settable.\n", " |  \n", " |  Twist2(self)\n", " |  \n", " |  __init__(self, x=None, y=None, theta=None, *, unit='rad', check=True)\n", " |      Construct new SE(2) object\n", " |      \n", " |      :param unit: angular units 'deg' or 'rad' [default] if applicable\n", " |      :type unit: str, optional\n", " |      :param check: check for valid SE(2) elements if applicable, default to True\n", " |      :type check: bool\n", " |      :return: SE(2) matrix\n", " |      :rtype: SE2 instance\n", " |      \n", " |      - ``SE2()`` is an SE2 instance representing a null motion -- the\n", " |        identity matrix\n", " |      - ``SE2(θ)`` is an SE2 instance representing a pure rotation of\n", " |        ``θ`` radians\n", " |      - ``SE2(θ, unit='deg')`` as above but ``θ`` in degrees\n", " |      - ``SE2(x, y)`` is an SE2 instance representing a pure translation of\n", " |        (``x``, ``y``)\n", " |      - ``SE2(t)`` is an SE2 instance representing a pure translation of\n", " |        (``x``, ``y``) where``t``=[x,y] is a 2-element array_like\n", " |      - ``SE2(x, y, θ)`` is an SE2 instance representing a translation of\n", " |        (``x``, ``y``) and a rotation of ``θ`` radians\n", " |      - ``SE2(x, y, θ, unit='deg')`` as above but ``θ`` in degrees\n", " |      - ``SE2(t)`` where ``t``=[x,y] is a 2-element array_like, is an SE2\n", " |        instance representing a pure translation of (``x``, ``y``)\n", " |      - ``SE2(q)`` where ``q``=[x,y,θ] is a 3-element array_like, is an SE2\n", " |        instance representing a translation of (``x``, ``y``) and a rotation\n", " |        of ``θ`` radians\n", " |      - ``SE2(t, unit='deg')`` as above but ``θ`` in degrees\n", " |      - ``SE2(T)`` is an SE2 instance with rigid-body motion described by the\n", " |        SE(2) matrix T which is a 3x3 numpy array.  If ``check`` is ``True``\n", " |        check the matrix belongs to SE(2).\n", " |      - ``SE2([T1, T2, ... TN])`` is an SE2 instance containing a sequence of\n", " |        N rigid-body motions, each described by an SE(2) matrix Ti which is a\n", " |        3x3 numpy array. If ``check`` is ``True`` then check each matrix\n", " |        belongs to SE(2).\n", " |      - ``SE2([X1, X2, ... XN])`` is an SE2 instance containing a sequence of\n", " |        N rigid-body motions, where each Xi is an SE2 instance.\n", " |  \n", " |  inv(self)\n", " |      Inverse of SE(2)\n", " |      \n", " |      :param self: pose\n", " |      :type self: SE2 instance\n", " |      :return: inverse\n", " |      :rtype: SE2\n", " |      \n", " |      Notes:\n", " |      \n", " |          - for elements of SE(2) this takes into account the matrix structure :math:`T = \\left[ \\begin{array}{cc} R & t \\\\ 0 & 1 \\end{array} \\right], T^{-1} = \\left[ \\begin{array}{cc} R^T & -R^T t \\\\ 0 & 1 \\end{array} \\right]`\n", " |          - if `x` contains a sequence, returns an `SE2` with a sequence of inverses\n", " |  \n", " |  xyt(self)\n", " |      SE(2) as a configuration vector\n", " |      \n", " |      :return: An array :math:`[x, y, \\theta]` :rtype: numpy.ndarray\n", " |      \n", " |      ``x.xyt`` is the rigidbody motion in minimal form as a translation and\n", " |      rotation expressed in vector form as :math:`[x, y, \\theta]`.  If\n", " |      ``len(x)`` is:\n", " |      \n", " |      - 1, return an ndarray with shape=(3,)\n", " |      - N>1, return an ndarray with shape=(N,3)\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Class methods defined here:\n", " |  \n", " |  Exp(S, check=True) from abc.ABCMeta\n", " |      Construct a new SE(2) from se(2) Lie algebra\n", " |      \n", " |      :param S: element of Lie algebra se(2)\n", " |      :type S: numpy n<PERSON><PERSON>\n", " |      :param check: check that passed matrix is valid se(2), default True\n", " |      :type check: bool\n", " |      :return: homogeneous transform matrix\n", " |      :rtype: SE2 instance\n", " |      \n", " |      - ``SE2.Exp(S)`` is an SE(2) rotation defined by its Lie algebra\n", " |        which is a 3x3 se(2) matrix (skew symmetric)\n", " |      - ``SE2.Exp(t)`` is an SE(2) rotation defined by a 3-element twist\n", " |        vector array_like (the unique elements of the se(2) skew-symmetric matrix)\n", " |      - ``SE2.Exp(T)`` is a sequence of SE(2) rigid-body motions defined by an Nx3 matrix of twist vectors, one per row.\n", " |      \n", " |      Note:\n", " |      \n", " |      - an input 3x3 matrix is ambiguous, it could be the first or third case above. In this case the argument ``se2`` is the decider.\n", " |      \n", " |      :seealso: :func:`spatialmath.base.transforms2d.trexp`, :func:`spatialmath.base.transformsNd.skew`\n", " |  \n", " |  Rand(N=1, xrange=(-1, 1), yrange=(-1, 1), arange=(0, 6.283185307179586), unit='rad') from abc.ABCMeta\n", " |      Construct a new random SE(2)\n", " |      \n", " |      :param xrange: x-axis range [min,max], defaults to [-1, 1]\n", " |      :type xrange: 2-element sequence, optional\n", " |      :param yrange: y-axis range [min,max], defaults to [-1, 1]\n", " |      :type yrange: 2-element sequence, optional\n", " |      :param arange: angle range [min,max], defaults to :math:`[0, 2\\pi)`\n", " |      :type arange: 2-element sequence, optional\n", " |      :param N: number of random rotations, defaults to 1\n", " |      :type N: int\n", " |      :param unit: angular units 'deg' or 'rad' [default] if applicable\n", " |      :type unit: str, optional\n", " |      :return: homogeneous rigid-body transformation matrix\n", " |      :rtype: SE2 instance\n", " |      \n", " |      Return an SE2 instance with random rotation and translation.\n", " |      \n", " |      - ``SE2.<PERSON>()`` is a random SE(2) rotation.\n", " |      - ``SE2.<PERSON>(N)`` is an SE2 object containing a sequence of N random\n", " |        poses.\n", " |      \n", " |      Example, create random ten vehicles in the xy-plane::\n", " |      \n", " |          >>> x = SE3.<PERSON>(N=10, xrange=[-2,2], yrange=[-2,2])\n", " |          >>> len(x)\n", " |          10\n", " |  \n", " |  Rot(theta, unit='rad') from abc.ABCMeta\n", " |      Create an SE(2) rotation\n", " |      \n", " |      :param theta: rotation angle in radians\n", " |      :type theta: float\n", " |      :param unit: angular units: \"rad\" [default] or \"deg\"\n", " |      :type unit: str\n", " |      :return: SE(2) matrix\n", " |      :rtype: SE2 instance\n", " |      \n", " |      `SE2.Rot(theta)` is an SE(2) rotation of ``theta``\n", " |      \n", " |      Example:\n", " |      \n", " |      .. runblock:: pycon\n", " |      \n", " |          >>> SE2.Rot(0.3)\n", " |          >>> SE2.Rot([0.2, 0.3])\n", " |      \n", " |      \n", " |      :seealso: :func:`~spatialmath.base.transforms3d.transl`\n", " |      :SymPy: supported\n", " |  \n", " |  Tx(x) from abc.ABCMeta\n", " |      Create an SE(2) translation along the X-axis\n", " |      \n", " |      :param x: translation distance along the X-axis\n", " |      :type x: float\n", " |      :return: SE(2) matrix\n", " |      :rtype: SE2 instance\n", " |      \n", " |      `SE2.Tx(x)` is an SE(2) translation of ``x`` along the x-axis\n", " |      \n", " |      Example:\n", " |      \n", " |      .. runblock:: pycon\n", " |      \n", " |          >>> SE2.Tx(2)\n", " |          >>> SE2.Tx([2,3])\n", " |      \n", " |      \n", " |      :seealso: :func:`~spatialmath.base.transforms3d.transl`\n", " |      :SymPy: supported\n", " |  \n", " |  Ty(y) from abc.ABCMeta\n", " |      Create an SE(2) translation along the Y-axis\n", " |      \n", " |      :param y: translation distance along the Y-axis\n", " |      :type y: float\n", " |      :return: SE(2) matrix\n", " |      :rtype: SE2 instance\n", " |      \n", " |      `SE2.Ty(y) is an SE(2) translation of ``y`` along the y-axis\n", " |      \n", " |      Example:\n", " |      \n", " |      .. runblock:: pycon\n", " |      \n", " |          >>> SE2.<PERSON>(2)\n", " |          >>> SE2.<PERSON>([2,3])\n", " |      \n", " |      :seealso: :func:`~spatialmath.base.transforms3d.transl`\n", " |      :SymPy: supported\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Static methods defined here:\n", " |  \n", " |  isvalid(x, check=True)\n", " |      Test if matrix is valid SE(2)\n", " |      \n", " |      :param x: matrix to test\n", " |      :type x: numpy.ndarray\n", " |      :return: true if the matrix is a valid element of SE(2), ie. it is a\n", " |               3x3 homogeneous rigid-body transformation matrix.\n", " |      :rtype: bool\n", " |      \n", " |      :seealso: :func:`~spatialmath.base.transform2d.ishom`\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Readonly properties defined here:\n", " |  \n", " |  shape\n", " |      Shape of the object's interal matrix representation\n", " |      \n", " |      :return: (3,3)\n", " |      :rtype: tuple\n", " |  \n", " |  t\n", " |      Translational component of SE(2)\n", " |      \n", " |      :param self: SE(2)\n", " |      :type self: SE2 instance\n", " |      :return: translational component\n", " |      :rtype: numpy.ndarray\n", " |      \n", " |      ``x.t`` is the translational vector component.  If ``len(x)`` is:\n", " |      \n", " |      - 1, return an ndarray with shape=(2,)\n", " |      - N>1, return an ndarray with shape=(N,2)\n", " |  \n", " |  x\n", " |      First element of the translational component of SE(2)\n", " |      \n", " |      :param self: SE(2)\n", " |      :type self: SE2 instance\n", " |      :return: translational component\n", " |      :rtype: float\n", " |      \n", " |      ``v.x`` is the first element of the translational vector component.  If ``len(x)`` is:\n", " |      \n", " |      - 1, return an float\n", " |      - N>1, return an ndarray with shape=(N,)\n", " |  \n", " |  y\n", " |      Second element of the translational component of SE(2)\n", " |      \n", " |      :param self: SE(2)\n", " |      :type self: SE2 instance\n", " |      :return: translational component\n", " |      :rtype: float\n", " |      \n", " |      ``v.y`` is the second element of the translational vector component.  If ``len(x)`` is:\n", " |      \n", " |      - 1, return an float\n", " |      - N>1, return an ndarray with shape=(N,)\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Data and other attributes defined here:\n", " |  \n", " |  __abstractmethods__ = frozenset()\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Methods inherited from SO2:\n", " |  \n", " |  SE2(self)\n", " |      Create SE(2) from SO(2)\n", " |      \n", " |      :return: SE(2) with same rotation but zero translation\n", " |      :rtype: SE2 instance\n", " |  \n", " |  theta(self, unit='rad')\n", " |      SO(2) as a rotation angle\n", " |      \n", " |      :param unit: angular units 'deg' or 'rad' [default]\n", " |      :type unit: str, optional\n", " |      :return: rotation angle\n", " |      :rtype: float or list\n", " |      \n", " |      ``x.theta`` is the rotation angle such that `x` is `SO2(x.theta)`.\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Readonly properties inherited from SO2:\n", " |  \n", " |  R\n", " |      SO(2) or SE(2) as rotation matrix\n", " |      \n", " |      :return: rotational component\n", " |      :rtype: numpy.ndarray, shape=(2,2)\n", " |      \n", " |      ``x.R`` returns the rotation matrix, when `x` is `SO2` or `SE2`. If `len(x)` is:\n", " |      \n", " |      - 1, return an ndarray with shape=(2,2)\n", " |      - N>1, return ndarray with shape=(N,2,2)\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Methods inherited from spatialmath.baseposematrix.BasePoseMatrix:\n", " |  \n", " |  __add__(left, right)\n", " |      Overloaded ``+`` operator (superclass method)\n", " |      \n", " |      :return: Sum of two operands\n", " |      :rtype: NumPy array, shape=(N,N)\n", " |      :raises ValueError: for incompatible arguments\n", " |      \n", " |      \n", " |      Add the elements of two poses.  This is not a group operation so the\n", " |      result is a matrix not a pose class.\n", " |      \n", " |      - ``X + Y`` is the element-wise sum of the matrix value of ``X`` and ``Y``\n", " |      - ``X + s`` is the element-wise sum of the matrix value of ``X`` and scalar ``s``\n", " |      - ``s + X`` is the element-wise sum of the scalar ``s`` and the matrix value of ``X``\n", " |      \n", " |      ==============   ==============   ===========  ========================\n", " |                 Operands                   Sum\n", " |      -------------------------------   -------------------------------------\n", " |          left             right            type           operation\n", " |      ==============   ==============   ===========  ========================\n", " |      Pose             Pose             NxN matrix   element-wise matrix sum\n", " |      Pose             scalar           NxN matrix   element-wise sum\n", " |      scalar           Pose             NxN matrix   element-wise sum\n", " |      ==============   ==============   ===========  ========================\n", " |      \n", " |      .. note::\n", " |      \n", " |          #. Pose is an ``SO2``, ``SE2``, ``SO3`` or ``SE3`` instance\n", " |          #. N is 2 for ``SO2``, ``SE2``; 3 for ``SO3`` or ``SE3``\n", " |          #. scalar + Pose is handled by :meth:`__radd__`\n", " |          #. Addition is commutative\n", " |          #. Any other input combinations result in a ``ValueError``.\n", " |      \n", " |      For pose addition either or both operands may hold more than one value which\n", " |      results in the sum holding more than one value according to:\n", " |      \n", " |      =========   ==========   ====  ================================\n", " |      len(left)   len(right)   len     operation\n", " |      =========   ==========   ====  ================================\n", " |       1          1             1    ``sum = left + right``\n", " |       1          M             M    ``sum[i] = left + right[i]``\n", " |       N          1             M    ``sum[i] = left[i] + right``\n", " |       M          M             M    ``sum[i] = left[i] + right[i]``\n", " |      =========   ==========   ====  ================================\n", " |  \n", " |  __eq__(left, right: 'Self') -> 'bool'\n", " |      Overloaded ``==`` operator (superclass method)\n", " |      \n", " |      :return: Equality of two operands\n", " |      :rtype: bool or list of bool\n", " |      \n", " |      Test two poses for equality\n", " |      \n", " |      ``X == Y`` is true of the poses are of the same type and numerically\n", " |      equal.\n", " |      \n", " |      If either or both operands may hold more than one value which\n", " |      results in the equality test holding more than one value according to:\n", " |      \n", " |      =========   ==========   ====  ================================\n", " |      len(left)   len(right)   len     operation\n", " |      =========   ==========   ====  ================================\n", " |       1          1             1    ``eq = left == right``\n", " |       1          M             M    ``eq[i] = left == right[i]``\n", " |       N          1             M    ``eq[i] = left[i] == right``\n", " |       M          M             M    ``eq[i] = left[i] == right[i]``\n", " |      =========   ==========   ====  ================================\n", " |  \n", " |  __iadd__(left, right)\n", " |      Overloaded ``+=`` operator (superclass method)\n", " |      \n", " |      :return: Sum of two operands\n", " |      :rtype: NumPy array, shape=(N,N)\n", " |      :raises ValueError: for incompatible arguments\n", " |      \n", " |      - ``X += Y`` adds the matrix values of ``X`` and ``Y`` and places the result in ``X``\n", " |      - ``X += s`` elementwise addition of the matrix elements of ``X``\n", " |        and ``s`` and places the result in ``X``\n", " |      \n", " |      :seealso: ``__add__``\n", " |  \n", " |  __imul__(left, right)\n", " |      Overloaded ``*=`` operator (superclass method)\n", " |      \n", " |      :return: Product of two operands\n", " |      :rtype: Pose instance or NumPy array\n", " |      :raises ValueError: for incompatible arguments\n", " |      \n", " |      - ``X *= Y`` compounds the poses ``X`` and ``Y`` and places the result in ``X``\n", " |      - ``X *= s`` performs elementwise multiplication of the elements of ``X``\n", " |        and ``s`` and places the result in ``X``\n", " |      \n", " |      :see<PERSON>o: ``__mul__``\n", " |  \n", " |  __isub__(left, right: 'Self')\n", " |      Overloaded ``-=`` operator (superclass method)\n", " |      \n", " |      :return: Difference of two operands\n", " |      :rtype: NumPy array, shape=(N,N)\n", " |      :raises: ValueError\n", " |      \n", " |      - ``X -= Y`` is the element-wise difference of the matrix value of ``X``\n", " |        and ``Y`` and places the result in ``X``\n", " |      - ``X -= s`` is the element-wise difference of the matrix value of ``X``\n", " |        and the scalar ``s`` and places the result in ``X``\n", " |      \n", " |      :seealso: ``__sub__``\n", " |  \n", " |  __itruediv__(left, right)\n", " |      Overloaded ``/=`` operator (superclass method)\n", " |      \n", " |      :return: Product of right operand and inverse of left operand\n", " |      :rtype: Pose instance or NumPy array\n", " |      :raises ValueError: for incompatible arguments\n", " |      \n", " |      - ``X /= Y`` compounds the poses ``X`` and ``Y.inv()`` and places the result in ``X``\n", " |      - ``X /= s`` performs elementwise division of the elements of ``X``\n", " |        by ``s`` and places the result in ``X``\n", " |      \n", " |      :seealso: ``__truediv__``\n", " |  \n", " |  __matmul__(left, right)\n", " |      Overloaded ``@`` operator (superclass method)\n", " |      \n", " |      :return: Product of two operands with normalization\n", " |      :rtype: Pose instance or NumPy array\n", " |      :raises ValueError: for incompatible arguments\n", " |      \n", " |      - ``X @ Y`` compounds the poses ``X`` and ``Y`` and normalizes the result\n", " |      - ``X @= Y`` compounds the poses ``X`` and ``Y``, normalizes the result,\n", " |        and places the result in ``X``\n", " |      \n", " |      .. note:: This operator is functionally equivalent to ``*`` but is more\n", " |          costly.  It is useful for cases where a pose is incrementally\n", " |          update over many cycles.\n", " |      \n", " |      :seealso: :func:`__mul__`, :func:`~spatialmath.base.trnorm`\n", " |  \n", " |  __mul__(left, right)\n", " |      Overloaded ``*`` operator (superclass method)\n", " |      \n", " |      :return: Product of two operands\n", " |      :rtype: Pose instance or NumPy array\n", " |      :raises NotImplemented: for incompatible arguments\n", " |      \n", " |      Pose composition, scaling or vector transformation:\n", " |      \n", " |      - ``X * Y`` compounds the poses ``X`` and ``Y``\n", " |      - ``X * s`` performs element-wise multiplication of the elements of ``X`` by ``s``\n", " |      - ``s * X`` performs element-wise multiplication of the elements of ``X`` by ``s``\n", " |      - ``X * v`` linear transformation of the vector ``v`` where ``v`` is array-like\n", " |      \n", " |      ==============   ==============   ===========  ======================\n", " |                 Multiplicands                   Product\n", " |      -------------------------------   -----------------------------------\n", " |          left             right            type           operation\n", " |      ==============   ==============   ===========  ======================\n", " |      Pose             Pose             Pose         matrix product\n", " |      Pose             scalar           NxN matrix   element-wise product\n", " |      scalar           Pose             NxN matrix   element-wise product\n", " |      Pose             N-vector         N-vector     vector transform\n", " |      Pose             NxM matrix       NxM matrix   transform each column\n", " |      ==============   ==============   ===========  ======================\n", " |      \n", " |      .. note::\n", " |      \n", " |          #. Pose is an ``SO2``, ``SE2``, ``SO3`` or ``SE3`` instance\n", " |          #. N is 2 for ``SO2``, ``SE2``; 3 for ``SO3`` or ``SE3``\n", " |          #. Scalar x Pose is handled by __rmul__`\n", " |          #. <PERSON><PERSON><PERSON> multiplication is commutative but the result is not a group\n", " |             operation so the result will be a matrix\n", " |          #. Any other input combinations result in a ValueError.\n", " |      \n", " |      For pose composition either or both operands may hold more than one value which\n", " |      results in the composition holding more than one value according to:\n", " |      \n", " |      =========   ==========   ====  ================================\n", " |      len(left)   len(right)   len     operation\n", " |      =========   ==========   ====  ================================\n", " |       1          1             1    ``prod = left * right``\n", " |       1          M             M    ``prod[i] = left * right[i]``\n", " |       N          1             M    ``prod[i] = left[i] * right``\n", " |       M          M             M    ``prod[i] = left[i] * right[i]``\n", " |      =========   ==========   ====  ================================\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> SE3.Rx(pi/2) * SE3.Ry(pi/2)\n", " |          SE3(array([[0., 0., 1., 0.],\n", " |                  [1., 0., 0., 0.],\n", " |                  [0., 1., 0., 0.],\n", " |                  [0., 0., 0., 1.]]))\n", " |          >>> SE3.Rx(pi/2) * 2\n", " |          array([[ 2.0000000e+00,  0.0000000e+00,  0.0000000e+00,  0.0000000e+00],\n", " |                 [ 0.0000000e+00,  1.2246468e-16, -2.0000000e+00,  0.0000000e+00],\n", " |                 [ 0.0000000e+00,  2.0000000e+00,  1.2246468e-16,  0.0000000e+00],\n", " |                 [ 0.0000000e+00,  0.0000000e+00,  0.0000000e+00,  2.0000000e+00]])\n", " |      \n", " |      For vector transformation there are three cases:\n", " |      \n", " |      =========  ===========  =====  ==========================\n", " |            Multiplicands             Product\n", " |      ----------------------  ---------------------------------\n", " |      len(left)  right.shape  shape  operation\n", " |      =========  ===========  =====  ==========================\n", " |      1          (N,)         (N,)   vector transformation\n", " |      M          (N,)         (N,M)  vector transformations\n", " |      1          (N,M)        (N,M)  column transformation\n", " |      =========  ===========  =====  ==========================\n", " |      \n", " |      .. note::\n", " |          - The vector is an array-like, a 1D NumPy array or a list/tuple\n", " |          - For the ``SE2`` and ``SE3`` case the vectors are converted to homogeneous\n", " |            form, transformed, then converted back to Euclidean form.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> SE3.Rx(pi/2) * [0, 1, 0]\n", " |          array([0.000000e+00, 6.123234e-17, 1.000000e+00])\n", " |          >>> SE3.Rx(pi/2) * np.r_[0, 0, 1]\n", " |          array([ 0.000000e+00, -1.000000e+00,  6.123234e-17])\n", " |  \n", " |  __ne__(left, right)\n", " |      Overloaded ``!=`` operator (superclass method)\n", " |      \n", " |      :return: Inequality of two operands\n", " |      :rtype: bool or list of bool\n", " |      \n", " |      Test two poses for inequality\n", " |      \n", " |      - ``X != Y`` is true of the poses are of the same type but not numerically\n", " |        equal.\n", " |      \n", " |      If either or both operands may hold more than one value which\n", " |      results in the inequality test holding more than one value according to:\n", " |      \n", " |      =========   ==========   ====  ================================\n", " |      len(left)   len(right)   len     operation\n", " |      =========   ==========   ====  ================================\n", " |       1          1             1    ``ne = left != right``\n", " |       1          M             M    ``ne[i] = left != right[i]``\n", " |       N          1             M    ``ne[i] = left[i] != right``\n", " |       M          M             M    ``ne[i] = left[i] != right[i]``\n", " |      =========   ==========   ====  ================================\n", " |  \n", " |  __pow__(self, n: 'int') -> 'Self'\n", " |      Overloaded ``**`` operator (superclass method)\n", " |      \n", " |      :param n: exponent\n", " |      :type n: int\n", " |      :return: pose to the power ``n``\n", " |      :rtype: pose instance\n", " |      \n", " |      ``X**n`` raise all values held in `X` to the specified power using repeated\n", " |      multiplication.  If ``n`` < 0 then the result is inverted.\n", " |      \n", " |      Example:\n", " |      \n", " |      .. runblock:: pycon\n", " |      \n", " |          >>> from spatialmath import SE3\n", " |          >>> SE3.Rx(0.1) ** 2\n", " |          >>> SE3.Rx([0, 0.1]) ** 2\n", " |  \n", " |  __radd__(right, left)\n", " |      Overloaded ``+`` operator (superclass method)\n", " |      \n", " |      :return: Sum of two operands\n", " |      :rtype: NumPy array, shape=(N,N)\n", " |      :raises ValueError: for incompatible arguments\n", " |      \n", " |      Left-addition by a scalar\n", " |      \n", " |      - ``s + X`` performs elementwise addition of the elements of ``X`` and ``s``\n", " |      \n", " |      :seealso: :meth:`__add__`\n", " |  \n", " |  __repr__(self) -> 'str'\n", " |      Readable representation of pose (superclass method)\n", " |      \n", " |      :return: readable representation of the pose as a list of arrays\n", " |      :rtype: str\n", " |      \n", " |      Example:\n", " |      \n", " |      .. runblock:: pycon\n", " |      \n", " |          >>> from spatialmath import SE3\n", " |          >>> x = SE3.Rx(0.3)\n", " |          >>> repr(x)\n", " |  \n", " |  __rmul__(right, left)\n", " |      Overloaded ``*`` operator (superclass method)\n", " |      \n", " |      :return: Product of two operands\n", " |      :rtype: Pose instance or NumPy array\n", " |      :raises NotImplemented: for incompatible arguments\n", " |      \n", " |      Left-multiplication\n", " |      \n", " |      - ``s * X`` where ``s`` is a scalar, performs elementwise multiplication of the\n", " |        elements of ``X`` by ``s`` and the result is a NumPy array.\n", " |      - ``A * X`` where ``A`` is a conforming matrix, performs matrix multiplication\n", " |        of ``A`` and ``X`` and the result is a NumPy array.\n", " |      \n", " |      :seealso: :func:`__mul__`\n", " |  \n", " |  __rsub__(right, left: 'Self')\n", " |      Overloaded ``-`` operator (superclass method)\n", " |      \n", " |      :return: Difference of two operands\n", " |      :rtype: NumPy array, shape=(N,N)\n", " |      :raises ValueError: for incompatible arguments\n", " |      \n", " |      Left-addition by a scalar\n", " |      \n", " |      - ``s - X`` performs elementwise addition of the elements of ``X`` and ``s``\n", " |      \n", " |      :seealso: :meth:`__sub__`\n", " |  \n", " |  __str__(self) -> 'str'\n", " |      Pretty string representation of pose (superclass method)\n", " |      \n", " |      :return: readable representation of the pose\n", " |      :rtype: str\n", " |      \n", " |      Convert the pose's matrix value to a simple grid of numbers.\n", " |      \n", " |      Example:\n", " |      \n", " |      .. runblock:: pycon\n", " |      \n", " |          >>> from spatialmath import SE3\n", " |          >>> x = SE3.Rx(0.3)\n", " |          >>> print(x)\n", " |      \n", " |      Notes:\n", " |      \n", " |          - By default, the output is colorised for an ANSI terminal console:\n", " |      \n", " |              * red: rotational elements\n", " |              * blue: translational elements\n", " |              * white: constant elements\n", " |  \n", " |  __sub__(left, right)\n", " |      Overloaded ``-`` operator (superclass method)\n", " |      \n", " |      :return: Difference of two operands\n", " |      :rtype: NumPy array, shape=(N,N)\n", " |      :raises ValueError: for incompatible arguments\n", " |      \n", " |      \n", " |      Subtract elements of two poses.  This is not a group operation so the\n", " |      result is a matrix not a pose class.\n", " |      \n", " |      - ``X - Y`` is the element-wise difference of the matrix value of ``X`` and ``Y``\n", " |      - ``X - s`` is the element-wise difference of the matrix value of ``X`` and the scalar ``s``\n", " |      - ``s - X`` is the element-wise difference of the scalar ``s`` and the matrix value of ``X``\n", " |      \n", " |      ==============   ==============   ===========  ==============================\n", " |                 Operands                   Sum\n", " |      -------------------------------   -------------------------------------------\n", " |          left             right            type           operation\n", " |      ==============   ==============   ===========  ==============================\n", " |      Pose             Pose             NxN matrix   element-wise matrix difference\n", " |      Pose             scalar           NxN matrix   element-wise sum\n", " |      scalar           Pose             NxN matrix   element-wise sum\n", " |      ==============   ==============   ===========  ==============================\n", " |      \n", " |      .. note::\n", " |      \n", " |          #. Pose is ``SO2``, ``SE2``, ``SO3`` or ``SE3`` instance\n", " |          #. N is 2 for ``SO2``, ``SE2``; 3 for ``SO3`` or ``SE3``\n", " |          #. scalar - Pose is handled by :meth:`__rsub__`\n", " |          #. Any other input combinations result in a ``ValueError``.\n", " |      \n", " |      For pose subtraction either or both operands may hold more than one value which\n", " |      results in the difference holding more than one value according to:\n", " |      \n", " |      =========   ==========   ====  ================================\n", " |      len(left)   len(right)   len     operation\n", " |      =========   ==========   ====  ================================\n", " |       1          1             1    ``diff = left - right``\n", " |       1          M             M    ``diff[i] = left - right[i]``\n", " |       N          1             M    ``diff[i] = left[i] - right``\n", " |       M          M             M    ``diff[i] = left[i]  right[i]``\n", " |      =========   ==========   ====  ================================\n", " |  \n", " |  __truediv__(left, right)\n", " |      Overloaded ``/`` operator (superclass method)\n", " |      \n", " |      :return: Product of right operand and inverse of left operand\n", " |      :rtype: pose instance or NumPy array\n", " |      :raises ValueError: for incompatible arguments\n", " |      \n", " |      Pose composition or scaling:\n", " |      \n", " |      - ``X / Y`` compounds the poses ``X`` and ``Y.inv()``\n", " |      - ``X / s`` performs elementwise division of the elements of ``X`` by ``s``\n", " |      \n", " |      ==============   ==============   ===========  =========================\n", " |                 Multiplicands                   Quotient\n", " |      -------------------------------   --------------------------------------\n", " |          left             right            type           operation\n", " |      ==============   ==============   ===========  =========================\n", " |      Pose             Pose             Pose         matrix product by inverse\n", " |      Pose             scalar           NxN matrix   element-wise division\n", " |      ==============   ==============   ===========  =========================\n", " |      \n", " |      .. note::\n", " |      \n", " |          #. Pose is ``SO2``, ``SE2``, ``SO3`` or ``SE3`` instance\n", " |          #. N is 2 for ``SO2``, ``SE2``; 3 for ``SO3`` or ``SE3``\n", " |          #. <PERSON><PERSON><PERSON> multiplication is not a group operation so the result will\n", " |             be a matrix\n", " |          #. Any other input combinations result in a ValueError.\n", " |      \n", " |      For pose composition either or both operands may hold more than one value which\n", " |      results in the composition holding more than one value according to:\n", " |      \n", " |      =========   ==========   ====  =====================================\n", " |      len(left)   len(right)   len     operation\n", " |      =========   ==========   ====  =====================================\n", " |       1          1             1    ``quo = left * right.inv()``\n", " |       1          M             M    ``quo[i] = left * right[i].inv()``\n", " |       N          1             M    ``quo[i] = left[i] * right.inv()``\n", " |       M          M             M    ``quo[i] = left[i] * right[i].inv()``\n", " |      =========   ==========   ====  =====================================\n", " |  \n", " |  animate(self, *args, start=None, **kwargs) -> 'None'\n", " |      Plot pose object as an animated coordinate frame (superclass method)\n", " |      \n", " |      :param start: initial pose, defaults to null/identity\n", " |      :type start: same as ``self``\n", " |      :param `**kwargs`: plotting options\n", " |      \n", " |      - ``X.animate()`` displays the pose ``X`` as a coordinate frame moving\n", " |        from the origin in either 2D or 3D.  There are many options, see the\n", " |        links below.\n", " |      - ``X.animate(*args, start=X1)`` displays the pose ``X`` as a coordinate\n", " |        frame moving from pose ``X1``, in either 2D or 3D.  There are\n", " |        many options, see the links below.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> X = SE3.Rx(0.3)\n", " |          >>> X.animate(frame='A', color='green')\n", " |          >>> X.animate(start=SE3.Ry(0.2))\n", " |      \n", " |      :seealso: :func:`~spatialmath.base.transforms3d.tranimate`, :func:`~spatialmath.base.transforms2d.tranimate2`\n", " |  \n", " |  conjugation(self, A: 'NDArray') -> 'NDArray'\n", " |      Matrix conjugation\n", " |      \n", " |      :param A: matrix to conjugate\n", " |      :type A: ndarray\n", " |      :return: conjugated matrix\n", " |      :rtype: n<PERSON><PERSON>\n", " |      \n", " |      Compute the conjugation :math:`\\mat{X} \\mat{A} \\mat{X}^{-1}` where :math:`\\mat{X}`\n", " |      is the current object.\n", " |      \n", " |      Example:\n", " |      \n", " |      .. runblock:: pycon\n", " |      \n", " |          >>> from spatialmath import SO2\n", " |          >>> import numpy as np\n", " |          >>> R = SO2(0.5)\n", " |          >>> A = np.array([[10, 0], [0, 1]])\n", " |          >>> print(R * A * R.inv())\n", " |          >>> print(R.conjugation(A))\n", " |  \n", " |  det(self) -> '<PERSON><PERSON>[float, Rn]'\n", " |      Determinant of rotational component (superclass method)\n", " |      \n", " |      :return: Determinant of rotational component\n", " |      :rtype: float or NumPy array\n", " |      \n", " |      ``x.det()`` is the determinant of the rotation component of the values\n", " |      of ``x``.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> x=SE3.Rand()\n", " |          >>> x.det()\n", " |          1.0000000000000004\n", " |          >>> x=SE3.<PERSON>(N=2)\n", " |          >>> x.det()\n", " |          [0.9999999999999997, 1.0000000000000002]\n", " |      \n", " |      :SymPy: not supported\n", " |  \n", " |  interp(self, end: 'Optional[bool]' = None, s: 'Union[int, float]' = None, shortest: 'bool' = True) -> 'Self'\n", " |      Interpolate between poses (superclass method)\n", " |      \n", " |      :param end: final pose\n", " |      :type end: same as ``self``\n", " |      :param s: interpolation coefficient, range 0 to 1, or number of steps\n", " |      :type s: array_like or int\n", " |      :param shortest: take the shortest path along the great circle for the rotation\n", " |      :type shortest: bool, default to True\n", " |      :return: interpolated pose\n", " |      :rtype: same as ``self``\n", " |      \n", " |      - ``X.interp(Y, s)`` interpolates pose between X between when s=0\n", " |        and Y when s=1.\n", " |      - ``<PERSON>.interp(Y, N)`` interpolates pose between X and Y in ``N`` steps.\n", " |      \n", " |      Example:\n", " |      \n", " |      .. runblock:: pycon\n", " |      \n", " |          >>> x = SE3(-1, -2, 0) * SE3.Rx(-0.3)\n", " |          >>> y = SE3(1, 2, 0) * SE3.Rx(0.3)\n", " |          >>> x.interp(y, 0)    # this is x\n", " |          >>> x.interp(y, 1)    # this is y\n", " |          >>> x.interp(y, 0.5)  # this is in between\n", " |          >>> z = x.interp(y, 11)  # in 11 steps\n", " |          >>> len(z)\n", " |          >>> z[0]              # this is x\n", " |          >>> z[5]              # this is in between\n", " |      \n", " |      .. note::\n", " |      \n", " |          - For SO3 and SE3 rotation is interpolated using quaternion spherical linear interpolation (slerp).\n", " |          - Values of ``s`` outside the range [0,1] are silently clipped\n", " |      :seealso: :func:`interp1`, :func:`~spatialmath.base.transforms3d.trinterp`, :func:`~spatialmath.base.quaternions.qslerp`, :func:`~spatialmath.base.transforms2d.trinterp2`\n", " |      \n", " |      :SymPy: not supported\n", " |  \n", " |  interp1(self, s: 'float' = None) -> 'Self'\n", " |      Interpolate pose (superclass method)\n", " |      \n", " |      :param end: final pose\n", " |      :type end: same as ``self``\n", " |      :param s: interpolation coefficient, range 0 to 1\n", " |      :type s: array_like\n", " |      :return: interpolated pose\n", " |      :rtype: SO2, SE2, SO3, SE3 instance\n", " |      \n", " |      - ``X.interp(s)`` interpolates pose between identity when s=0, and X when s=1.\n", " |      \n", " |          ======  ======  ===========  ===============================\n", " |          len(X)  len(s)  len(result)  Result\n", " |          ======  ======  ===========  ===============================\n", " |          1       1       1            Y = interp(X, s)\n", " |          M       1       M            Y[i] = interp(X[i], s)\n", " |          1       M       M            Y[i] = interp(X, s[i])\n", " |          ======  ======  ===========  ===============================\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> x = SE3.Rx(0.3)\n", " |          >>> print(x.interp(0))\n", " |          SE3(array([[1., 0., 0., 0.],\n", " |                     [0., 1., 0., 0.],\n", " |                     [0., 0., 1., 0.],\n", " |                     [0., 0., 0., 1.]]))\n", " |          >>> print(x.interp(1))\n", " |          SE3(array([[ 1.        ,  0.        ,  0.        ,  0.        ],\n", " |                     [ 0.        ,  0.95533649, -0.29552021,  0.        ],\n", " |                     [ 0.        ,  0.29552021,  0.95533649,  0.        ],\n", " |                     [ 0.        ,  0.        ,  0.        ,  1.        ]]))\n", " |          >>> y = x.interp(x, np.linspace(0, 1, 10))\n", " |          >>> len(y)\n", " |          10\n", " |          >>> y[5]\n", " |          SE3(array([[ 1.        ,  0.        ,  0.        ,  0.        ],\n", " |                     [ 0.        ,  0.98614323, -0.16589613,  0.        ],\n", " |                     [ 0.        ,  0.16589613,  0.98614323,  0.        ],\n", " |                     [ 0.        ,  0.        ,  0.        ,  1.        ]]))\n", " |      \n", " |      Notes:\n", " |      \n", " |      #. For SO3 and SE3 rotation is interpolated using quaternion spherical linear interpolation (slerp).\n", " |      \n", " |      :seealso: :func:`interp`, :func:`~spatialmath.base.transforms3d.trinterp`, :func:`~spatialmath.base.quaternions.qslerp`, :func:`~spatialmath.smb.transforms2d.trinterp2`\n", " |      \n", " |      :SymPy: not supported\n", " |  \n", " |  ishom(self) -> 'bool'\n", " |      Test if object belongs to SE(3) group (superclass method)\n", " |      \n", " |      :return: ``True`` if object is instance of SE3\n", " |      :rtype: bool\n", " |      \n", " |      For compatibility with Spatial Math Toolbox for MATLAB.\n", " |      In Python use ``isinstance(x, SE3)``.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> x = SO3()\n", " |          >>> x.isrot()\n", " |          False\n", " |          >>> x = SE3()\n", " |          >>> x.isrot()\n", " |          True\n", " |  \n", " |  ishom2(self) -> 'bool'\n", " |      Test if object belongs to SE(2) group (superclass method)\n", " |      \n", " |      :return: ``True`` if object is instance of SE2\n", " |      :rtype: bool\n", " |      \n", " |      For compatibility with Spatial Math Toolbox for MATLAB.\n", " |      In Python use ``isinstance(x, SE2)``.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> x = SO2()\n", " |          >>> x.isrot()\n", " |          False\n", " |          >>> x = SE2()\n", " |          >>> x.isrot()\n", " |          True\n", " |  \n", " |  isrot(self) -> 'bool'\n", " |      Test if object belongs to SO(3) group (superclass method)\n", " |      \n", " |      :return: ``True`` if object is instance of SO3\n", " |      :rtype: bool\n", " |      \n", " |      For compatibility with Spatial Math Toolbox for MATLAB.\n", " |      In Python use ``isinstance(x, SO3)``.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> x = SO3()\n", " |          >>> x.isrot()\n", " |          True\n", " |          >>> x = SE3()\n", " |          >>> x.isrot()\n", " |          False\n", " |  \n", " |  isrot2(self) -> 'bool'\n", " |      Test if object belongs to SO(2) group (superclass method)\n", " |      \n", " |      :return: ``True`` if object is instance of SO2\n", " |      :rtype: bool\n", " |      \n", " |      For compatibility with Spatial Math Toolbox for MATLAB.\n", " |      In Python use ``isinstance(x, SO2)``.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> x = SO2()\n", " |          >>> x.isrot()\n", " |          True\n", " |          >>> x = SE2()\n", " |          >>> x.isrot()\n", " |          False\n", " |  \n", " |  log(self, twist: 'Optional[bool]' = False) -> 'Union[NDArray, List[NDArray]]'\n", " |      Logarithm of pose (superclass method)\n", " |      \n", " |      :return: logarithm\n", " |      :rtype: n<PERSON><PERSON>\n", " |      :raises: ValueError\n", " |      \n", " |      An efficient closed-form solution of the matrix logarithm.\n", " |      \n", " |      =====  ======  ===============================\n", " |      Input         Output\n", " |      -----  ---------------------------------------\n", " |      Pose   Shape   Structure\n", " |      =====  ======  ===============================\n", " |      SO2    (2,2)   skew-symmetric SE2    (3,3)   augmented skew-symmetric\n", " |      SO3    (3,3)   skew-symmetric SE3    (4,4)   augmented skew-symmetric\n", " |      =====  ======  ===============================\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> x = SE3.Rx(0.3)\n", " |          >>> y = x.log()\n", " |          >>> y\n", " |          array([[ 0. , -0. ,  0. ,  0. ],\n", " |                 [ 0. ,  0. , -0.3,  0. ],\n", " |                 [-0. ,  0.3,  0. ,  0. ],\n", " |                 [ 0. ,  0. ,  0. ,  0. ]])\n", " |      \n", " |      \n", " |      :seealso: :func:`~spatialmath.base.transforms2d.trlog2`,\n", " |      :func:`~spatialmath.base.transforms3d.trlog`\n", " |      \n", " |      :SymPy: not supported\n", " |  \n", " |  norm(self) -> 'Self'\n", " |      Normalize pose (superclass method)\n", " |      \n", " |      :return: pose\n", " |      :rtype: SO2, SE2, SO3, SE3 instance\n", " |      \n", " |      - ``X.norm()`` is an equivalent pose object but the rotational matrix\n", " |        part of all values has been adjusted to ensure it is a proper orthogonal\n", " |        matrix rotation.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> x = SE3()\n", " |          >>> y = x.norm()\n", " |          >>> y\n", " |          SE3(array([[1., 0., 0., 0.],\n", " |                     [0., 1., 0., 0.],\n", " |                     [0., 0., 1., 0.],\n", " |                     [0., 0., 0., 1.]]))\n", " |      \n", " |      Notes:\n", " |      \n", " |      #. Only the direction of A vector (the z-axis) is unchanged.\n", " |      #. Used to prevent finite word length arithmetic causing transforms to\n", " |         become 'unnormalized'.\n", " |      \n", " |      :seealso: :func:`~spatialmath.base.transforms3d.trnorm`, :func:`~spatialmath.base.transforms2d.trnorm2`\n", " |  \n", " |  plot(self, *args, **kwargs) -> 'None'\n", " |      Plot pose object as a coordinate frame (superclass method)\n", " |      \n", " |      :param `**kwargs`: plotting options\n", " |      \n", " |      - ``X.plot()`` displays the pose ``X`` as a coordinate frame in either\n", " |        2D or 3D.  There are many options, see the links below.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> X = SE3.Rx(0.3)\n", " |          >>> X.plot(frame='A', color='green')\n", " |      \n", " |      .. plot::\n", " |      \n", " |          from spatialmath import SE3\n", " |          X = SE3.Rx(0.3)\n", " |          X.plot(frame='A', color='green')\n", " |      \n", " |      :seealso: :func:`~spatialmath.base.transforms3d.trplot`, :func:`~spatialmath.base.transforms2d.trplot2`\n", " |  \n", " |  print(self, label: 'Optional[str]' = None, file: 'Optional[TextIO]' = None) -> 'None'\n", " |      Print pose as a matrix (superclass method)\n", " |      \n", " |      :param label: label to print before the matrix, defaults to None\n", " |      :type label: str, optional\n", " |      :param file: file to write to, defaults to None\n", " |      :type file: file object, optional\n", " |      \n", " |      Print the pose as a matrix, with an optional line beforehand.  By default\n", " |      the matrix is printed to stdout.\n", " |      \n", " |      Example:\n", " |      \n", " |      .. runblock:: pycon\n", " |      \n", " |          >>> from spatialmath import SE3\n", " |          >>> SE3().print()\n", " |          >>> SE3().print(\"pose is:\")\n", " |      \n", " |      :seealso: :meth:`printline` :meth:`strline`\n", " |  \n", " |  printline(self, *args, **kwargs) -> 'None'\n", " |      Print pose in compact single line format (superclass method)\n", " |      \n", " |      :param arg: value for orient option, optional\n", " |      :type arg: str\n", " |      :param label: text label to put at start of line\n", " |      :type label: str\n", " |      :param fmt: conversion format for each number as used by ``format()``\n", " |      :type fmt: str\n", " |      :param label: text label to put at start of line\n", " |      :type label: str\n", " |      :param orient: 3-angle convention to use, optional, ``SO3`` and ``SE3``\n", " |                     only\n", " |      :type orient: str\n", " |      :param unit: angular units: 'rad' [default], or 'deg'\n", " |      :type unit: str\n", " |      :param file: file to write formatted string to. [default, stdout]\n", " |      :type file: file object\n", " |      \n", " |      Print pose in a compact single line format. If ``X`` has multiple\n", " |      values, print one per line.\n", " |      \n", " |      Orientation can be displayed in various formats:\n", " |      \n", " |      =============   =================================================\n", " |      ``orient``      description\n", " |      =============   =================================================\n", " |      ``'rpy/zyx'``   roll-pitch-yaw angles in ZYX axis order [default]\n", " |      ``'rpy/yxz'``   roll-pitch-yaw angles in YXZ axis order\n", " |      ``'rpy/zyx'``   roll-pitch-yaw angles in ZYX axis order\n", " |      ``'eul'``       Euler angles in ZYZ axis order\n", " |      ``'angvec'``    angle and axis\n", " |      =============   =================================================\n", " |      \n", " |      Example:\n", " |      \n", " |      .. runblock:: pycon\n", " |      \n", " |          >>> from spatialmath import SE2, SE3\n", " |          >>> x = SE3.Rx(0.3)\n", " |          >>> x.printline()\n", " |          >>> x = SE3.Rx([0.2, 0.3])\n", " |          >>> x.printline()\n", " |          >>> x.printline('angvec')\n", " |          >>> x.printline(orient='angvec', fmt=\"{:.6f}\")\n", " |          >>> x = SE2(1, 2, 0.3)\n", " |          >>> x.printline()\n", " |      \n", " |      .. note::\n", " |          - Default formatting is for compact display of data\n", " |          - For tabular data set ``fmt`` to a fixed width format such as\n", " |            ``fmt='{:.3g}'``\n", " |      \n", " |      :seealso: :meth:`strline` :func:`trprint`, :func:`trprint2`\n", " |  \n", " |  prod(self, norm=False, check=True) -> 'Self'\n", " |      Product of elements (superclass method)\n", " |      \n", " |      :param norm: normalize the product, defaults to False\n", " |      :type norm: bool, optional\n", " |      :param check: check that computed matrix is valid member of group, default True\n", " |      :bool check: bool, optional\n", " |      :return: Product of elements\n", " |      :rtype: pose instance\n", " |      \n", " |      ``x.prod()`` is the product of the values held by ``x``, ie.\n", " |      :math:`\\prod_i^N T_i`.\n", " |      \n", " |      .. runblock:: pycon\n", " |      \n", " |          >>> from spatialmath import SE3\n", " |          >>> x = SE3.Rx([0, 0.1, 0.2, 0.3])\n", " |          >>> x.prod()\n", " |      \n", " |      .. note:: When compounding many transformations the product may become\n", " |          denormalized resulting in a result that is not a proper member of the\n", " |          group.  You can either disable membership checking by ``check=False``\n", " |          which is risky, or normalize the result by ``norm=True``.\n", " |  \n", " |  simplify(self) -> 'Self'\n", " |      Symbolically simplify matrix values (superclass method)\n", " |      \n", " |      :return: pose with symbolic elements\n", " |      :rtype: pose instance\n", " |      \n", " |      Apply symbolic simplification to every element of every value in the\n", " |      pose instance.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> a = SE3.Rx(sympy.symbols('theta'))\n", " |          >>> b = a * a\n", " |          >>> b\n", " |          SE3(array([[1, 0, 0, 0.0],\n", " |          [0, -sin(theta)**2 + cos(theta)**2, -2*sin(theta)*cos(theta), 0],\n", " |          [0, 2*sin(theta)*cos(theta), -sin(theta)**2 + cos(theta)**2, 0],\n", " |          [0.0, 0, 0, 1.0]], dtype=object)\n", " |          >>> b.simplify()\n", " |          SE3(array([[1, 0, 0, 0],\n", " |          [0, cos(2*theta), -sin(2*theta), 0],\n", " |          [0, sin(2*theta), cos(2*theta), 0],\n", " |          [0, 0, 0, 1.00000000000000]], dtype=object))\n", " |      \n", " |      .. todo:: No need to simplify the constants in bottom row\n", " |      \n", " |      :SymPy: supported\n", " |  \n", " |  stack(self) -> 'NDArray'\n", " |      Convert to 3-dimensional matrix\n", " |      \n", " |      :return: 3-dimensional NumPy array\n", " |      :rtype: n<PERSON>ray(n,n,m)\n", " |      \n", " |      Converts the value to a 3-dimensional NumPy array where the values are\n", " |      stacked along the third axis.  The first two dimensions are given by\n", " |      ``self.shape``.\n", " |  \n", " |  strline(self, *args, **kwargs) -> 'str'\n", " |      Convert pose to compact single line string (superclass method)\n", " |      \n", " |      :param label: text label to put at start of line\n", " |      :type label: str\n", " |      :param fmt: conversion format for each number as used by ``format()``\n", " |      :type fmt: str\n", " |      :param label: text label to put at start of line\n", " |      :type label: str\n", " |      :param orient: 3-angle convention to use, optional, ``SO3`` and ``SE3``\n", " |                     only\n", " |      :type orient: str\n", " |      :param unit: angular units: 'rad' [default], or 'deg'\n", " |      :type unit: str\n", " |      :return: pose in string format\n", " |      :rtype: str\n", " |      \n", " |      Convert pose in a compact single line format. If ``X`` has multiple\n", " |      values, the string has one pose per line.\n", " |      \n", " |      Orientation can be displayed in various formats:\n", " |      \n", " |      =============   =================================================\n", " |      ``orient``      description\n", " |      =============   =================================================\n", " |      ``'rpy/zyx'``   roll-pitch-yaw angles in ZYX axis order [default]\n", " |      ``'rpy/yxz'``   roll-pitch-yaw angles in YXZ axis order\n", " |      ``'rpy/zyx'``   roll-pitch-yaw angles in ZYX axis order\n", " |      ``'eul'``       Euler angles in ZYZ axis order\n", " |      ``'angvec'``    angle and axis\n", " |      =============   =================================================\n", " |      \n", " |      Example:\n", " |      \n", " |      .. runblock:: pycon\n", " |      \n", " |          >>> from spatialmath import SE2, SE3\n", " |          >>> x = SE3.Rx(0.3)\n", " |          >>> x.strline()\n", " |          >>> x = SE3.Rx([0.2, 0.3])\n", " |          >>> x.strline()\n", " |          >>> x.strline('angvec')\n", " |          >>> x.strline(orient='angvec', fmt=\"{:.6f}\")\n", " |          >>> x = SE2(1, 2, 0.3)\n", " |          >>> x.strline()\n", " |      \n", " |      .. note::\n", " |          - Default formatting is for compact display of data\n", " |          - For tabular data set ``fmt`` to a fixed width format such as\n", " |            ``fmt='{:.3g}'``\n", " |      \n", " |      :seealso: :meth:`printline` :func:`trprint`, :func:`trprint2`\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Static methods inherited from spatialmath.baseposematrix.BasePoseMatrix:\n", " |  \n", " |  __new__(cls, *args, **kwargs)\n", " |      Create the subclass instance (superclass method)\n", " |      \n", " |      Create a new instance and call the superclass initializer to enable the\n", " |      ``UserList`` capabilities.\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Readonly properties inherited from spatialmath.baseposematrix.BasePoseMatrix:\n", " |  \n", " |  N\n", " |      Dimension of the object's group (superclass property)\n", " |      \n", " |      :return: dimension\n", " |      :rtype: int\n", " |      \n", " |      Dimension of the group is 2 for ``SO2`` or ``SE2``, and 3 for ``SO3`` or ``SE3``.\n", " |      This corresponds to the dimension of the space, 2D or 3D, to which these\n", " |      rotations or rigid-body motions apply.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> SE3().N\n", " |          3\n", " |          >>> SE2().N\n", " |          2\n", " |  \n", " |  about\n", " |      Succinct summary of object type and length (superclass property)\n", " |      \n", " |      :return: succinct summary\n", " |      :rtype: str\n", " |      \n", " |      Displays the type and the number of elements in compact form, for\n", " |      example::\n", " |      \n", " |          >>> x = SE3([SE3() for i in range(20)])\n", " |          >>> len(x)\n", " |          20\n", " |          >>> print(x.about)\n", " |          SE3[20]\n", " |  \n", " |  isSE\n", " |      Test if object belongs to SE(n) group (superclass property)\n", " |      \n", " |      :param self: object to test\n", " |      :type self: SO2, SE2, SO3, SE3 instance\n", " |      :return: ``True`` if object is instance of SE2 or SE3\n", " |      :rtype: bool\n", " |  \n", " |  isSO\n", " |      Test if object belongs to SO(n) group (superclass property)\n", " |      \n", " |      :param self: object to test\n", " |      :type self: SO2, SE2, SO3, SE3 instance\n", " |      :return: ``True`` if object is instance of SO2 or SO3\n", " |      :rtype: bool\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Data and other attributes inherited from spatialmath.baseposematrix.BasePoseMatrix:\n", " |  \n", " |  __array_ufunc__ = None\n", " |  \n", " |  __hash__ = None\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Methods inherited from spatialmath.baseposelist.BasePoseList:\n", " |  \n", " |  __ge__(self, other: 'BasePoseList') -> 'Type[Exception]'\n", " |      Return self>=value.\n", " |  \n", " |  __getitem__(self, i: 'Union[int, slice]') -> 'BasePoseList'\n", " |      Access value of an instance (BasePoseList superclass method)\n", " |      \n", " |      :param i: index of element to return\n", " |      :type i: int\n", " |      :return: the specific element of the pose\n", " |      :rtype: Quaternion or UnitQuaternion instance\n", " |      :raises IndexError: if the element is out of bounds\n", " |      \n", " |      Note that only a single index is supported, slices are not.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> x = <PERSON>.<PERSON>(10)\n", " |          >>> len(x)\n", " |          10\n", " |          >>> y = x[1]\n", " |          >>> len(y)\n", " |          1\n", " |          >>> y = x[1:5]\n", " |          >>> len(y)\n", " |          4\n", " |      \n", " |      where ``X`` is any of the SMTB classes.\n", " |  \n", " |  __gt__(self, other: 'BasePoseList') -> 'Type[Exception]'\n", " |      Return self>value.\n", " |  \n", " |  __le__(self, other: 'BasePoseList') -> 'Type[Exception]'\n", " |      Return self<=value.\n", " |  \n", " |  __lt__(self, other: 'BasePoseList') -> 'Type[Exception]'\n", " |      Return self<value.\n", " |  \n", " |  __setitem__(self, i: 'int', value: 'BasePoseList') -> 'None'\n", " |      Assign a value to an instance (BasePoseList superclass method)\n", " |      \n", " |      :param i: index of element to assign to\n", " |      :type i: int\n", " |      :param value: the value to insert\n", " |      :type value: Quaternion or UnitQuaternion instance\n", " |      :raises ValueError: incorrect type of assigned value\n", " |      \n", " |      Assign the argument to an element of the object's internal list of values.\n", " |      This supports the assignement operator, for example::\n", " |      \n", " |          >>> x = <PERSON>.<PERSON>(10)\n", " |          >>> len(x)\n", " |          10\n", " |          >>> x[3] = X()   # assign to position 3 in the list\n", " |      \n", " |      where ``X`` is any of the SMTB classes.\n", " |  \n", " |  append(self, item: 'BasePoseList') -> 'None'\n", " |      Append a value to an instance (BasePoseList superclass method)\n", " |      \n", " |      :param x: the value to append\n", " |      :type x: Quaternion or UnitQuaternion instance\n", " |      :raises ValueError: incorrect type of appended object\n", " |      \n", " |      Appends the argument to the object's internal list of values.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> x = <PERSON>.<PERSON>(10)\n", " |          >>> len(x)\n", " |          10\n", " |          >>> x.append(X())   # append to the list\n", " |          >>> len(x)\n", " |          11\n", " |      \n", " |      where ``X`` is any of the SMTB classes.\n", " |  \n", " |  arghandler(self, arg: 'Any', convertfrom: 'Tuple' = (), check: 'Optional[bool]' = True) -> 'bool'\n", " |      Standard constructor support (BasePoseList superclass method)\n", " |      \n", " |      :param arg: initial value\n", " |      :param convertfrom: list of classes to accept and convert from\n", " |      :type: tuple of typles\n", " |      :param check: check value is valid, defaults to True\n", " |      :type check: bool\n", " |      :raises ValueError: bad type passed\n", " |      \n", " |      The value ``arg`` can be any of:\n", " |      \n", " |      #. None, an identity value is created\n", " |      #. a numpy.ndarray of the appropriate shape and value which is valid for the subclass\n", " |      #. a list whose elements all meet the criteria above\n", " |      #. an instance of the subclass\n", " |      #. a list whose elements are all singelton instances of the subclass\n", " |      \n", " |      For cases 2 and 3, a NumPy array or a list of NumPy array is passed.\n", " |      Each NumPyarray is tested for validity (if ``check`` is False a cursory\n", " |      check of shape is made, if ``check`` is True the numerical value is\n", " |      inspected) and converted to the required internal format by the\n", " |      ``_import`` method. The default ``_import`` method calls the ``isvalid``\n", " |      method for checking.  This mechanism allows equivalent forms to be\n", " |      passed, ie. 6x1 or 4x4 for an se(3).\n", " |      \n", " |      If ``self`` is an instance of class ``A``, and an instance of class\n", " |      ``B`` is passed and ``B`` is an element of the ``convertfrom`` argument,\n", " |      then ``B.A()`` will be invoked to perform the type conversion.\n", " |      \n", " |      Examples::\n", " |      \n", " |          SE3()\n", " |          SE3(np.identity(4))\n", " |          SE3([np.identity(4), np.identity(4)])\n", " |          SE3(SE3())\n", " |          SE3([SE3(), SE3()])\n", " |          Twist3(SE3())\n", " |  \n", " |  binop(self, right: 'BasePoseList', op: 'Callable', op2: 'Optional[Callable]' = None, list1: 'Optional[bool]' = True) -> 'List'\n", " |      Perform binary operation\n", " |      \n", " |      :param left: left operand\n", " |      :type left: BasePoseList subclass\n", " |      :param right: right operand\n", " |      :type right: BasePoseList subclass, scalar or array\n", " |      :param op: binary operation\n", " |      :type op: callable\n", " |      :param op2: binary operation\n", " |      :type op2: callable\n", " |      :param list1: return single array as a list, default True\n", " |      :type list1: bool\n", " |      :raises ValueError: arguments are not compatible\n", " |      :return: list of values\n", " |      :rtype: list\n", " |      \n", " |      The is a helper method for implementing binary operation with overloaded\n", " |      operators such as ``X * Y`` where ``X`` and ``Y`` are both subclasses\n", " |      of ``BasePoseList``.  Each operand has a list of one or more\n", " |      values and this methods computes a list of result values according to:\n", " |      \n", " |      =========   ==========   ====  ===================================\n", " |            Inputs                    Output\n", " |      ----------------------   -----------------------------------------\n", " |      len(left)   len(right)   len     operation\n", " |      =========   ==========   ====  ===================================\n", " |       1          1             1    ``ret = op(left, right)``\n", " |       1          M             M    ``ret[i] = op(left, right[i])``\n", " |       M          1             M    ``ret[i] = op(left[i], right)``\n", " |       M          M             M    ``ret[i] = op(left[i], right[i])``\n", " |      =========   ==========   ====  ===================================\n", " |      \n", " |      The arguments to ``op`` are the internal numeric values, ie. as returned\n", " |      by the ``._A`` property.\n", " |      \n", " |      The result is always a list, except for the first case above and\n", " |      ``list1`` is ``False``.\n", " |      \n", " |      If the right operand is not a ``BasePoseList`` subclass, but is a numeric\n", " |      scalar or array then then ``op2`` is invoked\n", " |      \n", " |      For example::\n", " |      \n", " |          X._binop(Y, lambda x, y: x + y)\n", " |      \n", " |      =========   ====  ===================================\n", " |        Input                    Output\n", " |      ---------   -----------------------------------------\n", " |      len(left)   len     operation\n", " |      =========   ====  ===================================\n", " |       1           1    ``ret = op2(left, right)``\n", " |       M           M    ``ret[i] = op2(left[i], right)``\n", " |      =========   ====  ===================================\n", " |      \n", " |      There is no check on the shape of ``right`` if it is an array.\n", " |      The result is always a list, except for the first case above and\n", " |      ``list1`` is ``False``.\n", " |  \n", " |  extend(self, iterable: 'BasePoseList') -> 'None'\n", " |      Extend sequence of values in an instance (BasePoseList superclass method)\n", " |      \n", " |      :param x: the value to extend\n", " |      :type x: instance of same type\n", " |      :raises ValueError: incorrect type of appended object\n", " |      \n", " |      Appends the argument's values to the object's internal list of values.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> x = <PERSON>.<PERSON>(10)\n", " |          >>> len(x)\n", " |          10\n", " |          >>> x.append(<PERSON><PERSON>(5))   # extend the list\n", " |          >>> len(x)\n", " |          15\n", " |      \n", " |      where ``X`` is any of the SMTB classes.\n", " |  \n", " |  insert(self, i: 'int', item: 'BasePoseList') -> 'None'\n", " |      Insert a value to an instance (BasePoseList superclass method)\n", " |      \n", " |      :param i: element to insert value before\n", " |      :type i: int\n", " |      :param item: the value to insert\n", " |      :type item: instance of same type\n", " |      :raises ValueError: incorrect type of inserted value\n", " |      \n", " |      Inserts the argument into the object's internal list of values.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> x = <PERSON>.<PERSON>(10)\n", " |          >>> len(x)\n", " |          10\n", " |          >>> x.insert(0, X())   # insert at start of list\n", " |          >>> len(x)\n", " |          11\n", " |          >>> x.insert(10, X())   # append to the list\n", " |          >>> len(x)\n", " |          11\n", " |      \n", " |      where ``X`` is any of the SMTB classes.\n", " |      \n", " |      .. note:: If ``i`` is beyond the end of the list, the item is appended\n", " |          to the list\n", " |  \n", " |  pop(self, i: 'Optional[int]' = -1) -> 'Self'\n", " |      Pop value from an instance (BasePoseList superclass method)\n", " |      \n", " |      :param i: item in the list to pop, default is last\n", " |      :type i: int\n", " |      :return: the popped value\n", " |      :rtype: instance of same type\n", " |      :raises IndexError: if there are no values to pop\n", " |      \n", " |      Removes a value from the value list and returns it.  The original\n", " |      instance is modified.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> x = <PERSON>.<PERSON>(10)\n", " |          >>> len(x)\n", " |          10\n", " |          >>> y = x.pop()  # pop the last value x[9]\n", " |          >>> len(x)\n", " |          9\n", " |          >>> y = x.pop(0)  # pop the first value x[0]\n", " |          >>> len(x)\n", " |          8\n", " |      \n", " |      where ``X`` is any of the SMTB classes.\n", " |  \n", " |  unop(self, op: 'Callable', matrix: 'Optional[bool]' = False) -> 'Union[NDArray, List]'\n", " |      Perform unary operation\n", " |      \n", " |      :param self: operand\n", " |      :type self: BasePoseList subclass\n", " |      :param op: unnary operation\n", " |      :type op: callable\n", " |      :param matrix: return array instead of list, default False\n", " |      :type matrix: bool\n", " |      :return: operation results\n", " |      :rtype: list or NumPy array\n", " |      \n", " |      The is a helper method for implementing unary operations where the\n", " |      operand has multiple value. This method computes the value of\n", " |      the operation for all input values and returns the result as either\n", " |      a list or as a matrix which vertically stacks the results.\n", " |      \n", " |      =========   ====  ===================================\n", " |        Input                     Output\n", " |      ---------   -----------------------------------------\n", " |      len(self)   len     operation\n", " |      =========   ====  ===================================\n", " |       1           1    ``ret = op(self)``\n", " |       M           M    ``ret[i] = op(self[i])``\n", " |       M           M    ``ret[i,;] = op(self[i])``\n", " |      =========   ====  ===================================\n", " |      \n", " |      The result is:\n", " |      \n", " |      - a list of values if ``matrix==False``, or\n", " |      - a 2D NumPy stack of values if ``matrix==True``, it is assumed\n", " |        that the value is a 1D array.\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Class methods inherited from spatialmath.baseposelist.BasePoseList:\n", " |  \n", " |  Alloc(n: 'Optional[int]' = 1) -> 'Self' from abc.ABCMeta\n", " |      Construct an instance with N default values (BasePoseList superclass method)\n", " |      \n", " |      :param n: Number of values, defaults to 1\n", " |      :type n: int, optional\n", " |      :return: pose instance with ``n`` default values\n", " |      \n", " |      ``<PERSON><PERSON>(<PERSON>)`` creates an instance of the pose class ``X`` with ``N``\n", " |      default values, ie. ``len(X)`` will be ``N``.\n", " |      \n", " |      ``X`` can be considered a vector of pose objects, and those elements\n", " |      can be referenced ``X[i]`` or assigned to ``X[i] = ...``.\n", " |      \n", " |      .. note:: The default value depends on the pose class and is the result\n", " |                of the empty constructor. For ``SO2``,\n", " |                ``SE2``, ``SO3``, ``SE3`` it is an identity matrix, for a\n", " |                twist class ``Twist2`` or ``Twist3`` it is a zero vector,\n", " |                for a ``UnitQuaternion`` or ``Quaternion`` it is a zero\n", " |                vector.\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> x = <PERSON>.<PERSON>(10)\n", " |          >>> len(x)\n", " |          10\n", " |      \n", " |      where ``X`` is any of the SMTB classes.\n", " |  \n", " |  Empty() -> 'Self' from abc.ABCMeta\n", " |      Construct an empty instance (BasePoseList superclass method)\n", " |      \n", " |      :return: pose instance with zero values\n", " |      \n", " |      Example::\n", " |      \n", " |          >>> x = X.Empty()\n", " |          >>> len(x)\n", " |          0\n", " |      \n", " |      where ``X`` is any of the SMTB classes.\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Readonly properties inherited from spatialmath.baseposelist.BasePoseList:\n", " |  \n", " |  A\n", " |      Array value of an instance (BasePoseList superclass method)\n", " |      \n", " |      :return: NumPy array value of this instance\n", " |      :rtype: n<PERSON><PERSON>\n", " |      \n", " |      - ``X.A`` is a NumPy array that represents the value of this instance,\n", " |        and has a shape given by ``X.shape``.\n", " |      \n", " |      .. note:: This assumes that ``len(X)`` == 1, ie. it is a single-valued\n", " |          instance.\n", " |  \n", " |  __array_interface__\n", " |      Copies the numpy array interface from the first numpy array\n", " |      so that C extenstions with this spatial math class have direct\n", " |      access to the underlying numpy array\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Methods inherited from collections.UserList:\n", " |  \n", " |  __contains__(self, item)\n", " |  \n", " |  __copy__(self)\n", " |  \n", " |  __delitem__(self, i)\n", " |  \n", " |  __len__(self)\n", " |  \n", " |  clear(self)\n", " |      S.clear() -> None -- remove all items from S\n", " |  \n", " |  copy(self)\n", " |  \n", " |  count(self, item)\n", " |      S.count(value) -> integer -- return number of occurrences of value\n", " |  \n", " |  index(self, item, *args)\n", " |      S.index(value, [start, [stop]]) -> integer -- return first index of value.\n", " |      Raises ValueError if the value is not present.\n", " |      \n", " |      Supporting start and stop arguments is optional, but\n", " |      recommended.\n", " |  \n", " |  remove(self, item)\n", " |      S.remove(value) -- remove first occurrence of value.\n", " |      Raise ValueError if the value is not present.\n", " |  \n", " |  reverse(self)\n", " |      S.reverse() -- reverse *IN PLACE*\n", " |  \n", " |  sort(self, /, *args, **kwds)\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Data descriptors inherited from collections.UserList:\n", " |  \n", " |  __dict__\n", " |      dictionary for instance variables (if defined)\n", " |  \n", " |  __weakref__\n", " |      list of weak references to the object (if defined)\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Methods inherited from collections.abc.Sequence:\n", " |  \n", " |  __iter__(self)\n", " |  \n", " |  __reversed__(self)\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Class methods inherited from collections.abc.Reversible:\n", " |  \n", " |  __subclasshook__(C) from abc.ABCMeta\n", " |      Abstract classes can override this to customize issubclass().\n", " |      \n", " |      This is invoked early on by abc.ABCMeta.__subclasscheck__().\n", " |      It should return True, False or NotImplemented.  If it returns\n", " |      NotImplemented, the normal algorithm is used.  Otherwise, it\n", " |      overrides the normal algorithm (and the outcome is cached).\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Class methods inherited from collections.abc.Iterable:\n", " |  \n", " |  __class_getitem__ = GenericAlias(...) from abc.ABCMeta\n", " |      Represent a PEP 585 generic type\n", " |      \n", " |      E.g. for t = list[int], t.__origin__ is list and t.__args__ is (int,).\n", "\n"]}], "source": ["help(SE2)"]}, {"cell_type": "code", "execution_count": 46, "id": "75c7b9de", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 0.5     \u001b[0m \u001b[38;5;1m-0.866   \u001b[0m \u001b[38;5;4m 1       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.866   \u001b[0m \u001b[38;5;1m 0.5     \u001b[0m \u001b[38;5;4m 2       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["T=SE2(1, 2, 60, unit='deg')\n", "T"]}, {"cell_type": "code", "execution_count": 45, "id": "faebc156", "metadata": {}, "outputs": [], "source": ["initial_point=np.array([[0],[0],[1]])"]}, {"cell_type": "code", "execution_count": 42, "id": "33e6cd28", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0],\n", "       [0],\n", "       [1]])"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["initial_point"]}, {"cell_type": "code", "execution_count": 43, "id": "d7e221ff", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3, 1)"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["initial_point.shape"]}, {"cell_type": "code", "execution_count": 47, "id": "d453b41b", "metadata": {}, "outputs": [], "source": ["post_point=np.array(T).dot(initial_point)"]}, {"cell_type": "code", "execution_count": 48, "id": "574cc951", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1.],\n", "       [2.],\n", "       [1.]])"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["post_point"]}, {"cell_type": "code", "execution_count": 49, "id": "6003e321", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[-4.44089210e-16],\n", "       [ 8.32667268e-17],\n", "       [ 1.00000000e+00]])"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["from numpy.linalg import inv\n", "inv(np.array(T)).dot(post_point)"]}, {"cell_type": "markdown", "id": "8ba2238a", "metadata": {}, "source": ["SO和transl2 仅旋转和仅平移"]}, {"cell_type": "code", "execution_count": 50, "id": "0fad2756", "metadata": {}, "outputs": [], "source": ["T=SO2(np.pi/3)"]}, {"cell_type": "code", "execution_count": 51, "id": "5eed2bc9", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 0.5     \u001b[0m \u001b[38;5;1m-0.866   \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.866   \u001b[0m \u001b[38;5;1m 0.5     \u001b[0m  \u001b[0m\n"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["T"]}, {"cell_type": "code", "execution_count": 52, "id": "80306716", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 2       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["from spatialmath.base import *\n", "T=SE2.Tx(2)\n", "T"]}, {"cell_type": "code", "execution_count": 53, "id": "665d90ca", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 2       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["from spatialmath.base import *\n", "T=SE2.<PERSON>(2)\n", "T"]}, {"cell_type": "code", "execution_count": 2, "id": "1a6ac1cb", "metadata": {}, "outputs": [], "source": ["from spatialmath.base import *\n", "T=transl2([1,2])"]}, {"cell_type": "code", "execution_count": 55, "id": "fd62d7ed", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1., 0., 1.],\n", "       [0., 1., 2.],\n", "       [0., 0., 1.]])"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["T"]}, {"cell_type": "markdown", "id": "bbb8e976", "metadata": {}, "source": ["3维变换"]}, {"cell_type": "code", "execution_count": 5, "id": "fedc697e", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0.5     \u001b[0m \u001b[38;5;1m-0.866   \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0.866   \u001b[0m \u001b[38;5;1m 0.5     \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["T=SE3.Rx(np.pi/3)\n", "T"]}, {"cell_type": "code", "execution_count": 57, "id": "8b4d7eae", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 1.       ,  0.       ,  0.       ],\n", "       [ 0.       ,  0.5      , -0.8660254],\n", "       [ 0.       ,  0.8660254,  0.5      ]])"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["T=rotx(np.pi/3)\n", "T"]}, {"cell_type": "code", "execution_count": 58, "id": "3995c908", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m-1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["T=SE3.Ry(np.pi/2)\n", "T"]}, {"cell_type": "code", "execution_count": 19, "id": "370af71e", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-1       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["T=SE3.Rx(np.pi)\n", "T"]}, {"cell_type": "code", "execution_count": 20, "id": "6e4e9093", "metadata": {}, "outputs": [], "source": ["T.plot()"]}, {"cell_type": "code", "execution_count": 16, "id": "dd80f9b1", "metadata": {}, "outputs": [], "source": ["T=np.array(T)\n", "T[2][3]=0.02\n"]}, {"cell_type": "code", "execution_count": 18, "id": "224dddef", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0.  ],\n", "       [0.  ],\n", "       [0.02],\n", "       [1.  ]])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["T@(np.array([0,0,0,1]).reshape(4,1))"]}, {"cell_type": "code", "execution_count": 28, "id": "0cc1ff21", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.1     \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["T1=SE3.Rx(np.pi/2,t=np.array([0.1,0,0]))\n", "T1\n", "T2=SE3.Ry(np.pi/2,t=np.array([0,0.1,0]))\n", "T2\n", "T3=SE3.Rz(np.pi/2,t=np.array([0,0,0.1]))\n", "T3"]}, {"cell_type": "code", "execution_count": 60, "id": "dcf70c4c", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m-0.1     \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m-1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["#3个叠加，注意是按固定坐标轴旋转，左乘\n", "T=T3*T2*T1\n", "T"]}, {"cell_type": "code", "execution_count": 61, "id": "9f8c65e1", "metadata": {}, "outputs": [], "source": ["T.plot()"]}, {"cell_type": "code", "execution_count": 64, "id": "9cf171e2", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.animation.FuncAnimation at 0x13ca156d0>"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["X = T1\n", "X.animate(frame='A', color='green')\n", "X.animate(start=None)"]}, {"cell_type": "code", "execution_count": 63, "id": "a198165d", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.animation.FuncAnimation at 0x1416cab10>"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["X = T2*T1\n", "X.animate(frame='A', color='green')\n", "X.animate(start=None)\n"]}, {"cell_type": "code", "execution_count": 65, "id": "0622e488", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.animation.FuncAnimation at 0x1418437d0>"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["X = T1*T2\n", "X.animate(frame='A', color='green')\n", "X.animate(start=None)\n"]}, {"cell_type": "code", "execution_count": 66, "id": "cfeb6c87", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.animation.FuncAnimation at 0x14181f410>"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["X = T3*T2*T1\n", "X.animate(frame='A', color='green')\n", "X.animate(start=None)"]}, {"cell_type": "code", "execution_count": 67, "id": "0d796001", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m-0.1     \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m-1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["T"]}, {"cell_type": "code", "execution_count": 68, "id": "9552cf7f", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.animation.FuncAnimation at 0x1419674d0>"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["from spatialmath.base import animate\n", "anim = animate.Animate(dims=[-1,1,-1,1]) # set up the 3D axes\n", "anim.trplot(np.array(T), frame='A', color='green')  # draw the frame\n", "anim.run(loop=True)  # animate it"]}, {"cell_type": "code", "execution_count": null, "id": "0b6f8b58", "metadata": {}, "outputs": [], "source": ["SO3,rotx"]}, {"cell_type": "code", "execution_count": null, "id": "c5dc38a1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fb52e07f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}