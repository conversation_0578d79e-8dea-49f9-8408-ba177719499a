from roboticstoolbox import *
from spatialmath import *
from math import pi
import numpy as np
import torch
import torch.nn as nn #导入神经模块
import torch.nn.functional as F
import torch.optim as optim

#夹爪
DFbot2 = DHRobot(
    [
        RevoluteMDH(d=0.075, qlim=np.array([-np.pi*2/3, np.pi*2/3])),
        RevoluteMDH(alpha=np.pi / 2, qlim=np.array([-np.pi/2, np.pi/2]),offset=-np.pi/2),
        RevoluteMDH(a=-0.1, qlim=np.array([-np.pi*2/3, np.pi*2/3])),
        RevoluteMDH(a=-0.095, qlim=np.array([-np.pi/2, np.pi/2]),offset=np.pi/2),
        RevoluteMDH(alpha=-np.pi / 2, d=0.06, qlim=np.array([-np.pi*2/3, np.pi*2/3]))

    ],
    name="DFbot",
)

print(DFbot2.qlim.T)

n_dof=5

def get_random_q(robot, batch_size, max_inc=0.2):
    """Get 2 random configurations inside the robot limits at a max_inc distance"""
    q_init = np.random.rand(batch_size, n_dof)
    q_init = q_init * (robot.qlim.T[:,1] - robot.qlim.T[:,0]) + robot.qlim.T[:,0]
    q_inc = (np.random.rand(batch_size, n_dof) * 2 - 1) * max_inc
    q_final = np.clip(q_init + q_inc, robot.qlim.T[:,0], robot.qlim.T[:,1])
    return q_init, q_final 

get_random_q(DFbot2, 3, max_inc=0.2)

def get_batch(robot, batch_size):
    """ Create a batch (q_init + euclidean_velocity) --> (configuration_velocity)"""
    q_init, q_final = get_random_q(robot, batch_size)
    ee_pose_init = np.zeros((batch_size, 3 * 4))#旋转矩阵，和位置
    ee_pose_final = np.zeros((batch_size, 3 * 4))
    for j, init in enumerate(q_init):
        se_init = DFbot2.fkine(init)
        ee_pose_init[j] = np.array(se_init)[0:3, :].flatten()
    for j, final in enumerate(q_final):
        se_final = DFbot2.fkine(final)
        ee_pose_final[j] = np.array(se_final)[0:3, :].flatten()

    return np.hstack((q_init, ee_pose_final - ee_pose_init)), q_final - q_init

get_batch(DFbot2, 1)

def get_dataset(robot, batch_size, num_batchs):
    xt = np.empty((num_batchs, batch_size, 3*4 + n_dof))
    yt = np.empty((num_batchs, batch_size, n_dof))
    for i in range(num_batchs):
        x, y = get_batch(robot, batch_size)
        xt[i] = x
        yt[i] = y
        if (i+1)%int(num_batchs/10) == 0:
            print("{}%".format(((i+1)/num_batchs)*100))
    return xt, yt

device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
print("Device: {}".format(device))
x, y = get_dataset(DFbot2, 20, 50) #训练次数 20 * 50，基本需要5万左右，
x_t = torch.tensor(x).float().to(device)
y_t = torch.tensor(y).float().to(device)



layer_size = 100

#神经网络的构建
class Net(nn.Module):
    def __init__(self, robot):
        super().__init__()
        self.fc1 = nn.Linear(n_dof + 3*4, layer_size) #第一个元素，是训练的神经元个数
        self.fc2 = nn.Linear(layer_size, layer_size)#隐藏层
        self.fc3 = nn.Linear(layer_size, n_dof)
    def forward(self, x):
        x = F.relu(self.fc1(x))              #设置两层激活函数
        x = F.relu(self.fc2(x))
        x = self.fc3(x)
        return x


def train_model(xt, yt, epochs, net, optimizer):
    net.train()
    loss_log = []
    for epoch in range(epochs):
        running_loss = 0.0

        for x, y in zip(xt, yt):
            # zero the parameter gradients
            optimizer.zero_grad()

            # forward
            # track history if only in train
            with torch.set_grad_enabled(True):
                outputs = net(x)
                loss = criterion(outputs, y)

                # backward + optimize only if in training phase
                loss.backward()
                optimizer.step()#反向传播

                # statistics
                running_loss += loss.item() * x_t.size(0)
        print('[%d, %5d] loss: %.3f' % (epoch + 1, len(x_t), running_loss / len(x_t)))
        loss_log.append(running_loss / len(x_t))
    print('Finished Training')
    return loss_log

net = Net(DFbot2)
net.to(device)
criterion = nn.MSELoss()
optimizer = torch.optim.Adam(net.parameters(), lr=0.001)

model_parameters = filter(lambda p: p.requires_grad, net.parameters())
params = sum([np.prod(p.size()) for p in model_parameters])
print("Trainable parameters: ", params)
log = train_model(x_t, y_t, 100, net, optimizer)
#验证结果
def evaluate(robot, net, device, N=5000):
    error = np.zeros((N, 4*4))
    agg_error=[]
    net.eval()
    for i in range(N):
        x, y = get_batch(robot, 1)
        x_t = torch.tensor(x).float().to(device)
        out = net(x_t)
        y = y.flatten()
        y_out = out[0].cpu().detach().numpy()
        ee_net = robot.fkine(y_out)
        ee_in = robot.fkine(y)
        err_i = np.abs(ee_net - ee_in)
        error[i] = err_i.flatten()
        agg_error.append( sum([abs(x) for x in error[i]]))
    return agg_error,error
#验证误差结果
agg_error,error=evaluate(DFbot2, net, device, N=5000)


x, y = get_batch(DFbot2, 1)
x_t = torch.tensor(x).float().to(device)
out = net(x_t)
y = y.flatten()
y_out = out[0].cpu().detach().numpy()
ee_net = DFbot2.fkine(x[0][0:5]+y_out)
ee_in = DFbot2.fkine(x[0][0:5]+y)
err_i = ee_net - ee_in