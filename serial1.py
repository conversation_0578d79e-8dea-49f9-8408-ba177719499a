# serial_comm.py

from serial import Serial


class SerialComm:
    def __init__(self, port='COM11', baudrate=9600, timeout=1):
        """
        初始化串口通信。
        :param port: 串口号，默认为'COM11'
        :param baudrate: 波特率，默认为9600
        :param timeout: 超时时间，默认为1秒
        """
        self.ser = Serial(port=port, baudrate=baudrate, timeout=timeout)

    def send_data(self, data):
        """
        发送数据函数。
        :param data: 要发送的数据列表或元组。
        """
        if len(data) < 1:
            print("错误：数据不能为空")
            return

        # 构造要发送的数据帧
        frame = [0xFF]  # 包头
        frame.extend(data)  # 添加实际数据
        frame.append(0xFE)  # 包尾

        try:
            # 将数据写入串行端口
            self.ser.write(bytes(frame))
            print(f"发送成功: {frame}")
        except Exception as e:
            print(f"发送失败: {e}")

    def close(self):
        """关闭串口连接"""
        self.ser.close()