{"cells": [{"cell_type": "code", "execution_count": 1, "id": "60847901-0b26-42aa-8b30-9a752fe64b73", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import cv2"]}, {"cell_type": "code", "execution_count": null, "id": "1a956271-9a40-440a-9a58-15d3605a1ab7", "metadata": {}, "outputs": [], "source": ["\n", "ball_color = 'red'\n", "\n", "color_dist = {'red': {'Lower': np.array([150, 43, 46]), 'Upper': np.array([180, 255, 255])},\n", "'blue': {'Lower': np.array([100, 100, 100]), 'Upper': np.array([124, 255, 255])},\n", "'green': {'Lower': np.array([35, 43, 35]), 'Upper': np.array([90, 255, 255])},\n", "}\n", "\n", "cap = cv2.VideoCapture(0)\n", "cv2.namedW<PERSON>ow('camera', cv2.WINDOW_AUTOSIZE)\n", "\n", "while cap.isOpened():\n", "    ret, frame = cap.read()\n", "    if ret:\n", "        if frame is not None:\n", "            gs_frame = cv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(frame, (5, 5), 0) # 高斯模糊\n", "            hsv = cv2.cvtColor(gs_frame, cv2.COLOR_BGR2HSV) # 转化成HSV图像\n", "            erode_hsv = cv2.erode(hsv, None, iterations=2) # 腐蚀\n", "            inRange_hsv = cv2.inRange(erode_hsv, color_dist[ball_color]['Lower'], color_dist[ball_color]['Upper'])\n", "            cnts = cv2.findContours(inRange_hsv.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[-2]\n", "            try:\n", "                c = max(cnts, key=cv2.contourArea)\n", "                rect = cv2.minAreaRect(c)\n", "                box = cv2.boxPoints(rect)\n", "                cv2.drawContours(frame, [np.int0(box)], -1, (0, 255, 255), 2)\n", "                cv2.putText(frame, str(np.int0(box[0][0]))+','+str(np.int0(box[0][1])),(np.int0(box[0][0]),np.int0(box[0][1])),cv2.FONT_HERSHEY_COMPLEX_SMALL,0.75,(0,0,255),1)\n", "                cv2.putText(frame, str(np.int0(box[1][0]))+','+str(np.int0(box[1][1])),(np.int0(box[1][0]),np.int0(box[1][1])),cv2.FONT_HERSHEY_COMPLEX_SMALL,0.75,(0,0,255),1)\n", "                cv2.putText(frame, str(np.int0(box[2][0]))+','+str(np.int0(box[2][1])),(np.int0(box[2][0]),np.int0(box[2][1])),cv2.FONT_HERSHEY_COMPLEX_SMALL,0.75,(0,0,255),1)\n", "                cv2.putText(frame, str(np.int0(box[3][0]))+','+str(np.int0(box[3][1])),(np.int0(box[3][0]),np.int0(box[3][1])),cv2.FONT_HERSHEY_COMPLEX_SMALL,0.75,(0,0,255),1)\n", "                cv2.putText(frame, str(np.int0(box[3][0]/2+box[1][0]/2))+','+str(np.int0(box[3][1]/2+box[1][1]/2)),(np.int0(box[3][0]/2+box[1][0]/2),np.int0(box[3][1]/2+box[1][1]/2)),cv2.FONT_HERSHEY_COMPLEX_SMALL,0.75,(0,0,255),1)           \n", "                cv2.imshow('camera', frame)\n", "                cv2.<PERSON><PERSON><PERSON>(1)\n", "            except:\n", "                cv2.imshow('camera', frame)\n", "                cv2.<PERSON><PERSON><PERSON>(1)\n", "                pass\n", "        else:\n", "            print(\"无画面\")\n", "    else:\n", "        print(\"无法读取摄像头！\")\n", "\n", "cap.release()\n", "cv2.<PERSON><PERSON><PERSON>(0)\n", "cv2.destroyAllWindows()"]}, {"cell_type": "code", "execution_count": 2, "id": "9bf7f56b-b96a-475d-bea5-d99a32a0d572", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on built-in function drawContours:\n", "\n", "drawContours(...)\n", "    drawContours(image, contours, contourIdx, color[, thickness[, lineType[, hierarchy[, maxLevel[, offset]]]]]) -> image\n", "    .   @brief Draws contours outlines or filled contours.\n", "    .   \n", "    .   The function draws contour outlines in the image if \\f$\\texttt{thickness} \\ge 0\\f$ or fills the area\n", "    .   bounded by the contours if \\f$\\texttt{thickness}<0\\f$ . The example below shows how to retrieve\n", "    .   connected components from the binary image and label them: :\n", "    .   @include snippets/imgproc_drawContours.cpp\n", "    .   \n", "    .   @param image Destination image.\n", "    .   @param contours All the input contours. Each contour is stored as a point vector.\n", "    .   @param contourIdx Parameter indicating a contour to draw. If it is negative, all the contours are drawn.\n", "    .   @param color Color of the contours.\n", "    .   @param thickness Thickness of lines the contours are drawn with. If it is negative (for example,\n", "    .   thickness=#FILLED ), the contour interiors are drawn.\n", "    .   @param lineType Line connectivity. See #LineTypes\n", "    .   @param hierarchy Optional information about hierarchy. It is only needed if you want to draw only\n", "    .   some of the contours (see maxLevel ).\n", "    .   @param maxLevel Maximal level for drawn contours. If it is 0, only the specified contour is drawn.\n", "    .   If it is 1, the function draws the contour(s) and all the nested contours. If it is 2, the function\n", "    .   draws the contours, all the nested contours, all the nested-to-nested contours, and so on. This\n", "    .   parameter is only taken into account when there is hierarchy available.\n", "    .   @param offset Optional contour shift parameter. Shift all the drawn contours by the specified\n", "    .   \\f$\\texttt{offset}=(dx,dy)\\f$ .\n", "    .   @note When thickness=#FILLED, the function is designed to handle connected components with holes correctly\n", "    .   even when no hierarchy data is provided. This is done by analyzing all the outlines together\n", "    .   using even-odd rule. This may give incorrect results if you have a joint collection of separately retrieved\n", "    .   contours. In order to solve this problem, you need to call #drawContours separately for each sub-group\n", "    .   of contours, or iterate over the collection using contourIdx parameter.\n", "\n"]}], "source": ["help(cv2.drawContours)"]}, {"cell_type": "code", "execution_count": null, "id": "8178023d-cf2b-4b73-bbf4-2977050c20e3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}