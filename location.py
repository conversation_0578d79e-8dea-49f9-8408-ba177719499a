import numpy as np
import math
from spatialmath import SE3
from spatialmath.base import e2h, h2e
import numpy as np
import cv2
import matplotlib.pyplot as plt
from machinevisiontoolbox import CentralCamera
from machinevisiontoolbox import mkcube
from roboticstoolbox import *
from camera_robot_add import extrinsic

# 创建一个CentralCamera实例
cam = CentralCamera(
    f=[641.43945303*10e-6, 663.28643594*10e-6], # 焦距，单位为米
    rho=10e-6, # 像素大小，单位为米
    imagesize=[640, 480], # 图像尺寸
    pp=[307.9468488, 231.53404289], # 主点位置
    name="mycamera"
)

# 打印相机对象信息
# print(cam)
#
# print(cam.K)

P = np.array([0,-0.28,0.0])

#相机
DFbot = DHRobot(
    [
        RevoluteMDH(d=0.065, qlim=np.array([-np.pi*2/3, np.pi*2/3]),offset=np.pi/2),
        RevoluteMDH(alpha=np.pi / 2, qlim=np.array([-np.pi/2, np.pi/2]),offset=-np.pi/2),
        RevoluteMDH(a=-0.1, qlim=np.array([-np.pi*2/3, np.pi*2/3])),
        RevoluteMDH(a=-0.095, qlim=np.array([-np.pi/2, np.pi/2]),offset=np.pi/2),
        RevoluteMDH(alpha=-np.pi/2,d=0.04, qlim=np.array([-np.pi/2, np.pi/2])),
        RevoluteMDH(a=0.05, qlim=np.array([-np.pi, np.pi]))

    ],
    name="DFbot",
)
T1=DFbot.fkine([0,np.pi/3,0,np.pi/2,0,0])

p = cam.project_point(P,pose=T1)
# print(p)
cam.plot_point(P,pose=T1)
DFbot.plot([0,np.pi/3,0,np.pi/2,0,0],block=True)
# X, Y ,Z = mkcube(s=0.03, centre=(0, -0.27, 0.015), edge=True)
# cam.plot_wireframe(X, Y, Z,pose=T1)
# plt.show()
# camera_matrix = cam.K
# dist_coeffs = np.zeros((4,1))  #假设没有畸变，直接投影，没有畸变
#
# object_points=[]
# image_points=[]
# for (x, y, z) in zip(X[0, 0:4], Y[0, 0:4], Z[0, 0:4]):
#     object_points.append([x, y, z])  # 改为直接添加列表而不是元组可能更合适，但这不是关键问题
#
# # 将 object_points 转换为 numpy 数组
# object_points = np.array(object_points, dtype=np.float32)
#
# for p in object_points:
#     P = cam.project_point(p, pose=T1)
#     image_points.append([P[0][0], P[1][0]])  # 同样地，这里也改为添加列表
#     # 将 image_points 转换为 numpy 数组
# image_points = np.array(image_points, dtype=np.float32)
# '''rvec旋转矩阵'''
# '''tvec平移矩阵'''
# #利用pnp算法求解外参
# _, rvec, tvec = cv2.solvePnP(object_points, image_points, camera_matrix,dist_coeffs)
# #将旋转向量转换为旋转矩阵
# R, _= cv2.Rodrigues(rvec)
# #构建外参矩阵
# extrinsic_matrix=np.hstack((R,tvec))
#
# print("Extrinsic Matrix:")
# print(extrinsic_matrix)
#
# T1ni = np.linalg.inv(T1)
#
# print(T1ni)