{"cells": [{"cell_type": "code", "execution_count": 54, "id": "4c6fad89", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import math\n", "from spatialmath import SE3\n", "from spatialmath.base import e2h, h2e\n", "from roboticstoolbox import *"]}, {"cell_type": "code", "execution_count": 55, "id": "56ff989a", "metadata": {}, "outputs": [], "source": ["#相机"]}, {"cell_type": "code", "execution_count": 56, "id": "b8da1891", "metadata": {}, "outputs": [], "source": ["DFbot1 = DHRobot(\n", "    [                 \n", "                    RevoluteMDH(d=0.04145,qlim=np.array([-np.pi,np.pi])),            \n", "                    RevoluteMDH(alpha=np.pi/2,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteMDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteMDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteMDH(alpha=-np.pi/2,qlim=np.array([0,np.pi])),\n", "                    RevoluteMDH(a=0.05,d=0.06,qlim=np.array([-np.pi,np.pi]))\n", "                  \n", "    ],\n", "    name=\"DF<PERSON>\",\n", ")"]}, {"cell_type": "code", "execution_count": 57, "id": "f0f055f2", "metadata": {}, "outputs": [], "source": ["#夹爪"]}, {"cell_type": "code", "execution_count": 58, "id": "f0ad69ab", "metadata": {}, "outputs": [], "source": ["DFbot2 = DHRobot(\n", "    [                 \n", "                    RevoluteMDH(d=0.04145,qlim=np.array([-np.pi,np.pi])),            \n", "                    RevoluteMDH(alpha=np.pi/2,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteMDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteMDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteMDH(alpha=-np.pi/2,d=0.11,qlim=np.array([-np.pi,np.pi]))\n", "                  \n", "    ],\n", "    name=\"DF<PERSON>\",\n", ")"]}, {"cell_type": "code", "execution_count": 59, "id": "0f794626", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m-0.866   \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-0.5     \u001b[0m \u001b[38;5;4m-0.2168  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.5     \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-0.866   \u001b[0m \u001b[38;5;4m 0.09734 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["T1=DFbot1.fkine([np.pi/2,-np.pi/6,0,np.pi,0,0])\n", "T1"]}, {"cell_type": "code", "execution_count": 64, "id": "48a22b89", "metadata": {}, "outputs": [{"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "/* global mpl */\n", "window.mpl = {};\n", "\n", "mpl.get_websocket_type = function () {\n", "    if (typeof WebSocket !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof MozWebSocket !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert(\n", "            'Your browser does not have WebSocket support. ' +\n", "                'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "                'Firefox 4 and 5 are also supported but you ' +\n", "                'have to enable WebSockets in about:config.'\n", "        );\n", "    }\n", "};\n", "\n", "mpl.figure = function (figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = this.ws.binaryType !== undefined;\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById('mpl-warnings');\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent =\n", "                'This browser does not support binary websocket messages. ' +\n", "                'Performance may be slow.';\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = document.createElement('div');\n", "    this.root.setAttribute('style', 'display: inline-block');\n", "    this._root_extra_style(this.root);\n", "\n", "    parent_element.appendChild(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen = function () {\n", "        fig.send_message('supports_binary', { value: fig.supports_binary });\n", "        fig.send_message('send_image_mode', {});\n", "        if (fig.ratio !== 1) {\n", "            fig.send_message('set_device_pixel_ratio', {\n", "                device_pixel_ratio: fig.ratio,\n", "            });\n", "        }\n", "        fig.send_message('refresh', {});\n", "    };\n", "\n", "    this.imageObj.onload = function () {\n", "        if (fig.image_mode === 'full') {\n", "            // Full images could contain transparency (where diff images\n", "            // almost always do), so we need to clear the canvas so that\n", "            // there is no ghosting.\n", "            fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "        }\n", "        fig.context.drawImage(fig.imageObj, 0, 0);\n", "    };\n", "\n", "    this.imageObj.onunload = function () {\n", "        fig.ws.close();\n", "    };\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "};\n", "\n", "mpl.figure.prototype._init_header = function () {\n", "    var titlebar = document.createElement('div');\n", "    titlebar.classList =\n", "        'ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix';\n", "    var titletext = document.createElement('div');\n", "    titletext.classList = 'ui-dialog-title';\n", "    titletext.setAttribute(\n", "        'style',\n", "        'width: 100%; text-align: center; padding: 3px;'\n", "    );\n", "    titlebar.appendChild(titletext);\n", "    this.root.appendChild(titlebar);\n", "    this.header = titletext;\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._root_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._init_canvas = function () {\n", "    var fig = this;\n", "\n", "    var canvas_div = (this.canvas_div = document.createElement('div'));\n", "    canvas_div.setAttribute('tabindex', '0');\n", "    canvas_div.setAttribute(\n", "        'style',\n", "        'border: 1px solid #ddd;' +\n", "            'box-sizing: content-box;' +\n", "            'clear: both;' +\n", "            'min-height: 1px;' +\n", "            'min-width: 1px;' +\n", "            'outline: 0;' +\n", "            'overflow: hidden;' +\n", "            'position: relative;' +\n", "            'resize: both;' +\n", "            'z-index: 2;'\n", "    );\n", "\n", "    function on_keyboard_event_closure(name) {\n", "        return function (event) {\n", "            return fig.key_event(event, name);\n", "        };\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'keydown',\n", "        on_keyboard_event_closure('key_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'keyup',\n", "        on_keyboard_event_closure('key_release')\n", "    );\n", "\n", "    this._canvas_extra_style(canvas_div);\n", "    this.root.appendChild(canvas_div);\n", "\n", "    var canvas = (this.canvas = document.createElement('canvas'));\n", "    canvas.classList.add('mpl-canvas');\n", "    canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'pointer-events: none;' +\n", "            'position: relative;' +\n", "            'z-index: 0;'\n", "    );\n", "\n", "    this.context = canvas.getContext('2d');\n", "\n", "    var backingStore =\n", "        this.context.backingStorePixelRatio ||\n", "        this.context.webkitBackingStorePixelRatio ||\n", "        this.context.mozBackingStorePixelRatio ||\n", "        this.context.msBackingStorePixelRatio ||\n", "        this.context.oBackingStorePixelRatio ||\n", "        this.context.backingStorePixelRatio ||\n", "        1;\n", "\n", "    this.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband_canvas = (this.rubberband_canvas = document.createElement(\n", "        'canvas'\n", "    ));\n", "    rubberband_canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'left: 0;' +\n", "            'pointer-events: none;' +\n", "            'position: absolute;' +\n", "            'top: 0;' +\n", "            'z-index: 1;'\n", "    );\n", "\n", "    // Apply a ponyfill if ResizeObserver is not implemented by browser.\n", "    if (this.ResizeObserver === undefined) {\n", "        if (window.ResizeObserver !== undefined) {\n", "            this.ResizeObserver = window.ResizeObserver;\n", "        } else {\n", "            var obs = _JSXTOOLS_RESIZE_OBSERVER({});\n", "            this.ResizeObserver = obs.ResizeObserver;\n", "        }\n", "    }\n", "\n", "    this.resizeObserverInstance = new this.ResizeObserver(function (entries) {\n", "        var nentries = entries.length;\n", "        for (var i = 0; i < nentries; i++) {\n", "            var entry = entries[i];\n", "            var width, height;\n", "            if (entry.contentBoxSize) {\n", "                if (entry.contentBoxSize instanceof Array) {\n", "                    // Chrome 84 implements new version of spec.\n", "                    width = entry.contentBoxSize[0].inlineSize;\n", "                    height = entry.contentBoxSize[0].blockSize;\n", "                } else {\n", "                    // Firefox implements old version of spec.\n", "                    width = entry.contentBoxSize.inlineSize;\n", "                    height = entry.contentBoxSize.blockSize;\n", "                }\n", "            } else {\n", "                // Chrome <84 implements even older version of spec.\n", "                width = entry.contentRect.width;\n", "                height = entry.contentRect.height;\n", "            }\n", "\n", "            // Keep the size of the canvas and rubber band canvas in sync with\n", "            // the canvas container.\n", "            if (entry.devicePixelContentBoxSize) {\n", "                // Chrome 84 implements new version of spec.\n", "                canvas.setAttribute(\n", "                    'width',\n", "                    entry.devicePixelContentBoxSize[0].inlineSize\n", "                );\n", "                canvas.setAttribute(\n", "                    'height',\n", "                    entry.devicePixelContentBoxSize[0].blockSize\n", "                );\n", "            } else {\n", "                canvas.setAttribute('width', width * fig.ratio);\n", "                canvas.setAttribute('height', height * fig.ratio);\n", "            }\n", "            /* This rescales the canvas back to display pixels, so that it\n", "             * appears correct on HiDPI screens. */\n", "            canvas.style.width = width + 'px';\n", "            canvas.style.height = height + 'px';\n", "\n", "            rubberband_canvas.setAttribute('width', width);\n", "            rubberband_canvas.setAttribute('height', height);\n", "\n", "            // And update the size in Python. We ignore the initial 0/0 size\n", "            // that occurs as the element is placed into the DOM, which should\n", "            // otherwise not happen due to the minimum size styling.\n", "            if (fig.ws.readyState == 1 && width != 0 && height != 0) {\n", "                fig.request_resize(width, height);\n", "            }\n", "        }\n", "    });\n", "    this.resizeObserverInstance.observe(canvas_div);\n", "\n", "    function on_mouse_event_closure(name) {\n", "        /* User Agent sniffing is bad, but WebKit is busted:\n", "         * https://bugs.webkit.org/show_bug.cgi?id=144526\n", "         * https://bugs.webkit.org/show_bug.cgi?id=181818\n", "         * The worst that happens here is that they get an extra browser\n", "         * selection when dragging, if this check fails to catch them.\n", "         */\n", "        var UA = navigator.userAgent;\n", "        var isWebKit = /AppleWebKit/.test(UA) && !/Chrome/.test(UA);\n", "        if(isWebKit) {\n", "            return function (event) {\n", "                /* This prevents the web browser from automatically changing to\n", "                 * the text insertion cursor when the button is pressed. We\n", "                 * want to control all of the cursor setting manually through\n", "                 * the 'cursor' event from matplotlib */\n", "                event.preventDefault()\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        } else {\n", "            return function (event) {\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        }\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'mousedown',\n", "        on_mouse_event_closure('button_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseup',\n", "        on_mouse_event_closure('button_release')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'dblclick',\n", "        on_mouse_event_closure('dblclick')\n", "    );\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    canvas_div.addEventListener(\n", "        'mousemove',\n", "        on_mouse_event_closure('motion_notify')\n", "    );\n", "\n", "    canvas_div.addEventListener(\n", "        'mouseenter',\n", "        on_mouse_event_closure('figure_enter')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseleave',\n", "        on_mouse_event_closure('figure_leave')\n", "    );\n", "\n", "    canvas_div.addEventListener('wheel', function (event) {\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        on_mouse_event_closure('scroll')(event);\n", "    });\n", "\n", "    canvas_div.appendChild(canvas);\n", "    canvas_div.appendChild(rubberband_canvas);\n", "\n", "    this.rubberband_context = rubberband_canvas.getContext('2d');\n", "    this.rubberband_context.strokeStyle = '#000000';\n", "\n", "    this._resize_canvas = function (width, height, forward) {\n", "        if (forward) {\n", "            canvas_div.style.width = width + 'px';\n", "            canvas_div.style.height = height + 'px';\n", "        }\n", "    };\n", "\n", "    // Disable right mouse context menu.\n", "    canvas_div.addEventListener('contextmenu', function (_e) {\n", "        event.preventDefault();\n", "        return false;\n", "    });\n", "\n", "    function set_focus() {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'mpl-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'mpl-button-group';\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'mpl-button-group';\n", "            continue;\n", "        }\n", "\n", "        var button = (fig.buttons[name] = document.createElement('button'));\n", "        button.classList = 'mpl-widget';\n", "        button.setAttribute('role', 'button');\n", "        button.setAttribute('aria-disabled', 'false');\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "\n", "        var icon_img = document.createElement('img');\n", "        icon_img.src = '_images/' + image + '.png';\n", "        icon_img.srcset = '_images/' + image + '_large.png 2x';\n", "        icon_img.alt = tooltip;\n", "        button.appendChild(icon_img);\n", "\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    var fmt_picker = document.createElement('select');\n", "    fmt_picker.classList = 'mpl-widget';\n", "    toolbar.appendChild(fmt_picker);\n", "    this.format_dropdown = fmt_picker;\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = document.createElement('option');\n", "        option.selected = fmt === mpl.default_extension;\n", "        option.innerHTML = fmt;\n", "        fmt_picker.appendChild(option);\n", "    }\n", "\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "};\n", "\n", "mpl.figure.prototype.request_resize = function (x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', { width: x_pixels, height: y_pixels });\n", "};\n", "\n", "mpl.figure.prototype.send_message = function (type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "};\n", "\n", "mpl.figure.prototype.send_draw_message = function () {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({ type: 'draw', figure_id: this.id }));\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "};\n", "\n", "mpl.figure.prototype.handle_resize = function (fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] !== fig.canvas.width || size[1] !== fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1], msg['forward']);\n", "        fig.send_message('refresh', {});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_rubberband = function (fig, msg) {\n", "    var x0 = msg['x0'] / fig.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / fig.ratio;\n", "    var x1 = msg['x1'] / fig.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / fig.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0,\n", "        0,\n", "        fig.canvas.width / fig.ratio,\n", "        fig.canvas.height / fig.ratio\n", "    );\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "};\n", "\n", "mpl.figure.prototype.handle_figure_label = function (fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "};\n", "\n", "mpl.figure.prototype.handle_cursor = function (fig, msg) {\n", "    fig.canvas_div.style.cursor = msg['cursor'];\n", "};\n", "\n", "mpl.figure.prototype.handle_message = function (fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "};\n", "\n", "mpl.figure.prototype.handle_draw = function (fig, _msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "};\n", "\n", "mpl.figure.prototype.handle_image_mode = function (fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "};\n", "\n", "mpl.figure.prototype.handle_history_buttons = function (fig, msg) {\n", "    for (var key in msg) {\n", "        if (!(key in fig.buttons)) {\n", "            continue;\n", "        }\n", "        fig.buttons[key].disabled = !msg[key];\n", "        fig.buttons[key].setAttribute('aria-disabled', !msg[key]);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_navigate_mode = function (fig, msg) {\n", "    if (msg['mode'] === 'PAN') {\n", "        fig.buttons['Pan'].classList.add('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    } else if (msg['mode'] === 'ZOOM') {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.add('active');\n", "    } else {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message('ack', {});\n", "};\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function (fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            var img = evt.data;\n", "            if (img.type !== 'image/png') {\n", "                /* FIXME: We get \"Resource interpreted as Image but\n", "                 * transferred with MIME type text/plain:\" errors on\n", "                 * Chrome.  But how to set the MIME type?  It doesn't seem\n", "                 * to be part of the websocket stream */\n", "                img.type = 'image/png';\n", "            }\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src\n", "                );\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                img\n", "            );\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        } else if (\n", "            typeof evt.data === 'string' &&\n", "            evt.data.slice(0, 21) === 'data:image/png;base64'\n", "        ) {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig['handle_' + msg_type];\n", "        } catch (e) {\n", "            console.log(\n", "                \"No handler for the '\" + msg_type + \"' message type: \",\n", "                msg\n", "            );\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\n", "                    \"Exception inside the 'handler_\" + msg_type + \"' callback:\",\n", "                    e,\n", "                    e.stack,\n", "                    msg\n", "                );\n", "            }\n", "        }\n", "    };\n", "};\n", "\n", "function getModifiers(event) {\n", "    var mods = [];\n", "    if (event.ctrlKey) {\n", "        mods.push('ctrl');\n", "    }\n", "    if (event.altKey) {\n", "        mods.push('alt');\n", "    }\n", "    if (event.shiftKey) {\n", "        mods.push('shift');\n", "    }\n", "    if (event.metaKey) {\n", "        mods.push('meta');\n", "    }\n", "    return mods;\n", "}\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * https://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s(original) {\n", "    return Object.keys(original).reduce(function (obj, key) {\n", "        if (typeof original[key] !== 'object') {\n", "            obj[key] = original[key];\n", "        }\n", "        return obj;\n", "    }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function (event, name) {\n", "    if (name === 'button_press') {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    // from https://stackoverflow.com/q/1114465\n", "    var boundingRect = this.canvas.getBoundingClientRect();\n", "    var x = (event.clientX - boundingRect.left) * this.ratio;\n", "    var y = (event.clientY - boundingRect.top) * this.ratio;\n", "\n", "    this.send_message(name, {\n", "        x: x,\n", "        y: y,\n", "        button: event.button,\n", "        step: event.step,\n", "        modifiers: getModifiers(event),\n", "        guiEvent: simple<PERSON>eys(event),\n", "    });\n", "\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (_event, _name) {\n", "    // Handle any extra behaviour associated with a key event\n", "};\n", "\n", "mpl.figure.prototype.key_event = function (event, name) {\n", "    // Prevent repeat events\n", "    if (name === 'key_press') {\n", "        if (event.key === this._key) {\n", "            return;\n", "        } else {\n", "            this._key = event.key;\n", "        }\n", "    }\n", "    if (name === 'key_release') {\n", "        this._key = null;\n", "    }\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.key !== 'Control') {\n", "        value += 'ctrl+';\n", "    }\n", "    else if (event.altKey && event.key !== 'Alt') {\n", "        value += 'alt+';\n", "    }\n", "    else if (event.shiftKey && event.key !== 'Shift') {\n", "        value += 'shift+';\n", "    }\n", "\n", "    value += 'k' + event.key;\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, { key: value, guiEvent: simpleKeys(event) });\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function (name) {\n", "    if (name === 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message('toolbar_button', { name: name });\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function (tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "\n", "///////////////// REMAINING CONTENT GENERATED BY embed_js.py /////////////////\n", "// prettier-ignore\n", "var _JSXTOOLS_RESIZE_OBSERVER=function(A){var t,i=new WeakMap,n=new WeakMap,a=new WeakMap,r=new WeakMap,o=new Set;function s(e){if(!(this instanceof s))throw new TypeError(\"Constructor requires 'new' operator\");i.set(this,e)}function h(){throw new TypeError(\"Function is not a constructor\")}function c(e,t,i,n){e=0 in arguments?Number(arguments[0]):0,t=1 in arguments?Number(arguments[1]):0,i=2 in arguments?Number(arguments[2]):0,n=3 in arguments?Number(arguments[3]):0,this.right=(this.x=this.left=e)+(this.width=i),this.bottom=(this.y=this.top=t)+(this.height=n),Object.freeze(this)}function d(){t=requestAnimationFrame(d);var s=new WeakMap,p=new Set;o.forEach((function(t){r.get(t).forEach((function(i){var r=t instanceof window.SVGElement,o=a.get(t),d=r?0:parseFloat(o.paddingTop),f=r?0:parseFloat(o.paddingRight),l=r?0:parseFloat(o.paddingBottom),u=r?0:parseFloat(o.paddingLeft),g=r?0:parseFloat(o.borderTopWidth),m=r?0:parseFloat(o.borderRightWidth),w=r?0:parseFloat(o.borderBottomWidth),b=u+f,F=d+l,v=(r?0:parseFloat(o.borderLeftWidth))+m,W=g+w,y=r?0:t.offsetHeight-W-t.clientHeight,E=r?0:t.offsetWidth-v-t.clientWidth,R=b+v,z=F+W,M=r?t.width:parseFloat(o.width)-R-E,O=r?t.height:parseFloat(o.height)-z-y;if(n.has(t)){var k=n.get(t);if(k[0]===M&&k[1]===O)return}n.set(t,[M,O]);var S=Object.create(h.prototype);S.target=t,S.contentRect=new c(u,d,M,O),s.has(i)||(s.set(i,[]),p.add(i)),s.get(i).push(S)}))})),p.forEach((function(e){i.get(e).call(e,s.get(e),e)}))}return s.prototype.observe=function(i){if(i instanceof window.Element){r.has(i)||(r.set(i,new Set),o.add(i),a.set(i,window.getComputedStyle(i)));var n=r.get(i);n.has(this)||n.add(this),cancelAnimationFrame(t),t=requestAnimationFrame(d)}},s.prototype.unobserve=function(i){if(i instanceof window.Element&&r.has(i)){var n=r.get(i);n.has(this)&&(n.delete(this),n.size||(r.delete(i),o.delete(i))),n.size||r.delete(i),o.size||cancelAnimationFrame(t)}},A.DOMRectReadOnly=c,A.ResizeObserver=s,A.ResizeObserverEntry=h,A}; // eslint-disable-line\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Left button pans, Right button zooms\\nx/y fixes axis, CTRL fixes aspect\", \"fa fa-arrows\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\\nx/y fixes axis\", \"fa fa-square-o\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pgf\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\", \"webp\"];\n", "\n", "mpl.default_extension = \"png\";/* global mpl */\n", "\n", "var comm_websocket_adapter = function (comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.binaryType = comm.kernel.ws.binaryType;\n", "    ws.readyState = comm.kernel.ws.readyState;\n", "    function updateReadyState(_event) {\n", "        if (comm.kernel.ws) {\n", "            ws.readyState = comm.kernel.ws.readyState;\n", "        } else {\n", "            ws.readyState = 3; // Closed state.\n", "        }\n", "    }\n", "    comm.kernel.ws.addEventListener('open', updateReadyState);\n", "    comm.kernel.ws.addEventListener('close', updateReadyState);\n", "    comm.kernel.ws.addEventListener('error', updateReadyState);\n", "\n", "    ws.close = function () {\n", "        comm.close();\n", "    };\n", "    ws.send = function (m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function (msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        var data = msg['content']['data'];\n", "        if (data['blob'] !== undefined) {\n", "            data = {\n", "                data: new Blob(msg['buffers'], { type: data['blob'] }),\n", "            };\n", "        }\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(data);\n", "    });\n", "    return ws;\n", "};\n", "\n", "mpl.mpl_figure_comm = function (comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = document.getElementById(id);\n", "    var ws_proxy = comm_websocket_adapter(comm);\n", "\n", "    function ondownload(figure, _format) {\n", "        window.open(figure.canvas.toDataURL());\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy, ondownload, element);\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element;\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error('Failed to find cell for figure', id, fig);\n", "        return;\n", "    }\n", "    fig.cell_info[0].output_area.element.on(\n", "        'cleared',\n", "        { fig: fig },\n", "        fig._remove_fig_handler\n", "    );\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function (fig, msg) {\n", "    var width = fig.canvas.width / fig.ratio;\n", "    fig.cell_info[0].output_area.element.off(\n", "        'cleared',\n", "        fig._remove_fig_handler\n", "    );\n", "    fig.resizeObserverInstance.unobserve(fig.canvas_div);\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable();\n", "    fig.parent_element.innerHTML =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "    fig.close_ws(fig, msg);\n", "};\n", "\n", "mpl.figure.prototype.close_ws = function (fig, msg) {\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "};\n", "\n", "mpl.figure.prototype.push_to_output = function (_remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width / this.ratio;\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message('ack', {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () {\n", "        fig.push_to_output();\n", "    }, 1000);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'btn-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'btn-group';\n", "    var button;\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'btn-group';\n", "            continue;\n", "        }\n", "\n", "        button = fig.buttons[name] = document.createElement('button');\n", "        button.classList = 'btn btn-default';\n", "        button.href = '#';\n", "        button.title = name;\n", "        button.innerHTML = '<i class=\"fa ' + image + ' fa-lg\"></i>';\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message pull-right';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = document.createElement('div');\n", "    buttongrp.classList = 'btn-group inline pull-right';\n", "    button = document.createElement('button');\n", "    button.classList = 'btn btn-mini btn-primary';\n", "    button.href = '#';\n", "    button.title = 'Stop Interaction';\n", "    button.innerHTML = '<i class=\"fa fa-power-off icon-remove icon-large\"></i>';\n", "    button.addEventListener('click', function (_evt) {\n", "        fig.handle_close(fig, {});\n", "    });\n", "    button.addEventListener(\n", "        'mouseover',\n", "        on_mouseover_closure('Stop Interaction')\n", "    );\n", "    buttongrp.appendChild(button);\n", "    var titlebar = this.root.querySelector('.ui-dialog-titlebar');\n", "    titlebar.insertBefore(buttongrp, titlebar.firstChild);\n", "};\n", "\n", "mpl.figure.prototype._remove_fig_handler = function (event) {\n", "    var fig = event.data.fig;\n", "    if (event.target !== this) {\n", "        // Ignore bubbled events from children.\n", "        return;\n", "    }\n", "    fig.close_ws(fig, {});\n", "};\n", "\n", "mpl.figure.prototype._root_extra_style = function (el) {\n", "    el.style.boxSizing = 'content-box'; // override notebook setting of border-box.\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (el) {\n", "    // this is important to make the div 'focusable\n", "    el.setAttribute('tabindex', 0);\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    } else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (event, _name) {\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which === 13) {\n", "        this.canvas_div.blur();\n", "        // select the cell after this one\n", "        var index = IPython.notebook.find_cell_index(this.cell_info[0]);\n", "        IPython.notebook.select(index + 1);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    fig.ondownload(fig, null);\n", "};\n", "\n", "mpl.find_output_cell = function (html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i = 0; i < ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code') {\n", "            for (var j = 0; j < cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] === html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "};\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel !== null) {\n", "    IPython.notebook.kernel.comm_manager.register_target(\n", "        'matplotlib',\n", "        mpl.mpl_figure_comm\n", "    );\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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\" width=\"640\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["PyPlot3D backend, t = 0.05, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["%matplotlib notebook\n", "DFbot1.plot([np.pi/2,-np.pi/6,0,np.pi,0,0])"]}, {"cell_type": "code", "execution_count": 63, "id": "d662adb5", "metadata": {}, "outputs": [], "source": ["mtx=np.array([[919.21981864,   0.        , 356.41270451],\n", "       [  0.        , 913.16565134, 236.9305    ],\n", "       [  0.        ,   0.        ,   1.        ]])"]}, {"cell_type": "code", "execution_count": 70, "id": "f663efe8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2D 图像点:\n", "[468.3519525 236.9305   ]\n"]}], "source": ["import numpy as np\n", " \n", "# 内参矩阵\n", "K = mtx\n", " \n", "# 位姿矩阵（简化为只有平移的情况）\n", "pose=T1\n", " \n", "# 外参矩阵（位姿矩阵的逆）\n", "extrinsic = np.linalg.inv(pose)\n", " \n", "# 3D点（世界坐标系）\n", "point_3d = np.array([0, -0.29, 0, 1])\n", " \n", "# 将点从世界坐标系转换到相机坐标系\n", "point_camera = extrinsic @ point_3d\n", " \n", "# 投影到图像平面\n", "point_2d = K @ point_camera[:3]\n", "depth=point_2d[2]\n", "point_2d /= depth  # 归一化\n", " \n", "print(\"2D 图像点:\")\n", "print(point_2d[:2])"]}, {"cell_type": "code", "execution_count": 71, "id": "03e9814a", "metadata": {}, "outputs": [{"data": {"text/plain": ["111.93924798604007"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["point_2d[0]-356.41270451"]}, {"cell_type": "code", "execution_count": 72, "id": "3a3e6a80", "metadata": {}, "outputs": [{"data": {"text/plain": ["-2.842170943040401e-14"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["point_2d[1]-236.9305 "]}, {"cell_type": "code", "execution_count": 18, "id": "28615e20", "metadata": {}, "outputs": [], "source": ["T2=np.array(DFbot1.fkine([np.pi/2,-np.pi/6,0,np.pi,0,0]))\n", "T2[1,-1]=-0.23"]}, {"cell_type": "code", "execution_count": 73, "id": "68232284", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2D 图像点:\n", "[382.89038247 236.9305    ]\n"]}], "source": ["# 内参矩阵\n", "K = mtx\n", " \n", "# 位姿矩阵（简化为只有平移的情况）\n", "pose=T2\n", " \n", "# 外参矩阵（位姿矩阵的逆）\n", "extrinsic = np.linalg.inv(pose)\n", " \n", "# 3D点（世界坐标系）\n", "point_3d = np.array([0, -0.29, 0, 1])\n", " \n", "# 将点从世界坐标系转换到相机坐标系\n", "point_camera = extrinsic @ point_3d\n", " \n", "# 投影到图像平面\n", "point_2d = K @ point_camera[:3]\n", "depth=point_2d[2]\n", "point_2d /= depth  # 归一化\n", " \n", "print(\"2D 图像点:\")\n", "print(point_2d[:2])"]}, {"cell_type": "code", "execution_count": 76, "id": "ac810acf", "metadata": {}, "outputs": [], "source": ["#逆过程"]}, {"cell_type": "code", "execution_count": 77, "id": "a41d520b", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.1142975927850167"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["depth"]}, {"cell_type": "code", "execution_count": 78, "id": "f7466268", "metadata": {}, "outputs": [], "source": ["xaxis=356.41270451+96"]}, {"cell_type": "code", "execution_count": 79, "id": "5fbecad6", "metadata": {}, "outputs": [{"data": {"text/plain": ["452.41270451"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["xaxis"]}, {"cell_type": "code", "execution_count": 80, "id": "35e0216b", "metadata": {}, "outputs": [], "source": ["yaxis=236.9305 "]}, {"cell_type": "code", "execution_count": 81, "id": "fbe01550", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([51.70968307, 27.08058581,  0.11429759])"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["point_3dr=np.array([xaxis*depth,yaxis*depth,1*depth])\n", "point_3dr"]}, {"cell_type": "code", "execution_count": 82, "id": "9274dbf6", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 1.19368280e-02, -3.46944695e-18,  1.14297593e-01])"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["np.linalg.inv(K)@point_3dr"]}, {"cell_type": "code", "execution_count": 20, "id": "cd35b5e8", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 6.06211306e-03, -3.83640492e-18,  1.15896753e-01,  1.00000000e+00])"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["point_camera"]}, {"cell_type": "code", "execution_count": 21, "id": "83a702dc", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m-0.866   \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-0.5     \u001b[0m \u001b[38;5;4m-0.2168  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.5     \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-0.866   \u001b[0m \u001b[38;5;4m 0.09734 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["T1"]}, {"cell_type": "code", "execution_count": 83, "id": "7a571602", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.00587471494"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["distance=1.19368280e-02-6.06211306e-03\n", "distance"]}, {"cell_type": "code", "execution_count": 84, "id": "5c0bd0df", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[-8.36449319e-17, -1.00000000e+00,  8.36449319e-17,\n", "        -1.30235159e-17],\n", "       [-8.66025404e-01,  3.06161700e-17, -5.00000000e-01,\n", "        -2.22674715e-01],\n", "       [ 5.00000000e-01, -1.14261102e-16, -8.66025404e-01,\n", "         9.73384758e-02],\n", "       [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         1.00000000e+00]])"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["T2=np.array(T1)\n", "T2[1,-1]=-0.2168-distance\n", "T2"]}, {"cell_type": "code", "execution_count": 86, "id": "251f82aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["IKSolution(q=array([-1.57729327,  2.66418338,  1.7583892 , -0.75237966,  2.90510335,\n", "        0.23169474]), success=True, iterations=21, searches=2, residual=0.005894639681365834, reason='Success')"]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["sol = DFbot1.ikine_LM(T2,q0=[0,0,0,0,0,0],ilimit=100, slimit=100,joint_limits=True,tol=0.01)\n", "sol"]}, {"cell_type": "code", "execution_count": 87, "id": "c9c90365", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-1.57729327,  2.66418338,  1.7583892 , -0.75237966,  2.90510335,\n", "        0.23169474])"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["sol.q"]}, {"cell_type": "code", "execution_count": null, "id": "927bc204", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}