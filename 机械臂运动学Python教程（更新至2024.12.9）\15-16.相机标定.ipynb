{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import cv2 as cv\n", "import glob\n", " \n", "# termination criteria\n", "criteria = (cv.TERM_CRITERIA_EPS + cv.TERM_CRITERIA_MAX_ITER, 30, 0.001)\n", " \n", "# prepare object points, like (0,0,0), (1,0,0), (2,0,0) ....,(6,5,0)\n", "objp = np.zeros((7*7,3), np.float32)\n", "objp[:,:2] = np.mgrid[0:7,0:7].<PERSON>.reshape(-1,2)\n", "objp=0.008*objp"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Arrays to store object points and image points from all the images.\n", "objpoints = [] # 3d point in real world space\n", "imgpoints = [] # 2d points in image plane.\n", " \n", "images = glob.glob('*.jpg')\n", " \n", "for fname in images:\n", "    img = cv.imread(fname)\n", "    gray = cv.cvtColor(img, cv.COLOR_BGR2GRAY)\n", "    # Find the chess board corners\n", "    ret, corners = cv.findChessboardCorners(gray, (7,7), None)\n", "    # If found, add object points, image points (after refining them)\n", "    if ret == True:\n", "        objpoints.append(objp)\n", "        corners2 = cv.cornerSubPix(gray,corners, (5,5), (-1,-1), criteria)\n", "        imgpoints.append(corners2)\n", "    # Draw and display the corners\n", "    cv.drawChessboardCorners(img, (7,7), corners2, ret)\n", "    cv.imshow('img', img)\n", "    cv.<PERSON><PERSON><PERSON>(500)\n", "\n", "cv.<PERSON><PERSON>ll<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["['19f090b2abf3c42f8bc4f361bbf4b323.jpg',\n", " 'c4bf2e530c0c0e4e9a5af7fa1a81d356.jpg',\n", " '224716ff3deeadfe6a578730d1c31a34.jpg',\n", " 'e7ccbaaafc5da0324a39da29bf5c9d72.jpg',\n", " '12d8a0bf774dd67e4a0724e8bacffae5.jpg',\n", " '7839c5bc7cb9da17edb35af1a1943fc4.jpg',\n", " '612ed1b9fcd509196547952e11bcddb5.jpg',\n", " 'ba93d2601b3e5de8ad8b5ccf4bcf9e2e.jpg',\n", " '9eb21244062871276e556949cb4e7146.jpg',\n", " '683af129051c7db5b1131049022195ef.jpg',\n", " '071e795eabe63f778dd9ff7530d7b124.jpg',\n", " 'd79fd69b6d2d57b6f69e02146958829d.jpg',\n", " '53ed0daa055b835d7dba6e3ea5812242.jpg',\n", " '14213327bbb8e077bf130990bd4771dd.jpg']"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["images"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["ret, mtx, dist, rvecs, tvecs = cv.calibrateCamera(objpoints, imgpoints, gray.shape[::-1], None, None)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[919.21981864,   0.        , 356.41270451],\n", "       [  0.        , 913.16565134, 236.9305    ],\n", "       [  0.        ,   0.        ,   1.        ]])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["mtx"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[-4.32733568e-01,  1.08659933e+00,  1.11155166e-03,\n", "        -3.70676225e-04, -3.75586346e+00]])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["dist"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["#去畸变"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["img = cv.imread(images[0])\n", "h, w = img.shape[:2]\n", "newcameramtx, roi = cv.getOptimalNewCameraMatrix(mtx, dist, (w,h), 1, (w,h))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# undistort\n", "dst = cv.undistort(img, mtx, dist, None, newcameramtx)\n", "# crop the image\n", "x, y, w, h = roi\n", "dst = dst[y:y+h, x:x+w]\n", "cv.imwrite('calibresult.png', dst)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["'19f090b2abf3c42f8bc4f361bbf4b323.jpg'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["images[0]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["#rvecs, tvecs的作用"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def draw(img, corners, imgpts):\n", " corner = tuple(corners[0].ravel().astype(int))\n", " img = cv.line(img, corner , tuple(imgpts[0].ravel().astype(int)), (255,0,0), 5)\n", " img = cv.line(img, corner, tuple(imgpts[1].ravel().astype(int)), (0,255,0), 5)\n", " img = cv.line(img, corner , tuple(imgpts[2].ravel().astype(int)), (0,0,255), 5)\n", " return img"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["criteria = (cv.TERM_CRITERIA_EPS + cv.TERM_CRITERIA_MAX_ITER, 30, 0.001)\n", "objp = np.zeros((7*7,3), np.float32)\n", "objp[:,:2] = np.mgrid[0:7,0:7].<PERSON>.reshape(-1,2)\n", " \n", "axis = np.float32([[3,0,0], [0,3,0], [0,0,-3]]).reshape(-1,3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": [" img = cv.imread(images[0])\n", " gray = cv.cvtColor(img,cv.COLOR_BGR2GRAY)\n", " ret, corners = cv.findChessboardCorners(gray, (7,7),None)\n", " \n", " if ret == True:\n", "     corners2 = cv.cornerSubPix(gray,corners,(11,11),(-1,-1),criteria)\n", " \n", " # Find the rotation and translation vectors.\n", " ret,rvecs, tvecs = cv.solvePnP(objp, corners2, mtx, dist)\n", " \n", " # project 3D points to image plane\n", " imgpts, jac = cv.projectPoints(axis, rvecs, tvecs, mtx, dist)\n", " \n", " img = draw(img,corners2 ,imgpts)\n", " cv.imshow('img',img)\n", " k = cv.<PERSON><PERSON><PERSON>(0) & 0xFF\n", " if k == ord('s'):\n", "     cv.imwrite(fname[:6]+'.png', img)\n", " \n", "cv.<PERSON><PERSON>ll<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([[-0.24406926],\n", "        [ 0.00712551],\n", "        [ 0.16918039]]),\n", " array([[-3.82911151],\n", "        [-3.74079138],\n", "        [22.01701187]]))"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["rvecs, tvecs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 2}