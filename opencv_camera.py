
import numpy as np
import cv2 as cv
import glob

from matplotlib.pyplot import imshow

criteria = (cv.TERM_CRITERIA_EPS + cv.TERM_CRITERIA_MAX_ITER,30,0.001)

objp = np.zeros((7*7,3),np.float32)
objp[:,:2] = np.mgrid[0:7,0:7].T.reshape(-1,2)
objp=0.007*objp   #0.007鏄疄闄呯敓娲讳腑鏂规牸鐨勮竟闀�


# images = glob.glob('WIN_20250304_17_33_28_Pro.jpg')

# Arrays to store object points and image points from all the images.
objpoints = []  # 3d point in real world space
imgpoints = []  # 2d points in image plane.

images = glob.glob('*.jpg')

for fname in images:
    img = cv.imread(fname)
    gray = cv.cvtColor(img, cv.COLOR_BGR2GRAY)
    # Find the chess board corners
    ret, corners = cv.findChessboardCorners(gray, (7, 7), None)
    # If found, add object points, image points (after refining them)
    if ret == True:
        objpoints.append(objp)
        corners2 = cv.cornerSubPix(gray, corners, (5, 5), (-1, -1), criteria)
        imgpoints.append(corners2)
    # Draw and display the corners
    cv.drawChessboardCorners(img, (7, 7), corners2, ret)
    cv.imshow('img', img)
    cv.waitKey(500)

cv.destroyAllWindows()


ret, mtx, dist, rvecs, tvecs = cv.calibrateCamera(objpoints, imgpoints, gray.shape[::-1],None,None)


print(mtx)
print(dist)


#浠ヤ笅涓哄幓鐣稿彉鏁欑▼
img = cv.imread(images[0])
h,w = img.shape[:2]
newcameramtx,roi = cv.getOptimalNewCameraMatrix(mtx, dist, (w,h), 1,(w,h))

dst = cv.undistort(img, mtx, dist, None, newcameramtx)
x,y,w,h = roi
dst = dst[y:y+h, x:x+w]
cv.imwrite('calibresult.png', dst)



# [[464.05410977   0.         327.37283095]
#  [  0.         461.80335963 253.75556623]
#  [  0.           0.           1.        ]]
# [[-0.38316065  0.18109933  0.00079424 -0.00334928 -0.04634778]]