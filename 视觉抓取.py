import numpy as np
import math
from open3d.cpu.pybind.core import solve
from spatialmath import SE3
from spatialmath.base import e2h, h2e
from roboticstoolbox import *
from machinevisiontoolbox import CentralCamera
from machinevisiontoolbox import mkcube
from serial1 import SerialComm
import time
import matplotlib.pyplot as plt
import roboticstoolbox as rtb
import cv2

# [[476.82323697   0.         322.54024423]
#  [  0.         476.37004943 247.86719414]
#  [  0.           0.           1.        ]]
# [[-0.41647071  0.22407714  0.00135502  0.00114579 -0.07391987]]
# [[421.79900424   0.         322.02909425]
#  [  0.         421.64955874 246.87774967]
#  [  0.           0.           1.        ]]
# [[-0.32822254  0.14052718  0.00092947  0.00062209 -0.03723494]]
# [[334.38767364   0.         324.74464886]
#  [  0.         334.69791823 245.09996179]
#  [  0.           0.           1.        ]]
# [[-0.20522585  0.05372439  0.00176344 -0.00022573 -0.00840238]]
from ultralytics import YOLO
import cv2
###########################################################图像检测部分
# 加载预训练的 YOLO 模型
# E:/yolo_data/yolo11/代码/yolo11-trash-det/42_demo/runs/yolo11n-cbam/weights/best.pt
#E:/yolo_data/yolo11/代码/yolo11-trash-det/42_demo/runs/yolo11n-coordattention/weights/best.pt
#E:/yolo_data/yolo11/代码/yolo11-trash-det/42_demo/runs/yolo11n_pretrained/weights/best.pt
model = YOLO("E:/yolo_data/yolo11/代码/yolo11-trash-det/42_demo/runs/yolo11n-coordattention/weights/best.pt")  # 替换为您自己的模型路径

# 打开摄像头
cap = cv2.VideoCapture(0)  # 参数 0 表示默认摄像头，如果有多个摄像头，可以尝试 1, 2...

# 设置窗口尺寸
window_width = 640  # 小窗口宽度
window_height = 480  # 小窗口高度

# 读取一帧图像
ret, frame = cap.read()
if not ret:
    print("无法获取摄像头画面")

# 进行目标检测
results = model(frame)

# 遍历检测结果
for result in results:
    boxes = result.boxes  # 获取边界框信息
    names = result.names  # 获取类别名称字典
    for box in boxes:
        # 提取边界框坐标 (x1, y1, x2, y2)
        x1, y1, x2, y2 = map(int, box.xyxy[0])

        # 计算目标的面积
        width = x2 - x1
        height = y2 - y1
        area = width * height

        # 只处理面积小于等于 160x320（51200 像素）的目标
        if area > 160 * 320:  # 如果面积超过限制，则跳过该目标
            continue

        # 计算物体的中心点
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2

        # 获取类别 ID 和名称
        cls_id = int(box.cls[0])  # 类别 ID
        cls_name = names[cls_id]  # 类别名称

        # 打印中心点坐标和类别名称
        print(f"Detected object: {cls_name}, center: ({center_x}, {center_y}), area: {area}")

        # 在图像上绘制中心点
        cv2.circle(frame, (center_x, center_y), 5, (0, 255, 0), -1)  # 绿色圆点表示中心
        cv2.rectangle(frame, (x1, y1), (x2, y2), (255, 0, 0), 2)  # 蓝色矩形框表示边界框

        # 在图像上显示类别名称
        label = f"{cls_name}"
        cv2.putText(frame, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 0, 0), 2)

# 调整帧的大小以适应小窗口
frame = cv2.resize(frame, (window_width, window_height))

# 显示结果
cv2.imshow("Real-time Object Detection", frame)



###########################################################夹抓夹取
# 创建一个CentralCamera实例
cam = CentralCamera(
    f=[334.38767364*10e-6, 334.69791823*10e-6], # 焦距，单位为米
    rho=10e-6, # 像素大小，单位为米
    imagesize=[640, 480], # 图像尺寸
    pp=[324.74464886, 245.09996179], # 主点位置
    name="mycamera"
)

p = np.array([-0.18,0,0.01])
#相机
DFbot1 = DHRobot(
    [
        RevoluteMDH(d=0.075, qlim=np.array([-np.pi*2/3, np.pi*2/3])),
        RevoluteMDH(alpha=np.pi / 2, qlim=np.array([-np.pi/2, np.pi/2]),offset=-np.pi/2),
        RevoluteMDH(a=-0.1, qlim=np.array([-np.pi*2/3, np.pi*2/3])),
        RevoluteMDH(a=-0.095, qlim=np.array([-np.pi/2, np.pi/2]),offset=np.pi/2),
        RevoluteMDH(alpha=-np.pi/2,d=0.05, qlim=np.array([-np.pi/2, np.pi/2])),
        RevoluteMDH(a=0.05, qlim=np.array([-np.pi, np.pi]))

    ],
    name="DFbot",
)


# #夹爪
# DFbot2 = DHRobot(
#     [
#         RevoluteMDH(d=0.075, qlim=np.array([-np.pi, np.pi])),
#         RevoluteMDH(alpha=np.pi / 2, qlim=np.array([-np.pi, np.pi]),offset=-np.pi/2),
#         RevoluteMDH(a=-0.1, qlim=np.array([-np.pi, np.pi])),
#         RevoluteMDH(a=-0.095, qlim=np.array([-np.pi, np.pi]),offset=np.pi/2),
#         RevoluteMDH(alpha=-np.pi / 2, d=0.06, qlim=np.array([-np.pi, np.pi]))
#
#     ],
#     name="DFbot",
# )

#夹爪
DFbot2 = DHRobot(
    [
        RevoluteMDH(d=0.075, qlim=np.array([-np.pi*2/3, np.pi*2/3])),
        RevoluteMDH(alpha=np.pi / 2, qlim=np.array([-np.pi/2, np.pi/2]),offset=-np.pi/2),
        RevoluteMDH(a=-0.1, qlim=np.array([-np.pi*2/3, np.pi*2/3])),
        RevoluteMDH(a=-0.095, qlim=np.array([-np.pi/2, np.pi/2]),offset=np.pi/2),
        RevoluteMDH(alpha=-np.pi / 2, d=0.04, qlim=np.array([-np.pi*2/3, np.pi*2/3]))

    ],
    name="DFbot",
)
T1=DFbot1.fkine([0,0,np.pi/2,np.pi/2,0,np.pi/2])
DFbot1.plot([0, 0, np.pi/2, np.pi/2, 0, -np.pi/2],block=True)
# plt.show()
SE3(T1)
print(T1)
P = np.array([-0.18, 0, 0.015])
p=cam.project_point(P ,pose=T1)
cam.plot_point(P ,pose=T1)



X ,Y ,Z= mkcube(s=0.03,centre=(-0.165, 0, 0.015),edge=True)
cam.plot_wireframe(X,Y,Z,pose=T1)
plt.show()

mtx=np.array([[334.38767364   ,    0.         , 324.74464886 ],
              [  0.             ,   334.69791823, 245.09996179 ],
              [  0.             ,         0.    ,    1.      ]])

# 内参矩阵
K = mtx

# 位姿矩阵（简化为只有平移的情况）
pose = T1

# 外参矩阵（位姿矩阵的逆）
extrinsic = np.linalg.inv(pose)

# 3D点（世界坐标系）
point_3d = np.array([-0.165, 0, 0.015, 1])

# 将点从世界坐标系转换到相机坐标系
point_camera = extrinsic @ point_3d

# 投影到图像平面
point_2d = K @ point_camera[:3]
depth = point_2d[2]
print(depth)
point_2d /= depth  # 归一化，最后一行化为1
print(point_2d)
# DFbot1.plot([0,np.pi/6,np.pi/3,np.pi/2,0,0],block=True)
xaxis = center_x
if xaxis < 280:
    xaxis = xaxis * 0.5
if xaxis > 360:
    xaxis= xaxis * 1.5

yaxis = -(480-center_y)
pointy_2d = depth*1*np.array([xaxis,yaxis,1])
point_camera1=e2h(np.linalg.inv(K)@pointy_2d)
new_point_3d=np.array(np.linalg.inv(extrinsic)@point_camera1)

print(new_point_3d)
# print(T1)
T21=DFbot2.fkine([0,0,np.pi/2,np.pi/2,0])
print(T21)

T2=np.array(T21)
T2[:,-1]=new_point_3d.flatten()
print(T2)

sol = DFbot2.ikine_LM(T2,q0=[0,0,np.pi/2,np.pi/2,0],ilimit=500,slimit=500,joint_limits=True,tol=0.02)
print(sol)
state0=[0,0,0,0,0]
state1=[0,0,np.pi/2,np.pi/2,0]
# qt = jtraj(state0,state1,10)
# print(qt)


qt = rtb.tools.trajectory.jtraj(state1,sol.q,10)

# for i, q in enumerate(qt.q):  # 遍历 qt.q 中的每一个关节角度数组
#     print(f"Step {i}:")
#     print("Joint Angles:", q)
#     print("-" * 30)  # 打印分隔线以便更清晰地区分每一步输出
# 创建SerialComm对象
data_to_send2 = None
serial_comm = SerialComm(port='COM20', baudrate=115200)
time.sleep(1)
# 示例数据
for a, q in enumerate(qt.q):
    deg2 = [x * 180 / np.pi for x in q]
    data_to_send = [40, deg2[4], deg2[3], deg2[2], deg2[1], deg2[0]]
    if a == 9:
        data_to_send = [0, deg2[4], deg2[3], deg2[2], deg2[1], deg2[0]]
        data_to_send1 = data_to_send.copy()
    for i in range(len(data_to_send)):
        data_to_send[i] = int(data_to_send[i])
        if data_to_send[i] > 0:
            # 如果是负数，我们将其转换为字节表示，并设置最高位为1
            # 注意：这里的处理方式取决于你想要的结果格式。
            # 对于这个例子，我们将数值限制在8位范围内进行演示。
            if data_to_send[i] >= -128 and data_to_send[i] <= 127:
                # 将数值限制在8位有符号整数范围内
                data_to_send[i] = data_to_send[i] & 0xFF | 0x80
            else:
                print(f"警告: 数值 {data_to_send[i]} 超出了8位有符号整数范围.")
        if data_to_send[i] < 0:
            data_to_send[i] = -data_to_send[i]
    # 发送数据
    time.sleep(0.50)
    serial_comm.send_data(data_to_send)

print("Last sent array:", data_to_send1)
# 对每个数据项进行处理并确保它们是整数
for i in range(len(data_to_send1)):
    # 先转换为整数
    data_to_send1[i] = int(data_to_send1[i])
data_to_send2 = data_to_send1.copy()
data_to_send2[4] = 0
data_to_send2[5] = 50
qt = rtb.tools.trajectory.jtraj(data_to_send1,data_to_send2,10)
print(qt.q)
#搬运目标地点
for a, q in enumerate(qt.q):

    data_to_send = [0, q[1], q[2], q[3], q[4], q[5]]
    if a == 8:
        data_to_send = [0, q[1], q[2], q[3], 40, q[5]]

    if a == 9:
        time.sleep(2)
        data_to_send = [30, q[1], q[2], q[3], 40, q[5]]

    for i in range(len(data_to_send)):
        data_to_send[i] = int(data_to_send[i])
        if data_to_send[i] > 0:
            # 如果是负数，我们将其转换为字节表示，并设置最高位为1
            # 注意：这里的处理方式取决于你想要的结果格式。
            # 对于这个例子，我们将数值限制在8位范围内进行演示。
            if data_to_send[i] >= -128 and data_to_send[i] <= 127:
                # 将数值限制在8位有符号整数范围内
                data_to_send[i] = data_to_send[i] & 0xFF | 0x80
            else:
                print(f"警告: 数值 {data_to_send[i]} 超出了8位有符号整数范围.")
        if data_to_send[i] < 0:
            data_to_send[i] = -data_to_send[i]
    data_to_send1 = [1, deg2[4], deg2[3], deg2[2], deg2[1], deg2[0]]
    # 发送数据
    time.sleep(0.50)
    serial_comm.send_data(data_to_send)


time.sleep(2)
# # 对每个数据项进行处理并确保它们是整数
# for i in range(len(data_to_send1)):
#     # 先转换为整数
#     data_to_send1[i] = int(data_to_send1[i])
data_to_send2=[0,0,90,90,0,0]
data_to_send1[5] = 40
data_to_send1[4] = 10
qt = rtb.tools.trajectory.jtraj(data_to_send1,data_to_send2,10)
print(qt.q)
if label == "污损塑料":
    # 搬运目标地点
    for a, q in enumerate(qt.q):

        data_to_send = [0, q[1], q[2], q[3], q[4], q[5]]
        # if a == 8:
        #     data_to_send = [0, q[1], q[2], q[3], 40, q[5]]
        #
        # if a == 9:
        #     time.sleep(2)
        #     data_to_send = [30, q[1], q[2], q[3], 40, q[5]]

        for i in range(len(data_to_send)):
            data_to_send[i] = int(data_to_send[i])
            if data_to_send[i] > 0:
                # 如果是负数，我们将其转换为字节表示，并设置最高位为1
                # 注意：这里的处理方式取决于你想要的结果格式。
                # 对于这个例子，我们将数值限制在8位范围内进行演示。
                if data_to_send[i] >= -128 and data_to_send[i] <= 127:
                    # 将数值限制在8位有符号整数范围内
                    data_to_send[i] = data_to_send[i] & 0xFF | 0x80
                else:
                    print(f"警告: 数值 {data_to_send[i]} 超出了8位有符号整数范围.")
            if data_to_send[i] < 0:
                data_to_send[i] = -data_to_send[i]
        # data_to_send1 = [1, deg2[4], deg2[3], deg2[2], deg2[1], deg2[0]]
        # 发送数据
        time.sleep(0.50)
        serial_comm.send_data(data_to_send)




# 关闭串口连接
serial_comm.close()