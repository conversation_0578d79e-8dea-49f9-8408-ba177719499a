from ultralytics import YOLO
import cv2

# 加载预训练的 YOLO 模型
model = YOLO("E:/yolo_data/yolo11/代码/yolo11-trash-det/42_demo/runs/yolo11n-coordattention/weights/best.pt")  # 替换为您自己的模型路径

# 打开摄像头
cap = cv2.VideoCapture(0)  # 参数 0 表示默认摄像头，如果有多个摄像头，可以尝试 1, 2...

# 设置窗口尺寸
window_width = 640  # 小窗口宽度
window_height = 480  # 小窗口高度

while True:
    # 读取一帧图像
    ret, frame = cap.read()
    if not ret:
        print("无法获取摄像头画面")
        break

    # 进行目标检测
    results = model(frame)

    # 遍历检测结果
    for result in results:
        boxes = result.boxes  # 获取边界框信息
        names = result.names  # 获取类别名称字典
        for box in boxes:
            # 提取边界框坐标 (x1, y1, x2, y2)
            x1, y1, x2, y2 = map(int, box.xyxy[0])

            # 计算目标的面积
            width = x2 - x1
            height = y2 - y1
            area = width * height

            # 只处理面积小于等于 160x320（51200 像素）的目标
            if area > 160 * 320:  # 如果面积超过限制，则跳过该目标
                continue

            # 计算物体的中心点
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2

            # 获取类别 ID 和名称
            cls_id = int(box.cls[0])  # 类别 ID
            cls_name = names[cls_id]  # 类别名称

            # 打印中心点坐标和类别名称
            print(f"Detected object: {cls_name}, center: ({center_x}, {center_y}), area: {area}")

            # 在图像上绘制中心点
            cv2.circle(frame, (center_x, center_y), 5, (0, 255, 0), -1)  # 绿色圆点表示中心
            cv2.rectangle(frame, (x1, y1), (x2, y2), (255, 0, 0), 2)  # 蓝色矩形框表示边界框

            # 在图像上显示类别名称
            label = f"{cls_name}"
            cv2.putText(frame, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 0, 0), 2)

    # 调整帧的大小以适应小窗口
    frame = cv2.resize(frame, (window_width, window_height))

    # 显示结果
    cv2.imshow("Real-time Object Detection", frame)

    # 按下 'q' 键退出
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

# 释放摄像头并关闭所有窗口
cap.release()
cv2.destroyAllWindows()