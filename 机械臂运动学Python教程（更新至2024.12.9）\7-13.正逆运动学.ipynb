{"cells": [{"cell_type": "code", "execution_count": 2, "id": "b368df05", "metadata": {}, "outputs": [], "source": ["from roboticstoolbox import *\n", "from spatialmath import *\n", "from math import pi\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 3, "id": "a8c1e519", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using matplotlib backend: <object object at 0x10be02330>\n"]}], "source": ["%matplotlib auto"]}, {"cell_type": "code", "execution_count": 4, "id": "ff291887", "metadata": {}, "outputs": [], "source": ["#标准DH"]}, {"cell_type": "code", "execution_count": 5, "id": "11799a13", "metadata": {}, "outputs": [], "source": ["DFbot = DHRobot(\n", "    [\n", "                    RevoluteDH(d=0.04145,alpha=np.pi/2,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteDH(alpha=-np.pi/2,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteDH(d=0.11,qlim=np.array([-np.pi,3/2*np.pi])),\n", "                  \n", "    ],\n", "    name=\"DF<PERSON>\",\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "bcf13bd6", "metadata": {}, "outputs": [{"data": {"text/plain": ["DHRobot: DFbot, 5 joints (RRRRR), dynamics, standard DH parameters\n", "┌─────┬─────────┬──────────┬────────┬─────────┬────────┐\n", "│ θⱼ  │   dⱼ    │    aⱼ    │   ⍺ⱼ   │   q⁻    │   q⁺   │\n", "├─────┼─────────┼──────────┼────────┼─────────┼────────┤\n", "│  q1 │ 0.04145 │        0 │  90.0° │ -180.0° │ 180.0° │\n", "│  q2 │       0 │ -0.08285 │   0.0° │ -180.0° │ 180.0° │\n", "│  q3 │       0 │ -0.08285 │   0.0° │ -180.0° │ 180.0° │\n", "│  q4 │       0 │        0 │ -90.0° │ -180.0° │ 180.0° │\n", "│  q5 │    0.11 │        0 │   0.0° │ -180.0° │ 270.0° │\n", "└─────┴─────────┴──────────┴────────┴─────────┴────────┘\n", "\n", "┌──┬──┐\n", "└──┴──┘"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot"]}, {"cell_type": "code", "execution_count": 7, "id": "106be4e7", "metadata": {}, "outputs": [{"data": {"text/plain": ["PyPlot3D backend, t = 0.05, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.plot([0,-np.pi/2,0,0,0])"]}, {"cell_type": "code", "execution_count": 8, "id": "32d71aff", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m-0.1657  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;4m 0.1515  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["T1=DFbot.fkine([0,0,0,0,0])\n", "T1"]}, {"cell_type": "code", "execution_count": 9, "id": "3fe42d7f", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on method plot in module roboticstoolbox.robot.BaseRobot:\n", "\n", "plot(q: Union[numpy.n<PERSON><PERSON>, <PERSON>[float], <PERSON><PERSON>[float], <PERSON>[float]], backend: Optional[Literal['swift', 'pyplot', 'pyplot2']] = None, block: bool = False, dt: float = 0.05, limits: Union[numpy.ndarray, List[float], <PERSON><PERSON>[float], Set[float], NoneType] = None, vellipse: bool = False, fellipse: bool = False, fig: Optional[str] = None, movie: Optional[str] = None, loop: bool = False, **kwargs) -> Union[swift.Swift.Swift, roboticstoolbox.backends.PyPlot.PyPlot.PyPlot, roboticstoolbox.backends.PyPlot.PyPlot2.PyPlot2] method of roboticstoolbox.robot.DHRobot.DHRobot instance\n", "    Graphical display and animation\n", "    \n", "    ``robot.plot(q, 'pyplot')`` displays a graphical view of a robot\n", "    based on the kinematic model and the joint configuration ``q``.\n", "    This is a stick figure polyline which joins the origins of the\n", "    link coordinate frames. The plot will autoscale with an aspect\n", "    ratio of 1.\n", "    \n", "    If ``q`` (m,n) representing a joint-space trajectory it will create an\n", "    animation with a pause of ``dt`` seconds between each frame.\n", "    \n", "    Attributes\n", "    ----------\n", "    q\n", "        The joint configuration of the robot.\n", "    backend\n", "        The graphical backend to use, currently 'swift'\n", "        and 'pyplot' are implemented. Defaults to 'swift' of a ``Robot``\n", "        and 'pyplot` for a ``DHRobot``\n", "    block\n", "        Block operation of the code and keep the figure open\n", "    dt\n", "        if q is a trajectory, this describes the delay in\n", "        seconds between frames\n", "    limits\n", "        Custom view limits for the plot. If not supplied will\n", "        autoscale, [x1, x2, y1, y2, z1, z2]\n", "        (this option is for 'pyplot' only)\n", "    vellipse\n", "        (Plot Option) Plot the velocity ellipse at the\n", "        end-effector (this option is for 'pyplot' only)\n", "    fellipse\n", "        (Plot Option) Plot the force ellipse at the\n", "        end-effector (this option is for 'pyplot' only)\n", "    fig\n", "        (Plot Option) The figure label to plot in (this option is for\n", "        'pyplot' only)\n", "    movie\n", "        (Plot Option) The filename to save the movie to (this option is for\n", "        'pyplot' only)\n", "    loop\n", "        (Plot Option) Loop the movie (this option is for\n", "        'pyplot' only)\n", "    jointaxes\n", "        (Plot Option) Plot an arrow indicating the axes in\n", "        which the joint revolves around(revolute joint) or translates\n", "        along (prosmatic joint) (this option is for 'pyplot' only)\n", "    e<PERSON><PERSON>e\n", "        (Plot Option) Plot the end-effector coordinate frame\n", "        at the location of the end-effector. Uses three arrows, red,\n", "        green and blue to indicate the x, y, and z-axes.\n", "        (this option is for 'pyplot' only)\n", "    shadow\n", "        (Plot Option) Plot a shadow of the robot in the x-y\n", "        plane. (this option is for 'pyplot' only)\n", "    name\n", "        (Plot Option) Plot the name of the robot near its base\n", "        (this option is for 'pyplot' only)\n", "    \n", "    Returns\n", "    -------\n", "    env\n", "        A reference to the environment object which controls the\n", "        figure\n", "    \n", "    Notes\n", "    -----\n", "    - By default this method will block until the figure is dismissed.\n", "        To avoid this set ``block=False``.\n", "    - For PyPlot, the polyline joins the origins of the link frames,\n", "        but for some Denavit-Hartenberg models those frames may not\n", "        actually be on the robot, ie. the lines to not neccessarily\n", "        represent the links of the robot.\n", "    \n", "    See Also\n", "    --------\n", "    :func:`teach`\n", "\n"]}], "source": ["help(DFbot.plot)"]}, {"cell_type": "code", "execution_count": 10, "id": "f32ad90d", "metadata": {}, "outputs": [], "source": ["#改进DH"]}, {"cell_type": "code", "execution_count": 11, "id": "ae7be331", "metadata": {}, "outputs": [], "source": ["DFbot = DHRobot(\n", "    [                 \n", "                    RevoluteMDH(d=0.04145,qlim=np.array([-np.pi,np.pi])),            \n", "                    RevoluteMDH(alpha=np.pi/2,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteMDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteMDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteMDH(alpha=-np.pi/2,d=0.075,qlim=np.array([-np.pi,np.pi]))\n", "                  \n", "    ],\n", "    name=\"DF<PERSON>\",\n", ")"]}, {"cell_type": "code", "execution_count": 12, "id": "0845ba6b", "metadata": {}, "outputs": [{"data": {"text/plain": ["PyPlot3D backend, t = 0.05, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.plot([0,0,0,0,0], block=False)"]}, {"cell_type": "code", "execution_count": 13, "id": "6f72f2ce", "metadata": {}, "outputs": [{"data": {"text/plain": ["PyPlot3D backend, t = 0.05, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.plot([0,-np.pi/3,np.pi/6,np.pi,0], block=False)"]}, {"cell_type": "code", "execution_count": 14, "id": "4e4b0842", "metadata": {}, "outputs": [], "source": ["#正运动学"]}, {"cell_type": "code", "execution_count": 15, "id": "2cdc6606", "metadata": {}, "outputs": [], "source": ["state0=[0,0,0,0,0]\n", "T0=DFbot.fkine(state0)\n", "state1=[0,-np.pi/6,0,np.pi/6,0]\n", "T1=DFbot.fkine(state1)"]}, {"cell_type": "code", "execution_count": null, "id": "b42f3a46", "metadata": {}, "outputs": [], "source": ["#逆运动学"]}, {"cell_type": "code", "execution_count": 17, "id": "c671b2cc", "metadata": {}, "outputs": [], "source": ["#<PERSON>emberg-Marquadt优化求解"]}, {"cell_type": "code", "execution_count": 18, "id": "ebead05a", "metadata": {}, "outputs": [], "source": ["sol = DFbot.ikine_LM(T1,q0=[0,np.pi/6,0,0,0],ilimit=100, slimit=100,joint_limits=True)"]}, {"cell_type": "code", "execution_count": 19, "id": "c9c54d41", "metadata": {}, "outputs": [{"data": {"text/plain": ["IKSolution(q=array([ 0.        , -0.48857351, -0.07004978,  0.55862339,  0.        ]), success=True, iterations=5, searches=1, residual=8.161927300468807e-08, reason='Success')"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["sol"]}, {"cell_type": "code", "execution_count": 20, "id": "50caa8c0", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0.        , -0.48857351, -0.07004978,  0.55862339,  0.        ])"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["sol.q"]}, {"cell_type": "code", "execution_count": 21, "id": "b4120e57", "metadata": {}, "outputs": [], "source": ["#Gauss-<PERSON>优化求解"]}, {"cell_type": "code", "execution_count": 22, "id": "cfddcb67", "metadata": {}, "outputs": [], "source": ["sol=DFbot.ikine_GN(T1,q0=[0,0,0,0,0],ilimit=1000, slimit=1000,joint_limits=True)"]}, {"cell_type": "code", "execution_count": 23, "id": "cfb19473", "metadata": {}, "outputs": [{"data": {"text/plain": ["IKSolution(q=array([ 1.96238062,  1.04734254,  0.2433365 , -2.30714312,  2.93825139]), success=False, iterations=1000, searches=1000, residual=0.0, reason='iteration and search limit reached, 1000 numpy.LinAlgError encountered')"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["sol"]}, {"cell_type": "code", "execution_count": 24, "id": "7577f1e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 1.96238062,  1.04734254,  0.2433365 , -2.30714312,  2.93825139])"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["sol.q"]}, {"cell_type": "code", "execution_count": 25, "id": "a1c33057", "metadata": {}, "outputs": [], "source": ["#<PERSON>-<PERSON><PERSON><PERSON>优化求解"]}, {"cell_type": "code", "execution_count": 26, "id": "0e447bb7", "metadata": {}, "outputs": [], "source": ["sol=DFbot.ikine_NR(T1,q0=[0,0,0,0,0],ilimit=1000, slimit=1000,joint_limits=True)"]}, {"cell_type": "code", "execution_count": 27, "id": "e91a3009", "metadata": {}, "outputs": [{"data": {"text/plain": ["IKSolution(q=array([-0.39688697, -0.0897743 , -0.72217098, -0.66859004, -0.08469746]), success=False, iterations=1000, searches=1000, residual=0.0, reason='iteration and search limit reached, 1000 numpy.LinAlgError encountered')"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["sol"]}, {"cell_type": "code", "execution_count": 28, "id": "c1040e3a", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-0.39688697, -0.0897743 , -0.72217098, -0.66859004, -0.08469746])"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["sol.q"]}, {"cell_type": "code", "execution_count": null, "id": "ef23d7a0", "metadata": {}, "outputs": [], "source": ["#quadprog优化求解"]}, {"cell_type": "code", "execution_count": null, "id": "f33ea859", "metadata": {}, "outputs": [], "source": ["sol=DFbot.ikine_QP(T1,q0=[0,0,0,0,0],ilimit=100, slimit=100,joint_limits=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c59288a5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5f67b9d9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5b464388", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}