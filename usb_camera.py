from flask import Flask, Response
import cv2

# 初始化Flask应用
app = Flask(__name__)

# 捕获视频流，0代表第一个摄像头
camera = cv2.VideoCapture(0)


def generate_frames():
    """生成视频帧"""
    while True:
        # 读取摄像头中的每一帧
        success, frame = camera.read()
        if not success:
            break
        else:
            # 将图像编码为JPEG格式
            ret, buffer = cv2.imencode('.jpg', frame)
            frame = buffer.tobytes()

            # 使用yield返回每帧数据，构建MJPEG流
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')


@app.route('/')
def index():
    """主页路由"""
    return "欢迎来到IP摄像头服务！请访问 /video_feed 查看视频流。"


@app.route('/video_feed')
def video_feed():
    """视频流路由"""
    return Response(generate_frames(), mimetype='multipart/x-mixed-replace; boundary=frame')


if __name__ == '__main__':
    # 运行Flask应用，并监听所有可用的网络接口
    app.run(host='0.0.0.0', port=5000)