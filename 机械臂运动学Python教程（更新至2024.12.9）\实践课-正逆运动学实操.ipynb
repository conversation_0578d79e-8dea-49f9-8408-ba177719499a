{"cells": [{"cell_type": "code", "execution_count": 209, "id": "32969978-25b7-4027-90e6-2c8f63fd7f9a", "metadata": {}, "outputs": [], "source": ["%matplotlib qt "]}, {"cell_type": "code", "execution_count": 210, "id": "535e1234-8ab2-43cb-8464-9f7015fb115c", "metadata": {}, "outputs": [], "source": ["from roboticstoolbox import *\n", "from spatialmath import *\n", "from math import pi\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 211, "id": "cf065a66-033b-493e-a61e-041d38ac8b0f", "metadata": {}, "outputs": [], "source": ["import time\n", "from Arm_Lib import Arm_Device"]}, {"cell_type": "code", "execution_count": 212, "id": "bed9255e-006c-424a-9715-7f1c00d61995", "metadata": {}, "outputs": [], "source": ["DFbot = DHRobot(\n", "    [\n", "                    RevoluteMDH(d=0.04145,qlim=np.array([-np.pi/2,np.pi/2])),\n", "                    RevoluteMDH(alpha=np.pi/2,qlim=np.array([-np.pi/2,np.pi/2])),\n", "                    RevoluteMDH(a=-0.08285,qlim=np.array([-np.pi/2,np.pi/2])),\n", "                    RevoluteMDH(a=-0.08285,qlim=np.array([-np.pi/2,np.pi])),\n", "                    RevoluteMDH(alpha=-np.pi/2,d=0.11,qlim=np.array([0,np.pi])),  \n", " \n", "    ],\n", "    name=\"DF<PERSON>\",\n", ")"]}, {"cell_type": "code", "execution_count": 213, "id": "4ec63acb-05f6-4fa7-8b08-bacda61b03c2", "metadata": {}, "outputs": [{"data": {"text/plain": ["PyPlot3D backend, t = 0.05, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 213, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.plot([0,0,0,0,0])"]}, {"cell_type": "code", "execution_count": 214, "id": "bec5347d-8f7a-4b17-97df-a29a9f80ace8", "metadata": {}, "outputs": [], "source": ["state0=[0,0,0,0,0]"]}, {"cell_type": "code", "execution_count": 215, "id": "84ad530f-84f3-438a-9552-fa739d824bb7", "metadata": {}, "outputs": [], "source": ["deg=[x/np.pi*180 for x in state0]"]}, {"cell_type": "code", "execution_count": 216, "id": "0ce05ba6-6592-4151-b41a-a02649410b64", "metadata": {}, "outputs": [{"data": {"text/plain": ["[0.0, 0.0, 0.0, 0.0, 0.0]"]}, "execution_count": 216, "metadata": {}, "output_type": "execute_result"}], "source": ["deg"]}, {"cell_type": "code", "execution_count": 217, "id": "c8cb02a6-b558-4c5b-bda5-bc681d9b0915", "metadata": {}, "outputs": [], "source": ["def convert_deg(deg):\n", "    d1=deg[0]+90\n", "    d2=-deg[1]\n", "    d3=90-deg[2]\n", "    d4=180-deg[3]\n", "    d5=deg[4]+90\n", "    return [d1,d2,d3,d4,d5]"]}, {"cell_type": "code", "execution_count": 218, "id": "6cfb0370-eb84-4324-be9a-e1b99ad46862", "metadata": {}, "outputs": [], "source": ["state0_deg=convert_deg(deg)"]}, {"cell_type": "code", "execution_count": 219, "id": "514d5b5d-cf93-4bba-9c3c-52ad959051cf", "metadata": {}, "outputs": [{"data": {"text/plain": ["[90.0, -0.0, 90.0, 180.0, 90.0]"]}, "execution_count": 219, "metadata": {}, "output_type": "execute_result"}], "source": ["state0_deg"]}, {"cell_type": "code", "execution_count": 221, "id": "2ad23e41-438e-4242-ba95-ae1d068733ca", "metadata": {}, "outputs": [], "source": ["Arm = Arm_Device()\n", "time.sleep(1)\n", "d_claw=0\n", "Arm.Arm_serial_servo_write6(state0_deg[0],state0_deg[1],state0_deg[2],state0_deg[3],state0_deg[4],d_claw, 1500)\n", "time.sleep(0.1)"]}, {"cell_type": "code", "execution_count": null, "id": "a4a8bd10-bb06-4c65-ad46-dff1f54596d6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 222, "id": "58145afa-c137-4509-a1dc-70fe57d38a76", "metadata": {}, "outputs": [], "source": ["state1=[0,-np.pi/3,0,np.pi,0]"]}, {"cell_type": "code", "execution_count": 223, "id": "26a0befd-8e87-42ae-ade7-5882c40b4772", "metadata": {}, "outputs": [{"data": {"text/plain": ["PyPlot3D backend, t = 0.05, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 223, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.plot(state1)"]}, {"cell_type": "code", "execution_count": 224, "id": "e7617676-a381-4897-a3be-3528eb33c518", "metadata": {}, "outputs": [], "source": ["T1=DFbot.fkine(state1)"]}, {"cell_type": "code", "execution_count": 225, "id": "fa1fdc90-7567-49a8-9a7b-b5cee421f5e1", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m-0.5     \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-0.866   \u001b[0m \u001b[38;5;4m-0.1781  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m 1       \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0       \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.866   \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;1m-0.5     \u001b[0m \u001b[38;5;4m 0.13    \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 225, "metadata": {}, "output_type": "execute_result"}], "source": ["T1"]}, {"cell_type": "code", "execution_count": 226, "id": "a8f10b8f-b94a-45e6-ad89-cbac988348b8", "metadata": {}, "outputs": [], "source": ["deg1=[x/np.pi*180 for x in state1]"]}, {"cell_type": "code", "execution_count": 228, "id": "5ddc573b-5327-4aab-a416-ce9a5fcf37c3", "metadata": {}, "outputs": [], "source": ["state1_deg=convert_deg(deg1)"]}, {"cell_type": "code", "execution_count": 229, "id": "4daac5ed-677b-4278-9498-352cca34648d", "metadata": {}, "outputs": [], "source": ["Arm = Arm_Device()\n", "time.sleep(1)\n", "d_claw=0\n", "Arm.Arm_serial_servo_write6(state1_deg[0],state1_deg[1],state1_deg[2],state1_deg[3],state0_deg[4],d_claw, 1500)\n", "time.sleep(1)"]}, {"cell_type": "code", "execution_count": null, "id": "df7633fa-3009-4865-9266-e48421291783", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 230, "id": "6ad8b3a9-83d4-473f-b3d1-a3b46f72501e", "metadata": {}, "outputs": [], "source": ["T2=np.array(T1)\n", "T2[0,-1]=-0.255\n", "T2[2,-1]=0.02"]}, {"cell_type": "code", "execution_count": 231, "id": "2b648794-2b56-45cb-aab0-3d7afb5067e5", "metadata": {}, "outputs": [], "source": ["sol = DFbot.ikine_LM(T2,q0=[0,0,0,0,0],ilimit=100, slimit=100,joint_limits=True)"]}, {"cell_type": "code", "execution_count": 232, "id": "37aadf15-a81d-4ddc-85fd-f46d8b25347b", "metadata": {}, "outputs": [{"data": {"text/plain": ["IKSolution(q=array([ 0.        , -0.38115342,  0.3482598 ,  2.12728867,  0.        ]), success=True, iterations=8, searches=1, residual=1.7092975388849101e-07, reason='Success')"]}, "execution_count": 232, "metadata": {}, "output_type": "execute_result"}], "source": ["sol"]}, {"cell_type": "code", "execution_count": 233, "id": "dd033c7d-7417-450d-8e08-bdd7d0e944f1", "metadata": {}, "outputs": [{"data": {"text/plain": ["PyPlot3D backend, t = 0.05, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 233, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.plot(sol.q)"]}, {"cell_type": "code", "execution_count": 234, "id": "a16bf203-cf04-4f29-9656-14492e9c5e26", "metadata": {}, "outputs": [], "source": ["state2=sol.q"]}, {"cell_type": "code", "execution_count": 235, "id": "0a4cd5fb-3c3e-46bb-acc8-f41bd37997d0", "metadata": {}, "outputs": [], "source": ["deg2=[x/np.pi*180 for x in state2]"]}, {"cell_type": "code", "execution_count": 236, "id": "1f3024b7-c806-4bdb-a332-6b53e8a9bb88", "metadata": {}, "outputs": [], "source": ["state2_deg=convert_deg(deg2)"]}, {"cell_type": "code", "execution_count": 239, "id": "a436d362-e770-42a7-87b7-fd1bf452a8ec", "metadata": {}, "outputs": [], "source": ["Arm = Arm_Device()\n", "time.sleep(1)\n", "d_claw=0\n", "Arm.Arm_serial_servo_write6(state2_deg[0],state2_deg[1],state2_deg[2],state2_deg[3],state2_deg[4],d_claw, 1500)\n", "time.sleep(1)"]}, {"cell_type": "code", "execution_count": 240, "id": "9d562a19-d3c2-4074-9eb3-ed49a831fd25", "metadata": {}, "outputs": [], "source": ["Arm = Arm_Device()\n", "time.sleep(1)\n", "d_claw=0\n", "Arm.Arm_serial_servo_write6(state2_deg[0],state2_deg[1],state2_deg[2],state2_deg[3],state2_deg[4],d_claw, 1500)\n", "time.sleep(1)\n", "d_claw=180\n", "Arm.Arm_serial_servo_write6(state2_deg[0],state2_deg[1],state2_deg[2],state2_deg[3],state2_deg[4],d_claw, 1500)\n", "time.sleep(1)\n", "d_claw=180\n", "<PERSON>.Arm_serial_servo_write6(state2_deg[0],47,state2_deg[2],state2_deg[3],state2_deg[4],d_claw, 1500)\n", "time.sleep(0.1)"]}, {"cell_type": "code", "execution_count": null, "id": "200b5b97-c741-497e-8aeb-163bce0dac58", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}