{"cells": [{"cell_type": "code", "execution_count": 1, "id": "08ed6bce", "metadata": {}, "outputs": [], "source": ["#pytorch基础"]}, {"cell_type": "code", "execution_count": 2, "id": "3074f432", "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 14, "id": "fcf45c1f", "metadata": {}, "outputs": [], "source": ["data = [[1, 2],[3, 4]]\n", "x_data = torch.tensor(data)"]}, {"cell_type": "code", "execution_count": 15, "id": "ee6dd91f", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[1, 2],\n", "        [3, 4]])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["x_data"]}, {"cell_type": "code", "execution_count": 16, "id": "3976eab2", "metadata": {}, "outputs": [], "source": ["np_array = np.array(data)\n", "x_np = torch.from_numpy(np_array)"]}, {"cell_type": "code", "execution_count": 17, "id": "e64172b9", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[1, 2],\n", "        [3, 4]])"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["x_np"]}, {"cell_type": "code", "execution_count": 18, "id": "31b237c9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape of tensor: <PERSON>.<PERSON><PERSON>([3, 4])\n", "Datatype of tensor: torch.float32\n", "Device tensor is stored on: cpu\n"]}], "source": ["tensor = torch.rand(3,4)\n", "\n", "print(f\"Shape of tensor: {tensor.shape}\")\n", "print(f\"Datatype of tensor: {tensor.dtype}\")\n", "print(f\"Device tensor is stored on: {tensor.device}\")"]}, {"cell_type": "code", "execution_count": 19, "id": "0b173371", "metadata": {}, "outputs": [], "source": ["# We move our tensor to the GPU if available\n", "if torch.cuda.is_available():\n", "    tensor = tensor.to(\"cuda\")"]}, {"cell_type": "code", "execution_count": 20, "id": "10bb039b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First row: tensor([1., 1., 1., 1.])\n", "First column: tensor([1., 1., 1., 1.])\n", "Last column: tensor([1., 1., 1., 1.])\n", "tensor([[1., 0., 1., 1.],\n", "        [1., 0., 1., 1.],\n", "        [1., 0., 1., 1.],\n", "        [1., 0., 1., 1.]])\n"]}], "source": ["tensor = torch.ones(4, 4)\n", "print(f\"First row: {tensor[0]}\")\n", "print(f\"First column: {tensor[:, 0]}\")\n", "print(f\"Last column: {tensor[..., -1]}\")\n", "tensor[:,1] = 0\n", "print(tensor)"]}, {"cell_type": "code", "execution_count": 11, "id": "83a6acc3", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[1., 0., 1., 1.],\n", "        [1., 0., 1., 1.],\n", "        [1., 0., 1., 1.],\n", "        [1., 0., 1., 1.]])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# This computes the matrix multiplication between two tensors. y1, y2, y3 will have the same value\n", "# ``tensor.T`` returns the transpose of a tensor\n", "y1 = tensor @ tensor.T\n", "y2 = tensor.matmul(tensor.T)\n", "\n", "y3 = torch.rand_like(y1)\n", "torch.matmul(tensor, tensor.T, out=y3)\n", "\n", "\n", "# This computes the element-wise product. z1, z2, z3 will have the same value\n", "z1 = tensor * tensor\n", "z2 = tensor.mul(tensor)\n", "\n", "z3 = torch.rand_like(tensor)\n", "torch.mul(tensor, tensor, out=z3)"]}, {"cell_type": "code", "execution_count": 13, "id": "f732ce54", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[1., 0., 1., 1.],\n", "        [1., 0., 1., 1.],\n", "        [1., 0., 1., 1.],\n", "        [1., 0., 1., 1.]])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["z1"]}, {"cell_type": "code", "execution_count": 21, "id": "998bb20f", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[3., 3., 3., 3.],\n", "        [3., 3., 3., 3.],\n", "        [3., 3., 3., 3.],\n", "        [3., 3., 3., 3.]])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["y1"]}, {"cell_type": "code", "execution_count": null, "id": "c3e83b3a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9bcaf68c", "metadata": {}, "outputs": [], "source": ["##DNN+机械臂"]}, {"cell_type": "code", "execution_count": 22, "id": "b368df05", "metadata": {}, "outputs": [], "source": ["from roboticstoolbox import *\n", "from spatialmath import *\n", "from math import pi\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 23, "id": "a8c1e519", "metadata": {}, "outputs": [], "source": ["%matplotlib notebook"]}, {"cell_type": "code", "execution_count": 43, "id": "5f67b9d9", "metadata": {}, "outputs": [], "source": ["DFbot = DHRobot(\n", "    [                 \n", "                    RevoluteMDH(d=0.04145,qlim=np.array([-np.pi/2,np.pi/2])),            \n", "                    RevoluteMDH(alpha=np.pi/2,qlim=np.array([-np.pi/2,np.pi/2])),\n", "                    RevoluteMDH(a=-0.08285,qlim=np.array([-np.pi/2,np.pi/2])),\n", "                    RevoluteMDH(a=-0.08285,qlim=np.array([-np.pi/2,np.pi])),\n", "                    RevoluteMDH(alpha=-np.pi/2,d=0.11,qlim=np.array([0,np.pi]))\n", "                  \n", "    ],\n", "    name=\"DF<PERSON>\",\n", ")"]}, {"cell_type": "code", "execution_count": 25, "id": "94b63d39", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[-1.57079633,  1.57079633],\n", "       [-1.57079633,  1.57079633],\n", "       [-1.57079633,  1.57079633],\n", "       [-1.57079633,  3.14159265],\n", "       [ 0.        ,  3.14159265]])"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.qlim.T"]}, {"cell_type": "code", "execution_count": 26, "id": "6c762179", "metadata": {}, "outputs": [], "source": ["n_dof=5"]}, {"cell_type": "code", "execution_count": 27, "id": "5b464388", "metadata": {}, "outputs": [], "source": ["def get_random_q(robot, batch_size, max_inc=0.2):\n", "    \"\"\"Get 2 random configurations inside the robot limits at a max_inc distance\"\"\"\n", "    q_init = np.random.rand(batch_size, n_dof)\n", "    q_init = q_init * (robot.qlim.T[:,1] - robot.qlim.T[:,0]) + robot.qlim.T[:,0]\n", "    q_inc = (np.random.rand(batch_size, n_dof) * 2 - 1) * max_inc\n", "    q_final = np.clip(q_init + q_inc, robot.qlim.T[:,0], robot.qlim.T[:,1])\n", "    return q_init, q_final "]}, {"cell_type": "code", "execution_count": 44, "id": "551ef4ca", "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([[-1.20165826, -1.05254322, -0.35617976,  1.48369809,  2.22858811],\n", "        [ 1.14837088,  1.38991361, -0.62821715,  0.59760322,  1.67303888],\n", "        [ 0.96699469,  0.9140224 ,  1.45097172,  2.0649386 ,  0.53958998]]),\n", " array([[-1.13911107, -0.91143097, -0.42462952,  1.37677457,  2.35216172],\n", "        [ 1.13231779,  1.57079633, -0.78894222,  0.55705427,  1.54603111],\n", "        [ 0.99263131,  0.8151036 ,  1.57079633,  2.23186542,  0.70656119]]))"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["get_random_q(<PERSON><PERSON><PERSON>, 3, max_inc=0.2)"]}, {"cell_type": "code", "execution_count": 29, "id": "cf6b5150", "metadata": {}, "outputs": [], "source": ["def get_batch(robot, batch_size):\n", "    \"\"\" Create a batch (q_init + euclidean_velocity) --> (configuration_velocity)\"\"\"\n", "    q_init, q_final = get_random_q(robot, batch_size)\n", "    ee_pose_init = np.zeros((batch_size, 3*4))\n", "    ee_pose_final = np.zeros((batch_size, 3*4))\n", "    for j, init in enumerate(q_init):\n", "        se_init=DFbot.fkine(init)\n", "        ee_pose_init[j] = np.array(se_init)[0:3,:].flatten()\n", "    for j, final in enumerate(q_final):\n", "        se_final=DFbot.fkine(final)\n", "        ee_pose_final[j] = np.array(se_final)[0:3,:].flatten()\n", "        \n", "    return np.hstack((q_init, ee_pose_final - ee_pose_init)), q_final - q_init"]}, {"cell_type": "code", "execution_count": 30, "id": "e0ab393f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([[ 0.63032944,  0.521617  , -1.45147291, -0.0698886 ,  0.06810977,\n", "          0.33018914,  0.12134528, -0.20330125, -0.03433703,  0.04217241,\n", "          0.08380648, -0.2234859 , -0.01596188,  0.29063906, -0.0572587 ,\n", "          0.29547372,  0.00762637]]),\n", " array([[-0.11086414,  0.1763511 ,  0.05161363,  0.19091969, -0.06810977]]))"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["get_batch(<PERSON><PERSON><PERSON>, 1)"]}, {"cell_type": "code", "execution_count": 45, "id": "f38203a3", "metadata": {}, "outputs": [], "source": ["def get_dataset(robot, batch_size, num_batchs):\n", "    xt = np.empty((num_batchs, batch_size, 3*4 + n_dof))\n", "    yt = np.empty((num_batchs, batch_size, n_dof))\n", "    for i in range(num_batchs):\n", "        x, y = get_batch(robot, batch_size)\n", "        xt[i] = x\n", "        yt[i] = y\n", "        if (i+1)%int(num_batchs/10) == 0:\n", "            print(\"{}%\".format(((i+1)/num_batchs)*100))\n", "    return xt, yt"]}, {"cell_type": "code", "execution_count": 46, "id": "19dbfe85", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Device: cpu\n", "10.0%\n", "20.0%\n", "30.0%\n", "40.0%\n", "50.0%\n", "60.0%\n", "70.0%\n", "80.0%\n", "90.0%\n", "100.0%\n"]}], "source": ["device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n", "print(\"Device: {}\".format(device))\n", "x, y = get_dataset(<PERSON><PERSON><PERSON>, 20, 50)\n", "x_t = torch.tensor(x).float().to(device)\n", "y_t = torch.tensor(y).float().to(device)"]}, {"cell_type": "code", "execution_count": 40, "id": "7dd0dc6e", "metadata": {}, "outputs": [], "source": ["import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.optim as optim\n", "\n", "layer_size = 100 \n", "\n", "\n", "class Net(nn.Module):\n", "    def __init__(self, robot):\n", "        super().__init__()\n", "        self.fc1 = nn.Linear(n_dof + 3*4, layer_size)\n", "        self.fc2 = nn.Linear(layer_size, layer_size)\n", "        self.fc3 = nn.Linear(layer_size, n_dof)\n", "    def forward(self, x):\n", "        x = F.relu(self.fc1(x))\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.fc3(x)\n", "        return x"]}, {"cell_type": "code", "execution_count": null, "id": "b65aca1e", "metadata": {}, "outputs": [], "source": ["def train_model(xt, yt, epochs, net, optimizer):\n", "    net.train()\n", "    loss_log = []\n", "    for epoch in range(epochs):\n", "        running_loss = 0.0\n", "        \n", "        for x, y in zip(xt, yt):\n", "            # zero the parameter gradients\n", "            optimizer.zero_grad()\n", "\n", "            # forward\n", "            # track history if only in train\n", "            with torch.set_grad_enabled(True):\n", "                outputs = net(x)\n", "                loss = criterion(outputs, y)\n", "                \n", "                # backward + optimize only if in training phase\n", "                loss.backward()\n", "                optimizer.step()#反向传播\n", "\n", "                # statistics\n", "                running_loss += loss.item() * x_t.size(0) \n", "        print('[%d, %5d] loss: %.3f' % (epoch + 1, len(x_t), running_loss / len(x_t) ))\n", "        loss_log.append(running_loss / len(x_t))\n", "    print('Finished Training')\n", "    return loss_log"]}, {"cell_type": "code", "execution_count": 42, "id": "1064d0e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Trainable parameters:  12405\n"]}], "source": ["net = Net(DFbot)\n", "net.to(device)\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer = torch.optim.Adam(net.parameters(), lr=0.001)\n", "\n", "model_parameters = filter(lambda p: p.requires_grad, net.parameters())\n", "params = sum([np.prod(p.size()) for p in model_parameters])\n", "print(\"Trainable parameters: \", params)"]}, {"cell_type": "code", "execution_count": 111, "id": "67541fea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 50000] loss: 101.247\n", "[2, 50000] loss: 55.979\n", "[3, 50000] loss: 48.955\n", "[4, 50000] loss: 45.463\n", "[5, 50000] loss: 43.478\n", "[6, 50000] loss: 42.003\n", "[7, 50000] loss: 40.856\n", "[8, 50000] loss: 39.979\n", "[9, 50000] loss: 39.246\n", "[10, 50000] loss: 38.538\n", "[11, 50000] loss: 37.923\n", "[12, 50000] loss: 37.180\n", "[13, 50000] loss: 36.398\n", "[14, 50000] loss: 35.827\n", "[15, 50000] loss: 35.355\n", "[16, 50000] loss: 34.944\n", "[17, 50000] loss: 34.529\n", "[18, 50000] loss: 34.193\n", "[19, 50000] loss: 33.910\n", "[20, 50000] loss: 33.654\n", "[21, 50000] loss: 33.421\n", "[22, 50000] loss: 33.157\n", "[23, 50000] loss: 32.908\n", "[24, 50000] loss: 32.712\n", "[25, 50000] loss: 32.549\n", "[26, 50000] loss: 32.405\n", "[27, 50000] loss: 32.260\n", "[28, 50000] loss: 32.122\n", "[29, 50000] loss: 32.007\n", "[30, 50000] loss: 31.910\n", "[31, 50000] loss: 31.815\n", "[32, 50000] loss: 31.721\n", "[33, 50000] loss: 31.648\n", "[34, 50000] loss: 31.583\n", "[35, 50000] loss: 31.522\n", "[36, 50000] loss: 31.461\n", "[37, 50000] loss: 31.401\n", "[38, 50000] loss: 31.339\n", "[39, 50000] loss: 31.283\n", "[40, 50000] loss: 31.226\n", "[41, 50000] loss: 31.176\n", "[42, 50000] loss: 31.123\n", "[43, 50000] loss: 31.078\n", "[44, 50000] loss: 31.033\n", "[45, 50000] loss: 30.993\n", "[46, 50000] loss: 30.946\n", "[47, 50000] loss: 30.907\n", "[48, 50000] loss: 30.870\n", "[49, 50000] loss: 30.837\n", "[50, 50000] loss: 30.802\n", "[51, 50000] loss: 30.771\n", "[52, 50000] loss: 30.739\n", "[53, 50000] loss: 30.706\n", "[54, 50000] loss: 30.673\n", "[55, 50000] loss: 30.647\n", "[56, 50000] loss: 30.621\n", "[57, 50000] loss: 30.590\n", "[58, 50000] loss: 30.556\n", "[59, 50000] loss: 30.504\n", "[60, 50000] loss: 30.451\n", "[61, 50000] loss: 30.401\n", "[62, 50000] loss: 30.315\n", "[63, 50000] loss: 30.217\n", "[64, 50000] loss: 30.161\n", "[65, 50000] loss: 30.108\n", "[66, 50000] loss: 30.059\n", "[67, 50000] loss: 29.995\n", "[68, 50000] loss: 29.894\n", "[69, 50000] loss: 29.838\n", "[70, 50000] loss: 29.800\n", "[71, 50000] loss: 29.768\n", "[72, 50000] loss: 29.742\n", "[73, 50000] loss: 29.714\n", "[74, 50000] loss: 29.688\n", "[75, 50000] loss: 29.660\n", "[76, 50000] loss: 29.636\n", "[77, 50000] loss: 29.607\n", "[78, 50000] loss: 29.578\n", "[79, 50000] loss: 29.552\n", "[80, 50000] loss: 29.523\n", "[81, 50000] loss: 29.500\n", "[82, 50000] loss: 29.475\n", "[83, 50000] loss: 29.432\n", "[84, 50000] loss: 29.388\n", "[85, 50000] loss: 29.356\n", "[86, 50000] loss: 29.330\n", "[87, 50000] loss: 29.305\n", "[88, 50000] loss: 29.282\n", "[89, 50000] loss: 29.260\n", "[90, 50000] loss: 29.234\n", "[91, 50000] loss: 29.215\n", "[92, 50000] loss: 29.197\n", "[93, 50000] loss: 29.180\n", "[94, 50000] loss: 29.158\n", "[95, 50000] loss: 29.137\n", "[96, 50000] loss: 29.123\n", "[97, 50000] loss: 29.110\n", "[98, 50000] loss: 29.099\n", "[99, 50000] loss: 29.087\n", "[100, 50000] loss: 29.077\n", "Finished Training\n"]}], "source": ["log = train_model(x_t, y_t, 100, net, optimizer) "]}, {"cell_type": "code", "execution_count": 47, "id": "b61c267e", "metadata": {}, "outputs": [], "source": ["def evaluate(robot, net, device, N=5000):\n", "    error = np.zeros((N, 4*4))\n", "    agg_error=[]\n", "    net.eval()\n", "    for i in range(N):\n", "        x, y = get_batch(robot, 1)\n", "        x_t = torch.tensor(x).float().to(device)\n", "        out = net(x_t)\n", "        y = y.flatten()\n", "        y_out = out[0].cpu().detach().numpy()\n", "        ee_net = robot.fkine(y_out)\n", "        ee_in = robot.fkine(y)\n", "        err_i = np.abs(ee_net - ee_in)\n", "        error[i] = err_i.flatten()\n", "        agg_error.append( sum([abs(x) for x in error[i]]))\n", "    return agg_error,error"]}, {"cell_type": "code", "execution_count": null, "id": "4a137499", "metadata": {}, "outputs": [], "source": ["agg_error,error=evaluate(DFbot, net, device, N=5000)"]}, {"cell_type": "code", "execution_count": 49, "id": "89cd91d4", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1.5026351140831382,\n", " 1.0252007903735483,\n", " 0.9363938470385501,\n", " 0.5247218194482368,\n", " 1.643936820919089,\n", " 1.0537983798592743,\n", " 0.8212394007221517,\n", " 0.8089649045217133,\n", " 0.3846124014122635,\n", " 0.565539719165427,\n", " 0.48196743705209044,\n", " 0.8388621995481996,\n", " 0.9648094347961744,\n", " 0.3559543092911554,\n", " 1.5191352035421741,\n", " 0.73517502307668,\n", " 1.199092344502057,\n", " 0.5875294965173545,\n", " 0.9397492338371165,\n", " 0.6867164811717265,\n", " 0.876069247938598,\n", " 0.30133873871685585,\n", " 0.28906758792824927,\n", " 0.3215293927100597,\n", " 0.89901412117892,\n", " 0.5713329629780358,\n", " 1.0876727590540936,\n", " 0.16965912546815892,\n", " 1.0098197955419692,\n", " 1.22437185689679,\n", " 0.7210106437417895,\n", " 0.7215503736537441,\n", " 0.33506369177648104,\n", " 1.6045444057901859,\n", " 0.5222279872520234,\n", " 0.37120467166993054,\n", " 0.1685564584159696,\n", " 1.579979892706212,\n", " 1.3605486540466496,\n", " 0.6820413762542753,\n", " 0.31574728939965013,\n", " 0.24031460354448062,\n", " 0.9805527293385832,\n", " 1.0973848548295562,\n", " 0.27008094680405487,\n", " 0.5386812118089188,\n", " 0.5305046241956604,\n", " 0.7916147851515838,\n", " 0.9778297490509816,\n", " 0.5534546682150683,\n", " 0.4752436670881468,\n", " 0.5893653948817625,\n", " 1.1028958468987453,\n", " 1.3848587196476192,\n", " 0.9712726193482553,\n", " 1.1177158654390984,\n", " 0.5114203838661803,\n", " 1.1353877877090066,\n", " 0.6406036801514791,\n", " 0.5605674849969924,\n", " 0.7237011658420897,\n", " 0.8627182118617316,\n", " 0.9149795959526493,\n", " 0.7003075947051427,\n", " 0.9714991033508802,\n", " 0.445281999639348,\n", " 1.0136028587934127,\n", " 1.217325658618267,\n", " 1.587715469180339,\n", " 0.7383750529838367,\n", " 1.7236506792306991,\n", " 0.16060369316686454,\n", " 1.1420406345711993,\n", " 1.0781129940673093,\n", " 0.23507834031673763,\n", " 0.743034545068904,\n", " 0.7112601434987438,\n", " 0.7418818707493063,\n", " 0.5684318990820392,\n", " 0.8123847823172992,\n", " 0.6396830436859542,\n", " 1.1871723658244577,\n", " 1.2017001000387006,\n", " 1.1246200319072566,\n", " 1.0329551290747545,\n", " 0.998769157455297,\n", " 0.7557808687288448,\n", " 1.231006672921545,\n", " 0.6888007475221866,\n", " 0.2467157454462165,\n", " 1.065554389285097,\n", " 0.6242530503017116,\n", " 0.4143824638126344,\n", " 0.31459916898423507,\n", " 0.33435395883234076,\n", " 1.1085366484984487,\n", " 0.8576953082449086,\n", " 2.074055733033487,\n", " 0.39536341316678714,\n", " 0.7049684629652528,\n", " 0.8339915215213951,\n", " 1.110355757091684,\n", " 0.2189160018531271,\n", " 0.530762171875687,\n", " 0.7339368481142231,\n", " 1.297297250359608,\n", " 0.13656311550105343,\n", " 1.267013022006924,\n", " 0.7911090772646516,\n", " 0.8007406953983375,\n", " 0.42857025543756344,\n", " 0.8917107950027646,\n", " 0.2985382017679319,\n", " 0.892587228720972,\n", " 0.5133062872170802,\n", " 0.6325901265375575,\n", " 0.6257836313242691,\n", " 0.9583715498022556,\n", " 0.34751378775420394,\n", " 0.7859431995376382,\n", " 0.7949655404068781,\n", " 0.7844604470439539,\n", " 2.1043360921579612,\n", " 1.0289928476156107,\n", " 0.34697218013185366,\n", " 0.34049343848438135,\n", " 1.060289733274434,\n", " 1.3312816302139754,\n", " 0.6086149014610711,\n", " 0.4661489091522937,\n", " 0.6317774148446116,\n", " 0.8398412554283603,\n", " 0.43065161454158796,\n", " 1.099143920721634,\n", " 0.5657541454272703,\n", " 0.6667767344442113,\n", " 1.1003602283446186,\n", " 0.3068861111410124,\n", " 0.8291613032024846,\n", " 0.2729015745593944,\n", " 0.5518124222876419,\n", " 1.066055841140539,\n", " 0.34748770693742137,\n", " 1.2971334635268341,\n", " 0.8421584644067494,\n", " 0.7245139588163076,\n", " 2.052297532963842,\n", " 0.7089748922808201,\n", " 1.2279372435024216,\n", " 0.5071114752691412,\n", " 0.512213335840948,\n", " 0.7201533680317562,\n", " 1.12041503074198,\n", " 0.8473353693429099,\n", " 1.3749188961607304,\n", " 1.4894731540062638,\n", " 1.055424987959245,\n", " 0.7858839166643788,\n", " 1.0363988162670348,\n", " 0.6733460774969503,\n", " 0.35025094610418506,\n", " 1.3922617732578726,\n", " 0.7202245193674108,\n", " 0.30268693216158,\n", " 0.8134525688450376,\n", " 0.3113658831630216,\n", " 1.1932209758983077,\n", " 0.7906127945177448,\n", " 0.824747082344976,\n", " 0.27759082024909715,\n", " 0.8400257250917011,\n", " 0.4560123110381099,\n", " 0.3128115483899504,\n", " 0.4592706663259415,\n", " 1.7485171974937952,\n", " 0.8917023095060476,\n", " 1.9223771741370066,\n", " 1.515456495658578,\n", " 0.9669798435066841,\n", " 0.6291903748682136,\n", " 0.3937473343094364,\n", " 0.7699101999612504,\n", " 1.3433261255786788,\n", " 0.7759954099506485,\n", " 0.4745181065980547,\n", " 0.7938527762045668,\n", " 0.5999298697988361,\n", " 0.949091371766772,\n", " 0.7596847142709389,\n", " 0.9069969915878169,\n", " 0.6968682670915987,\n", " 1.08794036721835,\n", " 0.5602884508935422,\n", " 1.318869940756357,\n", " 1.1942236293948434,\n", " 0.5640227223503532,\n", " 0.8446377970051452,\n", " 1.628816098478645,\n", " 1.8305446882417173,\n", " 0.5397826714227629,\n", " 0.2038801182214214,\n", " 1.1948227809410754,\n", " 0.5764283735378148,\n", " 0.8229294357992282,\n", " 1.128709639363429,\n", " 0.40635267719696544,\n", " 0.39926592307636,\n", " 0.2903580420800136,\n", " 0.45816330834432695,\n", " 1.0674614075946602,\n", " 0.10952559684237645,\n", " 0.7269371031343099,\n", " 1.301768649128452,\n", " 1.3226843118768015,\n", " 0.986988006646031,\n", " 1.0605693914906622,\n", " 0.9914718386837788,\n", " 1.542859369094011,\n", " 0.5067410387257277,\n", " 1.403760845815931,\n", " 0.5719811313206238,\n", " 1.1145484740422242,\n", " 0.5645185200204728,\n", " 0.40321747018433063,\n", " 0.8229427602668038,\n", " 0.6847479919922137,\n", " 1.1866541674616335,\n", " 0.7957210332674904,\n", " 1.2528624682292628,\n", " 0.5548334221513603,\n", " 0.5452367267779412,\n", " 0.9402345989584331,\n", " 1.2708666528288315,\n", " 1.5565533726919814,\n", " 0.4962648727272815,\n", " 0.319727300852879,\n", " 1.1808460591225556,\n", " 0.7007252134789234,\n", " 0.3798326517784353,\n", " 1.8050548642420952,\n", " 0.2469552651791574,\n", " 0.9702153703977903,\n", " 0.28813303665130774,\n", " 1.1193942106666115,\n", " 1.128728334228454,\n", " 1.159023037088421,\n", " 0.9849253285855322,\n", " 0.3386520988405909,\n", " 1.4961814288485555,\n", " 0.35501342919263995,\n", " 1.4355748237655557,\n", " 0.7532542272252798,\n", " 0.7085146870931094,\n", " 1.0192893164273298,\n", " 0.8689224985353499,\n", " 0.34831490394864484,\n", " 0.8718489874777116,\n", " 0.47045206824954056,\n", " 1.1980778771218217,\n", " 0.7605450262559575,\n", " 0.29594533922971883,\n", " 0.9340219343247932,\n", " 0.661598009707532,\n", " 0.38480699173616717,\n", " 1.8048385062805767,\n", " 0.8003941720229975,\n", " 1.038962246361346,\n", " 0.8429968407818051,\n", " 1.064948729069189,\n", " 0.915566020452375,\n", " 0.9987120682932847,\n", " 0.77414097597943,\n", " 0.27047113314545057,\n", " 0.2896378198922808,\n", " 0.7698819977030014,\n", " 0.5019140435635764,\n", " 0.6791366700188544,\n", " 1.2948452846925842,\n", " 0.5444764305058631,\n", " 1.5591703345743442,\n", " 1.1832605501760076,\n", " 0.2652123390645027,\n", " 0.49208175311511376,\n", " 1.3412191865856258,\n", " 0.7042459602798293,\n", " 0.805224515644744,\n", " 0.7330614166584132,\n", " 0.6030255350352144,\n", " 0.5718259954151268,\n", " 0.7826544058002686,\n", " 1.1733797070034444,\n", " 1.3477145153176373,\n", " 1.2519308176483304,\n", " 1.0711135632346398,\n", " 0.8789891112663222,\n", " 0.6169646284767818,\n", " 1.0413652601341408,\n", " 1.1015804447127975,\n", " 0.954191370985066,\n", " 0.7487381428989828,\n", " 1.1746932010104207,\n", " 1.3640562461140289,\n", " 0.9216898624064113,\n", " 0.7935808801007029,\n", " 1.15309617019029,\n", " 0.553594972820007,\n", " 0.31304198828297214,\n", " 0.7084278752890136,\n", " 0.8052503921717946,\n", " 0.855150748969265,\n", " 1.2372343291263679,\n", " 1.0152882099252094,\n", " 0.9028142238173458,\n", " 0.744541120373777,\n", " 0.8476616625528294,\n", " 0.8231116432561099,\n", " 0.5610891208718534,\n", " 0.9661408642011338,\n", " 0.9979063645709263,\n", " 0.5004924781785397,\n", " 1.8495826881240498,\n", " 0.6808935423340096,\n", " 0.6455614936341498,\n", " 0.7095252841145618,\n", " 0.8749441337377186,\n", " 1.532158053309003,\n", " 0.80669381744149,\n", " 0.7405235637627042,\n", " 0.6169323465135274,\n", " 0.9735986711499199,\n", " 0.7537072773930769,\n", " 0.8365480916687608,\n", " 1.4576030691565427,\n", " 0.5788474400129731,\n", " 1.390925159331307,\n", " 0.5838277627135302,\n", " 0.5350022981871508,\n", " 1.156573104005251,\n", " 1.0567346391247276,\n", " 0.5870810508865146,\n", " 0.43210566044732746,\n", " 0.801921864993982,\n", " 1.1464761510916912,\n", " 0.9984600885762906,\n", " 0.7452727368403107,\n", " 2.102813628102601,\n", " 0.9786437895466296,\n", " 0.5131051835608773,\n", " 1.1383075524814086,\n", " 0.14572544875409096,\n", " 1.6160815614088668,\n", " 1.009920540824996,\n", " 0.8878556134378509,\n", " 0.5145382941842422,\n", " 1.3511234647375119,\n", " 0.7033303983384637,\n", " 0.21802862705910808,\n", " 0.5349840326346063,\n", " 0.17464314473015602,\n", " 0.7459687091635648,\n", " 1.895341304845769,\n", " 1.0480928145263197,\n", " 1.2850591812417518,\n", " 0.7363613886283286,\n", " 1.25140397966191,\n", " 0.3550016251976787,\n", " 0.5111966045261802,\n", " 0.9989810853282535,\n", " 0.47338505360465655,\n", " 0.4516175982136239,\n", " 1.3131004728252653,\n", " 0.7854992494374201,\n", " 1.2121306569297183,\n", " 0.7653664615642678,\n", " 0.9765684414249006,\n", " 0.12076299483940595,\n", " 1.081387127586169,\n", " 1.2196394624395106,\n", " 1.2019927728426172,\n", " 0.7752753928813061,\n", " 1.1539937756806853,\n", " 0.9125347091633038,\n", " 0.20008547719699477,\n", " 0.9484491527145831,\n", " 0.9099707732015141,\n", " 1.8033328985866715,\n", " 1.0178360725848552,\n", " 0.37854031611010797,\n", " 0.503769810769849,\n", " 0.660545328572385,\n", " 0.5740634709503042,\n", " 0.4777111817718773,\n", " 0.7164280589816596,\n", " 0.5717226601374039,\n", " 0.9852946471122429,\n", " 1.3747107640688634,\n", " 1.5303247159922002,\n", " 0.6268664425260766,\n", " 0.8882774305708779,\n", " 0.22250313243565492,\n", " 1.3610046366415323,\n", " 0.21891848702010488,\n", " 0.6706051762262402,\n", " 1.8069220248100575,\n", " 0.7232108031003731,\n", " 0.9420743291036489,\n", " 0.906917825123143,\n", " 1.2294552670525931,\n", " 1.8407798167871534,\n", " 0.9788273072273879,\n", " 1.567515747076488,\n", " 0.8667175522388199,\n", " 1.0778102513639916,\n", " 0.3091684855033065,\n", " 0.15278166356875209,\n", " 0.1418982795757111,\n", " 0.789644802473155,\n", " 1.086668896835035,\n", " 0.5388356740913118,\n", " 0.5101479237679829,\n", " 0.45164585218678344,\n", " 0.35623279579599554,\n", " 0.9372257065736564,\n", " 0.5318994092709293,\n", " 0.42108206912652313,\n", " 1.179971929003269,\n", " 1.0641498712234525,\n", " 0.9384394250000392,\n", " 0.5723992569434887,\n", " 0.49967970300773223,\n", " 0.690726985472558,\n", " 0.772710388722214,\n", " 1.190111551415162,\n", " 0.7736741154278138,\n", " 0.616573893863299,\n", " 0.7662615889256419,\n", " 0.6409817396597041,\n", " 0.620044358854281,\n", " 0.9462158288452747,\n", " 1.450133746243643,\n", " 1.2540531664621688,\n", " 1.777851631456451,\n", " 0.16152554120423057,\n", " 0.1183186908906511,\n", " 0.8945922884173109,\n", " 0.905361210466364,\n", " 0.9836868922080133,\n", " 0.7753559969114303,\n", " 1.1759369552483045,\n", " 0.9943852417543747,\n", " 0.899848985433058,\n", " 0.4337047816201854,\n", " 1.065340615410451,\n", " 1.1436025607210258,\n", " 1.6702709150684496,\n", " 0.09848098357011448,\n", " 0.6743773771141737,\n", " 0.34705588734931997,\n", " 1.4060585532257583,\n", " 0.49516933432928745,\n", " 0.4764935438216805,\n", " 1.0400873368697745,\n", " 0.4998184517023705,\n", " 1.2777218223283118,\n", " 1.240105182261665,\n", " 0.9262725214599266,\n", " 0.7252812630389229,\n", " 0.6542906916726872,\n", " 0.29773291641704014,\n", " 0.15635667669190753,\n", " 0.3714854298145183,\n", " 0.7232009280106035,\n", " 0.5362714186143404,\n", " 0.7772602692306457,\n", " 0.8989952350473284,\n", " 1.2943774389824831,\n", " 1.4670831608220152,\n", " 0.5205713532874017,\n", " 1.1521112633552155,\n", " 1.1306466661507903,\n", " 1.174568216541176,\n", " 0.18818476704682255,\n", " 1.0770295445497315,\n", " 0.2537965494919969,\n", " 0.9378520382780632,\n", " 0.26672455330460504,\n", " 0.9637326138098476,\n", " 1.4009087605179296,\n", " 1.3459177516780756,\n", " 0.8467154866745363,\n", " 0.8002669568089489,\n", " 1.0561531537791904,\n", " 1.2431974309406384,\n", " 0.7253348940090747,\n", " 0.7887007021163226,\n", " 1.039493448314954,\n", " 0.93333178279731,\n", " 0.35687089968647157,\n", " 1.5478782757441076,\n", " 0.8988386466600634,\n", " 1.0713430916277082,\n", " 0.2785013859006148,\n", " 1.855403382729778,\n", " 0.3888624516488747,\n", " 1.064816507153381,\n", " 1.0028605560708708,\n", " 0.9722930410484564,\n", " 0.9019719957581758,\n", " 0.22487420403669772,\n", " 0.4379940952308813,\n", " 1.4262337330577686,\n", " 0.36383972957910504,\n", " 0.5051458575830701,\n", " 0.3125119341396454,\n", " 0.5925010630113882,\n", " 0.6630436494530125,\n", " 1.1176103726769284,\n", " 1.2209885197010355,\n", " 1.6893258567826592,\n", " 0.27956910072485486,\n", " 0.3995023315711258,\n", " 0.9882009640613458,\n", " 0.5689652234026041,\n", " 0.5568834656982572,\n", " 0.4336060899275824,\n", " 1.6720314459716084,\n", " 0.337679384009573,\n", " 0.37718924118482977,\n", " 2.046957874657971,\n", " 0.9618782795664873,\n", " 1.0977476642045747,\n", " 1.2595367566434783,\n", " 0.5025424315667328,\n", " 1.6580777931268222,\n", " 0.6273114281240786,\n", " 0.33472094488248905,\n", " 0.42618049873797537,\n", " 1.1413704192282093,\n", " 0.6846853966512952,\n", " 0.46299072654758433,\n", " 0.2807753560767385,\n", " 2.0877169209689375,\n", " 0.5699436538872727,\n", " 0.5877797701294935,\n", " 0.15215523134067988,\n", " 1.279581322476349,\n", " 0.33631605048029234,\n", " 1.2268266959143086,\n", " 1.0276675203669248,\n", " 0.3487379263116471,\n", " 1.4843901659474525,\n", " 0.8406113852881005,\n", " 0.9777194864609822,\n", " 0.1042057516783503,\n", " 1.2318591503078482,\n", " 0.8405840421928067,\n", " 1.346750829358689,\n", " 0.3200488174428591,\n", " 1.1049242388553435,\n", " 1.3590169430435843,\n", " 0.4239922598795696,\n", " 1.5726446283640065,\n", " 1.1323093919808038,\n", " 0.897983431683577,\n", " 1.9962406094193428,\n", " 0.5924342725128217,\n", " 0.9576002779424317,\n", " 0.6684264742105107,\n", " 0.5528252723310005,\n", " 0.5382995396254147,\n", " 0.7631833676095042,\n", " 0.4211078117728002,\n", " 0.6703813563837411,\n", " 1.3717953537978391,\n", " 1.2005543324730328,\n", " 1.573183220084872,\n", " 1.233446723304709,\n", " 0.7732717445799646,\n", " 0.9866781329630302,\n", " 0.32121090160191185,\n", " 0.8100959510337902,\n", " 0.9761273642451671,\n", " 0.999734046144008,\n", " 1.1650469944957467,\n", " 1.2917439846122905,\n", " 0.8934322405964787,\n", " 0.17832335103069485,\n", " 1.393816648706831,\n", " 0.7022873601845998,\n", " 0.9607783005969345,\n", " 1.1020641651149055,\n", " 1.0065683770405847,\n", " 1.4871978532981314,\n", " 1.8095949937006646,\n", " 0.9048713291656485,\n", " 0.4737646677733209,\n", " 1.1574708389385908,\n", " 2.139889192516429,\n", " 1.3298723047208805,\n", " 1.2696871838119195,\n", " 1.4090881822860877,\n", " 1.1679323956679868,\n", " 1.244828229159326,\n", " 0.9497516402418111,\n", " 0.2505834971055517,\n", " 2.121732246153287,\n", " 0.8202283020313887,\n", " 1.0255778008529428,\n", " 1.010661351774345,\n", " 0.7383521052679322,\n", " 0.8089012362886762,\n", " 0.5905037489233432,\n", " 2.486672789932468,\n", " 0.6147781324462719,\n", " 1.3977331303445193,\n", " 0.9943142606134864,\n", " 1.3782947919562807,\n", " 0.25814213381763873,\n", " 1.0736586382437023,\n", " 0.548099833584943,\n", " 1.0794232654813896,\n", " 0.9751321949059206,\n", " 0.936034754975454,\n", " 0.7320282583788607,\n", " 0.7237660257326191,\n", " 1.246300084552653,\n", " 1.0160475234343895,\n", " 1.1150101329249769,\n", " 1.2926723502330344,\n", " 1.0998213061673658,\n", " 0.5623172076258305,\n", " 0.36107739785721304,\n", " 0.2085874475384764,\n", " 0.5912375852139083,\n", " 0.9840105987732455,\n", " 0.9385446345198978,\n", " 1.0521738205909894,\n", " 0.42158724176794704,\n", " 1.1000140464500532,\n", " 2.2816031084396413,\n", " 0.6205644255939033,\n", " 1.3907209992996237,\n", " 1.8425295376355044,\n", " 1.2359921021420326,\n", " 0.4129805407611019,\n", " 0.79988470750831,\n", " 0.3560426392962519,\n", " 0.2961488388747476,\n", " 0.4132872632324937,\n", " 0.526310517089293,\n", " 0.20864230811367301,\n", " 0.46322739377914274,\n", " 0.2743575051701145,\n", " 0.9596320500309562,\n", " 1.111231479221313,\n", " 0.4061778729816386,\n", " 0.7381142817324079,\n", " 1.3183427614363263,\n", " 1.4011769139582406,\n", " 1.2146415518371756,\n", " 0.6624342755748202,\n", " 0.8677595454342142,\n", " 1.5811396243705682,\n", " 0.4258531911030621,\n", " 0.7785767697415314,\n", " 0.9591661277521435,\n", " 1.392119329588245,\n", " 0.8239240983307415,\n", " 0.8561937319813331,\n", " 1.0306856402118596,\n", " 1.2323453193805578,\n", " 0.5375989643296872,\n", " 0.8628449338732025,\n", " 0.6787534027277152,\n", " 2.1952383422093398,\n", " 0.7397176253948312,\n", " 1.0524122687220432,\n", " 0.8545866241252821,\n", " 0.9296547293762196,\n", " 0.6125112382021238,\n", " 1.2097020104218013,\n", " 0.3754154537228658,\n", " 0.30131973533855244,\n", " 0.7966452375570099,\n", " 0.4578795858234844,\n", " 0.9189970382353367,\n", " 0.6413953410955415,\n", " 0.6043041335077219,\n", " 1.2988091727784936,\n", " 0.37907929179038446,\n", " 1.010182995154716,\n", " 1.2256624318892815,\n", " 1.4147322613158966,\n", " 0.6439485024958078,\n", " 0.5343360088444444,\n", " 0.34123577300073626,\n", " 0.2617166949470708,\n", " 1.2629467986853382,\n", " 0.29946670261424535,\n", " 1.6446113862648768,\n", " 1.1819121325680544,\n", " 1.035288406576167,\n", " 0.9571445916410437,\n", " 0.8542553661286103,\n", " 0.8069328767147556,\n", " 0.9161695381217849,\n", " 0.13336921282371955,\n", " 0.6936476264082934,\n", " 1.4888578692795256,\n", " 1.3676613990476003,\n", " 1.0777169855855468,\n", " 0.8435294321405653,\n", " 0.627977335904467,\n", " 0.9861122319691616,\n", " 0.8234688271610795,\n", " 0.6331453375915456,\n", " 0.813707732013291,\n", " 0.4200852349370837,\n", " 0.36744136467619676,\n", " 1.3189314419695204,\n", " 1.0322247116945984,\n", " 0.5893592323262073,\n", " 0.6533172838167381,\n", " 0.5431183113039427,\n", " 1.0403355578819438,\n", " 1.353110529507906,\n", " 0.16242352046533295,\n", " 1.3247608886958768,\n", " 1.0984881804944058,\n", " 0.39146351629237797,\n", " 0.6656864018053072,\n", " 1.0125944564716605,\n", " 0.4817063824986401,\n", " 0.2939965765544257,\n", " 1.228920890301069,\n", " 0.33685243720721286,\n", " 2.2305831708321735,\n", " 0.24514596617736026,\n", " 1.0516950039809918,\n", " 0.5948941941849184,\n", " 1.113434090392755,\n", " 0.7727853551893835,\n", " 1.110286883329884,\n", " 0.8572490692509105,\n", " 0.30677167099035163,\n", " 0.5342357770304985,\n", " 0.2519693305470141,\n", " 0.6361276624549436,\n", " 2.383034526790942,\n", " 0.8296226894458326,\n", " 1.3671790087672722,\n", " 0.9889263830957379,\n", " 2.0838762395312327,\n", " 0.49128582458067854,\n", " 1.4885063534967458,\n", " 0.8387876375239411,\n", " 1.337464670750941,\n", " 0.23324541478018015,\n", " 1.0908715690840394,\n", " 0.5356520146288715,\n", " 1.7279394397955952,\n", " 0.7725719996530885,\n", " 1.4695693767418219,\n", " 0.3290932704051277,\n", " 1.6252427375924754,\n", " 0.944720806454131,\n", " 1.1048551831154805,\n", " 0.573244114124031,\n", " 0.5494705192717846,\n", " 0.7905075640467226,\n", " 0.7296601534112779,\n", " 0.44785491348888584,\n", " 1.3441038553176048,\n", " 0.45229770293370586,\n", " 0.4940013935079068,\n", " 1.0259804083364505,\n", " 0.9456621466128908,\n", " 0.38443511886552906,\n", " 0.7066956969042407,\n", " 0.6153545091659798,\n", " 0.9608715325603604,\n", " 0.5719054708738252,\n", " 0.48689661038036613,\n", " 0.7440681147122569,\n", " 1.0803014784082383,\n", " 0.5729997445456658,\n", " 0.9304921019685728,\n", " 1.1699676152753813,\n", " 1.3067580358023498,\n", " 0.8394280596881111,\n", " 0.6481692881716759,\n", " 0.5646250016506795,\n", " 0.9507880730638296,\n", " 1.360986878100282,\n", " 1.1177507151827926,\n", " 0.34603935911242445,\n", " 0.9013878140329307,\n", " 0.6724156059441628,\n", " 0.9861875509763702,\n", " 1.2668369915760809,\n", " 1.1161913066478004,\n", " 1.1038071702618275,\n", " 0.8556735139784692,\n", " 0.7169198860975009,\n", " 0.7291887041761115,\n", " 0.8681634877066084,\n", " 0.6908686625315008,\n", " 0.2761198384277245,\n", " 0.4949272366212889,\n", " 0.4548355853164741,\n", " 0.7444517771118175,\n", " 0.2704785346096862,\n", " 2.11861170096717,\n", " 0.1189393056163208,\n", " 2.1303720349057538,\n", " 0.7309327440103799,\n", " 0.44092309914925343,\n", " 0.6882637377668996,\n", " 0.5857027655944479,\n", " 0.7694420756616328,\n", " 1.057839800279406,\n", " 1.7299996646347593,\n", " 0.7077900006521487,\n", " 0.5776782481254122,\n", " 0.6527815619606968,\n", " 1.143079707182396,\n", " 1.0278254316821673,\n", " 0.46402465587614994,\n", " 0.5581664752336761,\n", " 0.6766067476229688,\n", " 1.2765787868287632,\n", " 0.7909046048526291,\n", " 1.1518112913105782,\n", " 1.69547065179313,\n", " 1.1822013216750238,\n", " 0.6715908350685785,\n", " 1.1789742077925418,\n", " 0.8023177869687925,\n", " 1.2642526386548676,\n", " 0.3478533496528826,\n", " 0.845745954139068,\n", " 0.9518240166157714,\n", " 0.28670084227556547,\n", " 0.2847563289330126,\n", " 0.3591873212640102,\n", " 1.2563623373603332,\n", " 0.902739960713536,\n", " 0.5934893680952124,\n", " 1.2267194709331637,\n", " 1.4156426316371935,\n", " 1.2167390719244213,\n", " 1.0189556843437013,\n", " 1.1495917496538275,\n", " 0.5882695457973734,\n", " 1.4742447014134037,\n", " 0.3408176053344164,\n", " 0.8535599154495362,\n", " 1.0891884831478804,\n", " 0.33728955036398334,\n", " 1.073360033717988,\n", " 1.0922786564864162,\n", " 0.7931033191503458,\n", " 0.4687353765888784,\n", " 0.9931545017673705,\n", " 0.6101961770432061,\n", " 0.34778737252796676,\n", " 0.5333985065035015,\n", " 0.5598399471700327,\n", " 0.9890741238375687,\n", " 1.4693318685100003,\n", " 0.7595267619568856,\n", " 1.2452243809939703,\n", " 1.217689424638536,\n", " 0.933100355731109,\n", " 0.20719956544744683,\n", " 1.449435417433834,\n", " 1.6503341893292889,\n", " 1.3900549469352406,\n", " 1.193757638971678,\n", " 0.3074626975441836,\n", " 0.3815870877256507,\n", " 1.4931800917028537,\n", " 0.8057440983433997,\n", " 1.4834920303049266,\n", " 1.0156126466355102,\n", " 0.7436913569097032,\n", " 1.0886736716741252,\n", " 1.3862956275825933,\n", " 0.6958621936706216,\n", " 0.7201840271468425,\n", " 0.7879547158962201,\n", " 0.8263389345534042,\n", " 0.6251228171257636,\n", " 0.58972022178135,\n", " 1.6819659126643964,\n", " 1.1370325252311368,\n", " 0.9021874766473055,\n", " 1.0410787882671986,\n", " 0.14887813128442864,\n", " 0.4007303142833667,\n", " 0.3799836449274668,\n", " 0.4805683519775007,\n", " 1.0666794108847992,\n", " 0.6575414721302073,\n", " 0.6663484295400806,\n", " 0.776683839792147,\n", " 1.9436119798012104,\n", " 0.6348594926283799,\n", " 0.8300082567683418,\n", " 0.8059811937448595,\n", " 0.19323270904170248,\n", " 0.3864158330959764,\n", " 0.7545492130645071,\n", " 0.7935300262689303,\n", " 0.9583571299295088,\n", " 0.634442929138201,\n", " 0.16228554814364524,\n", " 1.2409750494000538,\n", " 0.9935778757825826,\n", " 0.615677078774362,\n", " 0.3759235290844234,\n", " 0.38862301963383994,\n", " 0.580119171616921,\n", " 0.9951970406804007,\n", " 0.7383086846581857,\n", " 0.8446599954814173,\n", " 1.4615427215791794,\n", " 0.3989386594717214,\n", " 1.3634315465157978,\n", " 0.5228231352802809,\n", " 0.5400031204620637,\n", " 0.39348307120369974,\n", " 1.2678433297309215,\n", " 0.5775075226851931,\n", " 0.48712951888916645,\n", " 1.0916587100644746,\n", " 1.455749428061633,\n", " 0.7326485595898442,\n", " 1.2188361008515751,\n", " 1.0190509619725918,\n", " 0.6740749463919509,\n", " 0.4857512294848795,\n", " 0.49599603114315255,\n", " 1.0166025964415268,\n", " 2.0081780261822213,\n", " 0.527288737156651,\n", " 0.5595980727400894,\n", " 0.684123100857019,\n", " 0.7555775931197091,\n", " 0.6021836289907917,\n", " 1.5665002632348386,\n", " 0.611119328962632,\n", " 1.0329990330541152,\n", " 0.883171022392395,\n", " 1.10054832113936,\n", " 0.901629459066864,\n", " 1.093463440756112,\n", " 0.8783015247755795,\n", " 0.3640749107805493,\n", " 1.0317910896831104,\n", " 1.2212220733354133,\n", " 0.9052642083139395,\n", " 0.8523209328079678,\n", " 0.5353057965515964,\n", " 0.4707310763775271,\n", " 0.6074228508211685,\n", " 1.2456202118960173,\n", " 0.6006059687454242,\n", " 0.6953476595831664,\n", " 0.7562372286370855,\n", " 1.229822436552197,\n", " 0.7973872222936789,\n", " 0.9654181631837031,\n", " 0.9169998764954006,\n", " 0.943333274279581,\n", " 1.0855764147532974,\n", " 0.24079982615596546,\n", " 0.8851161661908336,\n", " 1.1190790224015514,\n", " 0.24763813033786441,\n", " 0.5416180713180433,\n", " 1.578176794780314,\n", " 0.9011555892868179,\n", " 0.7460502760614895,\n", " 2.5242101437567768,\n", " 0.5439373645659541,\n", " 0.5705821374311932,\n", " 0.42548555055529647,\n", " 0.9820452684841446,\n", " 0.8666981047833231,\n", " 1.9019370849637411,\n", " 1.2730627296965669,\n", " 0.32399786463464525,\n", " 1.8463653840942489,\n", " 1.2736993853378389,\n", " 1.035291348607166,\n", " 0.5168634627263856,\n", " 0.8099411743945166,\n", " 0.3959672859055459,\n", " 0.44113843552852927,\n", " ...]"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["agg_error"]}, {"cell_type": "code", "execution_count": 166, "id": "385439cd", "metadata": {}, "outputs": [], "source": ["x, y = get_batch(D<PERSON><PERSON>, 1)\n", "x_t = torch.tensor(x).float().to(device)\n", "out = net(x_t)\n", "y = y.flatten()\n", "y_out = out[0].cpu().detach().numpy()\n", "ee_net = DFbot.fkine(x[0][0:5]+y_out)\n", "ee_in = DFbot.fkine(x[0][0:5]+y)\n", "err_i = ee_net - ee_in"]}, {"cell_type": "code", "execution_count": 167, "id": "41c67644", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-0.06728251, -0.00607499, -0.02287672,  0.17535152, -0.11657135])"]}, "execution_count": 167, "metadata": {}, "output_type": "execute_result"}], "source": ["y"]}, {"cell_type": "code", "execution_count": 168, "id": "f6afc341", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-0.05170549,  0.00199278, -0.02380542,  0.17323269, -0.09618129],\n", "      dtype=float32)"]}, "execution_count": 168, "metadata": {}, "output_type": "execute_result"}], "source": ["y_out"]}, {"cell_type": "code", "execution_count": 66, "id": "b5345891", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 0.6437  \u001b[0m \u001b[38;5;1m-0.03228 \u001b[0m \u001b[38;5;1m-0.7646  \u001b[0m \u001b[38;5;4m-0.1333  \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.4891  \u001b[0m \u001b[38;5;1m 0.7857  \u001b[0m \u001b[38;5;1m 0.3786  \u001b[0m \u001b[38;5;4m 0.06599 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.5885  \u001b[0m \u001b[38;5;1m-0.6177  \u001b[0m \u001b[38;5;1m 0.5216  \u001b[0m \u001b[38;5;4m-0.05149 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["ee_net"]}, {"cell_type": "code", "execution_count": 67, "id": "683b7067", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 0.6387  \u001b[0m \u001b[38;5;1m-0.04687 \u001b[0m \u001b[38;5;1m-0.7681  \u001b[0m \u001b[38;5;4m-0.134   \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.5041  \u001b[0m \u001b[38;5;1m 0.7796  \u001b[0m \u001b[38;5;1m 0.3716  \u001b[0m \u001b[38;5;4m 0.06483 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.5814  \u001b[0m \u001b[38;5;1m-0.6245  \u001b[0m \u001b[38;5;1m 0.5216  \u001b[0m \u001b[38;5;4m-0.05054 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["ee_in"]}, {"cell_type": "code", "execution_count": 69, "id": "697f8a6f", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 5.04837940e-03,  1.45913880e-02,  3.47052684e-03,  7.41830824e-04,\n", "       -1.49332429e-02,  6.08943933e-03,  7.06414626e-03,  1.16497210e-03,\n", "        7.14395834e-03,  6.74408064e-03,  1.86604634e-05, -9.43047145e-04,\n", "        0.00000000e+00,  0.00000000e+00,  0.00000000e+00,  0.00000000e+00])"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["(ee_net-ee_in).flatten()"]}, {"cell_type": "code", "execution_count": 70, "id": "16f355ea", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0.01369076, -0.03357922,  0.01247583, -0.00010718, -0.04164334,\n", "        0.05724144, -0.05307813, -0.00801802,  0.02318034,  0.06687793,\n", "        0.06282113,  0.00843126])"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["x[0][5:]"]}, {"cell_type": "code", "execution_count": 129, "id": "7f2bb20f", "metadata": {"scrolled": false}, "outputs": [{"data": {"application/javascript": "/* Put everything inside the global mpl namespace */\n/* global mpl */\nwindow.mpl = {};\n\nmpl.get_websocket_type = function () {\n    if (typeof WebSocket !== 'undefined') {\n        return WebSocket;\n    } else if (typeof MozWebSocket !== 'undefined') {\n        return MozWebSocket;\n    } else {\n        alert(\n            'Your browser does not have WebSocket support. ' +\n                'Please try Chrome, Safari or Firefox ≥ 6. ' +\n                'Firefox 4 and 5 are also supported but you ' +\n                'have to enable WebSockets in about:config.'\n        );\n    }\n};\n\nmpl.figure = function (figure_id, websocket, ondownload, parent_element) {\n    this.id = figure_id;\n\n    this.ws = websocket;\n\n    this.supports_binary = this.ws.binaryType !== undefined;\n\n    if (!this.supports_binary) {\n        var warnings = document.getElementById('mpl-warnings');\n        if (warnings) {\n            warnings.style.display = 'block';\n            warnings.textContent =\n                'This browser does not support binary websocket messages. ' +\n                'Performance may be slow.';\n        }\n    }\n\n    this.imageObj = new Image();\n\n    this.context = undefined;\n    this.message = undefined;\n    this.canvas = undefined;\n    this.rubberband_canvas = undefined;\n    this.rubberband_context = undefined;\n    this.format_dropdown = undefined;\n\n    this.image_mode = 'full';\n\n    this.root = document.createElement('div');\n    this.root.setAttribute('style', 'display: inline-block');\n    this._root_extra_style(this.root);\n\n    parent_element.appendChild(this.root);\n\n    this._init_header(this);\n    this._init_canvas(this);\n    this._init_toolbar(this);\n\n    var fig = this;\n\n    this.waiting = false;\n\n    this.ws.onopen = function () {\n        fig.send_message('supports_binary', { value: fig.supports_binary });\n        fig.send_message('send_image_mode', {});\n        if (fig.ratio !== 1) {\n            fig.send_message('set_device_pixel_ratio', {\n                device_pixel_ratio: fig.ratio,\n            });\n        }\n        fig.send_message('refresh', {});\n    };\n\n    this.imageObj.onload = function () {\n        if (fig.image_mode === 'full') {\n            // Full images could contain transparency (where diff images\n            // almost always do), so we need to clear the canvas so that\n            // there is no ghosting.\n            fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n        }\n        fig.context.drawImage(fig.imageObj, 0, 0);\n    };\n\n    this.imageObj.onunload = function () {\n        fig.ws.close();\n    };\n\n    this.ws.onmessage = this._make_on_message_function(this);\n\n    this.ondownload = ondownload;\n};\n\nmpl.figure.prototype._init_header = function () {\n    var titlebar = document.createElement('div');\n    titlebar.classList =\n        'ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix';\n    var titletext = document.createElement('div');\n    titletext.classList = 'ui-dialog-title';\n    titletext.setAttribute(\n        'style',\n        'width: 100%; text-align: center; padding: 3px;'\n    );\n    titlebar.appendChild(titletext);\n    this.root.appendChild(titlebar);\n    this.header = titletext;\n};\n\nmpl.figure.prototype._canvas_extra_style = function (_canvas_div) {};\n\nmpl.figure.prototype._root_extra_style = function (_canvas_div) {};\n\nmpl.figure.prototype._init_canvas = function () {\n    var fig = this;\n\n    var canvas_div = (this.canvas_div = document.createElement('div'));\n    canvas_div.setAttribute('tabindex', '0');\n    canvas_div.setAttribute(\n        'style',\n        'border: 1px solid #ddd;' +\n            'box-sizing: content-box;' +\n            'clear: both;' +\n            'min-height: 1px;' +\n            'min-width: 1px;' +\n            'outline: 0;' +\n            'overflow: hidden;' +\n            'position: relative;' +\n            'resize: both;' +\n            'z-index: 2;'\n    );\n\n    function on_keyboard_event_closure(name) {\n        return function (event) {\n            return fig.key_event(event, name);\n        };\n    }\n\n    canvas_div.addEventListener(\n        'keydown',\n        on_keyboard_event_closure('key_press')\n    );\n    canvas_div.addEventListener(\n        'keyup',\n        on_keyboard_event_closure('key_release')\n    );\n\n    this._canvas_extra_style(canvas_div);\n    this.root.appendChild(canvas_div);\n\n    var canvas = (this.canvas = document.createElement('canvas'));\n    canvas.classList.add('mpl-canvas');\n    canvas.setAttribute(\n        'style',\n        'box-sizing: content-box;' +\n            'pointer-events: none;' +\n            'position: relative;' +\n            'z-index: 0;'\n    );\n\n    this.context = canvas.getContext('2d');\n\n    var backingStore =\n        this.context.backingStorePixelRatio ||\n        this.context.webkitBackingStorePixelRatio ||\n        this.context.mozBackingStorePixelRatio ||\n        this.context.msBackingStorePixelRatio ||\n        this.context.oBackingStorePixelRatio ||\n        this.context.backingStorePixelRatio ||\n        1;\n\n    this.ratio = (window.devicePixelRatio || 1) / backingStore;\n\n    var rubberband_canvas = (this.rubberband_canvas = document.createElement(\n        'canvas'\n    ));\n    rubberband_canvas.setAttribute(\n        'style',\n        'box-sizing: content-box;' +\n            'left: 0;' +\n            'pointer-events: none;' +\n            'position: absolute;' +\n            'top: 0;' +\n            'z-index: 1;'\n    );\n\n    // Apply a ponyfill if ResizeObserver is not implemented by browser.\n    if (this.ResizeObserver === undefined) {\n        if (window.ResizeObserver !== undefined) {\n            this.ResizeObserver = window.ResizeObserver;\n        } else {\n            var obs = _JSXTOOLS_RESIZE_OBSERVER({});\n            this.ResizeObserver = obs.ResizeObserver;\n        }\n    }\n\n    this.resizeObserverInstance = new this.ResizeObserver(function (entries) {\n        var nentries = entries.length;\n        for (var i = 0; i < nentries; i++) {\n            var entry = entries[i];\n            var width, height;\n            if (entry.contentBoxSize) {\n                if (entry.contentBoxSize instanceof Array) {\n                    // Chrome 84 implements new version of spec.\n                    width = entry.contentBoxSize[0].inlineSize;\n                    height = entry.contentBoxSize[0].blockSize;\n                } else {\n                    // Firefox implements old version of spec.\n                    width = entry.contentBoxSize.inlineSize;\n                    height = entry.contentBoxSize.blockSize;\n                }\n            } else {\n                // Chrome <84 implements even older version of spec.\n                width = entry.contentRect.width;\n                height = entry.contentRect.height;\n            }\n\n            // Keep the size of the canvas and rubber band canvas in sync with\n            // the canvas container.\n            if (entry.devicePixelContentBoxSize) {\n                // Chrome 84 implements new version of spec.\n                canvas.setAttribute(\n                    'width',\n                    entry.devicePixelContentBoxSize[0].inlineSize\n                );\n                canvas.setAttribute(\n                    'height',\n                    entry.devicePixelContentBoxSize[0].blockSize\n                );\n            } else {\n                canvas.setAttribute('width', width * fig.ratio);\n                canvas.setAttribute('height', height * fig.ratio);\n            }\n            /* This rescales the canvas back to display pixels, so that it\n             * appears correct on HiDPI screens. */\n            canvas.style.width = width + 'px';\n            canvas.style.height = height + 'px';\n\n            rubberband_canvas.setAttribute('width', width);\n            rubberband_canvas.setAttribute('height', height);\n\n            // And update the size in Python. We ignore the initial 0/0 size\n            // that occurs as the element is placed into the DOM, which should\n            // otherwise not happen due to the minimum size styling.\n            if (fig.ws.readyState == 1 && width != 0 && height != 0) {\n                fig.request_resize(width, height);\n            }\n        }\n    });\n    this.resizeObserverInstance.observe(canvas_div);\n\n    function on_mouse_event_closure(name) {\n        /* User Agent sniffing is bad, but WebKit is busted:\n         * https://bugs.webkit.org/show_bug.cgi?id=144526\n         * https://bugs.webkit.org/show_bug.cgi?id=181818\n         * The worst that happens here is that they get an extra browser\n         * selection when dragging, if this check fails to catch them.\n         */\n        var UA = navigator.userAgent;\n        var isWebKit = /AppleWebKit/.test(UA) && !/Chrome/.test(UA);\n        if(isWebKit) {\n            return function (event) {\n                /* This prevents the web browser from automatically changing to\n                 * the text insertion cursor when the button is pressed. We\n                 * want to control all of the cursor setting manually through\n                 * the 'cursor' event from matplotlib */\n                event.preventDefault()\n                return fig.mouse_event(event, name);\n            };\n        } else {\n            return function (event) {\n                return fig.mouse_event(event, name);\n            };\n        }\n    }\n\n    canvas_div.addEventListener(\n        'mousedown',\n        on_mouse_event_closure('button_press')\n    );\n    canvas_div.addEventListener(\n        'mouseup',\n        on_mouse_event_closure('button_release')\n    );\n    canvas_div.addEventListener(\n        'dblclick',\n        on_mouse_event_closure('dblclick')\n    );\n    // Throttle sequential mouse events to 1 every 20ms.\n    canvas_div.addEventListener(\n        'mousemove',\n        on_mouse_event_closure('motion_notify')\n    );\n\n    canvas_div.addEventListener(\n        'mouseenter',\n        on_mouse_event_closure('figure_enter')\n    );\n    canvas_div.addEventListener(\n        'mouseleave',\n        on_mouse_event_closure('figure_leave')\n    );\n\n    canvas_div.addEventListener('wheel', function (event) {\n        if (event.deltaY < 0) {\n            event.step = 1;\n        } else {\n            event.step = -1;\n        }\n        on_mouse_event_closure('scroll')(event);\n    });\n\n    canvas_div.appendChild(canvas);\n    canvas_div.appendChild(rubberband_canvas);\n\n    this.rubberband_context = rubberband_canvas.getContext('2d');\n    this.rubberband_context.strokeStyle = '#000000';\n\n    this._resize_canvas = function (width, height, forward) {\n        if (forward) {\n            canvas_div.style.width = width + 'px';\n            canvas_div.style.height = height + 'px';\n        }\n    };\n\n    // Disable right mouse context menu.\n    canvas_div.addEventListener('contextmenu', function (_e) {\n        event.preventDefault();\n        return false;\n    });\n\n    function set_focus() {\n        canvas.focus();\n        canvas_div.focus();\n    }\n\n    window.setTimeout(set_focus, 100);\n};\n\nmpl.figure.prototype._init_toolbar = function () {\n    var fig = this;\n\n    var toolbar = document.createElement('div');\n    toolbar.classList = 'mpl-toolbar';\n    this.root.appendChild(toolbar);\n\n    function on_click_closure(name) {\n        return function (_event) {\n            return fig.toolbar_button_onclick(name);\n        };\n    }\n\n    function on_mouseover_closure(tooltip) {\n        return function (event) {\n            if (!event.currentTarget.disabled) {\n                return fig.toolbar_button_onmouseover(tooltip);\n            }\n        };\n    }\n\n    fig.buttons = {};\n    var buttonGroup = document.createElement('div');\n    buttonGroup.classList = 'mpl-button-group';\n    for (var toolbar_ind in mpl.toolbar_items) {\n        var name = mpl.toolbar_items[toolbar_ind][0];\n        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n        var image = mpl.toolbar_items[toolbar_ind][2];\n        var method_name = mpl.toolbar_items[toolbar_ind][3];\n\n        if (!name) {\n            /* Instead of a spacer, we start a new button group. */\n            if (buttonGroup.hasChildNodes()) {\n                toolbar.appendChild(buttonGroup);\n            }\n            buttonGroup = document.createElement('div');\n            buttonGroup.classList = 'mpl-button-group';\n            continue;\n        }\n\n        var button = (fig.buttons[name] = document.createElement('button'));\n        button.classList = 'mpl-widget';\n        button.setAttribute('role', 'button');\n        button.setAttribute('aria-disabled', 'false');\n        button.addEventListener('click', on_click_closure(method_name));\n        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n\n        var icon_img = document.createElement('img');\n        icon_img.src = '_images/' + image + '.png';\n        icon_img.srcset = '_images/' + image + '_large.png 2x';\n        icon_img.alt = tooltip;\n        button.appendChild(icon_img);\n\n        buttonGroup.appendChild(button);\n    }\n\n    if (buttonGroup.hasChildNodes()) {\n        toolbar.appendChild(buttonGroup);\n    }\n\n    var fmt_picker = document.createElement('select');\n    fmt_picker.classList = 'mpl-widget';\n    toolbar.appendChild(fmt_picker);\n    this.format_dropdown = fmt_picker;\n\n    for (var ind in mpl.extensions) {\n        var fmt = mpl.extensions[ind];\n        var option = document.createElement('option');\n        option.selected = fmt === mpl.default_extension;\n        option.innerHTML = fmt;\n        fmt_picker.appendChild(option);\n    }\n\n    var status_bar = document.createElement('span');\n    status_bar.classList = 'mpl-message';\n    toolbar.appendChild(status_bar);\n    this.message = status_bar;\n};\n\nmpl.figure.prototype.request_resize = function (x_pixels, y_pixels) {\n    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n    // which will in turn request a refresh of the image.\n    this.send_message('resize', { width: x_pixels, height: y_pixels });\n};\n\nmpl.figure.prototype.send_message = function (type, properties) {\n    properties['type'] = type;\n    properties['figure_id'] = this.id;\n    this.ws.send(JSON.stringify(properties));\n};\n\nmpl.figure.prototype.send_draw_message = function () {\n    if (!this.waiting) {\n        this.waiting = true;\n        this.ws.send(JSON.stringify({ type: 'draw', figure_id: this.id }));\n    }\n};\n\nmpl.figure.prototype.handle_save = function (fig, _msg) {\n    var format_dropdown = fig.format_dropdown;\n    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n    fig.ondownload(fig, format);\n};\n\nmpl.figure.prototype.handle_resize = function (fig, msg) {\n    var size = msg['size'];\n    if (size[0] !== fig.canvas.width || size[1] !== fig.canvas.height) {\n        fig._resize_canvas(size[0], size[1], msg['forward']);\n        fig.send_message('refresh', {});\n    }\n};\n\nmpl.figure.prototype.handle_rubberband = function (fig, msg) {\n    var x0 = msg['x0'] / fig.ratio;\n    var y0 = (fig.canvas.height - msg['y0']) / fig.ratio;\n    var x1 = msg['x1'] / fig.ratio;\n    var y1 = (fig.canvas.height - msg['y1']) / fig.ratio;\n    x0 = Math.floor(x0) + 0.5;\n    y0 = Math.floor(y0) + 0.5;\n    x1 = Math.floor(x1) + 0.5;\n    y1 = Math.floor(y1) + 0.5;\n    var min_x = Math.min(x0, x1);\n    var min_y = Math.min(y0, y1);\n    var width = Math.abs(x1 - x0);\n    var height = Math.abs(y1 - y0);\n\n    fig.rubberband_context.clearRect(\n        0,\n        0,\n        fig.canvas.width / fig.ratio,\n        fig.canvas.height / fig.ratio\n    );\n\n    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n};\n\nmpl.figure.prototype.handle_figure_label = function (fig, msg) {\n    // Updates the figure title.\n    fig.header.textContent = msg['label'];\n};\n\nmpl.figure.prototype.handle_cursor = function (fig, msg) {\n    fig.canvas_div.style.cursor = msg['cursor'];\n};\n\nmpl.figure.prototype.handle_message = function (fig, msg) {\n    fig.message.textContent = msg['message'];\n};\n\nmpl.figure.prototype.handle_draw = function (fig, _msg) {\n    // Request the server to send over a new figure.\n    fig.send_draw_message();\n};\n\nmpl.figure.prototype.handle_image_mode = function (fig, msg) {\n    fig.image_mode = msg['mode'];\n};\n\nmpl.figure.prototype.handle_history_buttons = function (fig, msg) {\n    for (var key in msg) {\n        if (!(key in fig.buttons)) {\n            continue;\n        }\n        fig.buttons[key].disabled = !msg[key];\n        fig.buttons[key].setAttribute('aria-disabled', !msg[key]);\n    }\n};\n\nmpl.figure.prototype.handle_navigate_mode = function (fig, msg) {\n    if (msg['mode'] === 'PAN') {\n        fig.buttons['Pan'].classList.add('active');\n        fig.buttons['Zoom'].classList.remove('active');\n    } else if (msg['mode'] === 'ZOOM') {\n        fig.buttons['Pan'].classList.remove('active');\n        fig.buttons['Zoom'].classList.add('active');\n    } else {\n        fig.buttons['Pan'].classList.remove('active');\n        fig.buttons['Zoom'].classList.remove('active');\n    }\n};\n\nmpl.figure.prototype.updated_canvas_event = function () {\n    // Called whenever the canvas gets updated.\n    this.send_message('ack', {});\n};\n\n// A function to construct a web socket function for onmessage handling.\n// Called in the figure constructor.\nmpl.figure.prototype._make_on_message_function = function (fig) {\n    return function socket_on_message(evt) {\n        if (evt.data instanceof Blob) {\n            var img = evt.data;\n            if (img.type !== 'image/png') {\n                /* FIXME: We get \"Resource interpreted as Image but\n                 * transferred with MIME type text/plain:\" errors on\n                 * Chrome.  But how to set the MIME type?  It doesn't seem\n                 * to be part of the websocket stream */\n                img.type = 'image/png';\n            }\n\n            /* Free the memory for the previous frames */\n            if (fig.imageObj.src) {\n                (window.URL || window.webkitURL).revokeObjectURL(\n                    fig.imageObj.src\n                );\n            }\n\n            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n                img\n            );\n            fig.updated_canvas_event();\n            fig.waiting = false;\n            return;\n        } else if (\n            typeof evt.data === 'string' &&\n            evt.data.slice(0, 21) === 'data:image/png;base64'\n        ) {\n            fig.imageObj.src = evt.data;\n            fig.updated_canvas_event();\n            fig.waiting = false;\n            return;\n        }\n\n        var msg = JSON.parse(evt.data);\n        var msg_type = msg['type'];\n\n        // Call the  \"handle_{type}\" callback, which takes\n        // the figure and JSON message as its only arguments.\n        try {\n            var callback = fig['handle_' + msg_type];\n        } catch (e) {\n            console.log(\n                \"No handler for the '\" + msg_type + \"' message type: \",\n                msg\n            );\n            return;\n        }\n\n        if (callback) {\n            try {\n                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n                callback(fig, msg);\n            } catch (e) {\n                console.log(\n                    \"Exception inside the 'handler_\" + msg_type + \"' callback:\",\n                    e,\n                    e.stack,\n                    msg\n                );\n            }\n        }\n    };\n};\n\nfunction getModifiers(event) {\n    var mods = [];\n    if (event.ctrlKey) {\n        mods.push('ctrl');\n    }\n    if (event.altKey) {\n        mods.push('alt');\n    }\n    if (event.shiftKey) {\n        mods.push('shift');\n    }\n    if (event.metaKey) {\n        mods.push('meta');\n    }\n    return mods;\n}\n\n/*\n * return a copy of an object with only non-object keys\n * we need this to avoid circular references\n * https://stackoverflow.com/a/24161582/3208463\n */\nfunction simpleKeys(original) {\n    return Object.keys(original).reduce(function (obj, key) {\n        if (typeof original[key] !== 'object') {\n            obj[key] = original[key];\n        }\n        return obj;\n    }, {});\n}\n\nmpl.figure.prototype.mouse_event = function (event, name) {\n    if (name === 'button_press') {\n        this.canvas.focus();\n        this.canvas_div.focus();\n    }\n\n    // from https://stackoverflow.com/q/1114465\n    var boundingRect = this.canvas.getBoundingClientRect();\n    var x = (event.clientX - boundingRect.left) * this.ratio;\n    var y = (event.clientY - boundingRect.top) * this.ratio;\n\n    this.send_message(name, {\n        x: x,\n        y: y,\n        button: event.button,\n        step: event.step,\n        modifiers: getModifiers(event),\n        guiEvent: simpleKeys(event),\n    });\n\n    return false;\n};\n\nmpl.figure.prototype._key_event_extra = function (_event, _name) {\n    // Handle any extra behaviour associated with a key event\n};\n\nmpl.figure.prototype.key_event = function (event, name) {\n    // Prevent repeat events\n    if (name === 'key_press') {\n        if (event.key === this._key) {\n            return;\n        } else {\n            this._key = event.key;\n        }\n    }\n    if (name === 'key_release') {\n        this._key = null;\n    }\n\n    var value = '';\n    if (event.ctrlKey && event.key !== 'Control') {\n        value += 'ctrl+';\n    }\n    else if (event.altKey && event.key !== 'Alt') {\n        value += 'alt+';\n    }\n    else if (event.shiftKey && event.key !== 'Shift') {\n        value += 'shift+';\n    }\n\n    value += 'k' + event.key;\n\n    this._key_event_extra(event, name);\n\n    this.send_message(name, { key: value, guiEvent: simpleKeys(event) });\n    return false;\n};\n\nmpl.figure.prototype.toolbar_button_onclick = function (name) {\n    if (name === 'download') {\n        this.handle_save(this, null);\n    } else {\n        this.send_message('toolbar_button', { name: name });\n    }\n};\n\nmpl.figure.prototype.toolbar_button_onmouseover = function (tooltip) {\n    this.message.textContent = tooltip;\n};\n\n///////////////// REMAINING CONTENT GENERATED BY embed_js.py /////////////////\n// prettier-ignore\nvar _JSXTOOLS_RESIZE_OBSERVER=function(A){var t,i=new WeakMap,n=new WeakMap,a=new WeakMap,r=new WeakMap,o=new Set;function s(e){if(!(this instanceof s))throw new TypeError(\"Constructor requires 'new' operator\");i.set(this,e)}function h(){throw new TypeError(\"Function is not a constructor\")}function c(e,t,i,n){e=0 in arguments?Number(arguments[0]):0,t=1 in arguments?Number(arguments[1]):0,i=2 in arguments?Number(arguments[2]):0,n=3 in arguments?Number(arguments[3]):0,this.right=(this.x=this.left=e)+(this.width=i),this.bottom=(this.y=this.top=t)+(this.height=n),Object.freeze(this)}function d(){t=requestAnimationFrame(d);var s=new WeakMap,p=new Set;o.forEach((function(t){r.get(t).forEach((function(i){var r=t instanceof window.SVGElement,o=a.get(t),d=r?0:parseFloat(o.paddingTop),f=r?0:parseFloat(o.paddingRight),l=r?0:parseFloat(o.paddingBottom),u=r?0:parseFloat(o.paddingLeft),g=r?0:parseFloat(o.borderTopWidth),m=r?0:parseFloat(o.borderRightWidth),w=r?0:parseFloat(o.borderBottomWidth),b=u+f,F=d+l,v=(r?0:parseFloat(o.borderLeftWidth))+m,W=g+w,y=r?0:t.offsetHeight-W-t.clientHeight,E=r?0:t.offsetWidth-v-t.clientWidth,R=b+v,z=F+W,M=r?t.width:parseFloat(o.width)-R-E,O=r?t.height:parseFloat(o.height)-z-y;if(n.has(t)){var k=n.get(t);if(k[0]===M&&k[1]===O)return}n.set(t,[M,O]);var S=Object.create(h.prototype);S.target=t,S.contentRect=new c(u,d,M,O),s.has(i)||(s.set(i,[]),p.add(i)),s.get(i).push(S)}))})),p.forEach((function(e){i.get(e).call(e,s.get(e),e)}))}return s.prototype.observe=function(i){if(i instanceof window.Element){r.has(i)||(r.set(i,new Set),o.add(i),a.set(i,window.getComputedStyle(i)));var n=r.get(i);n.has(this)||n.add(this),cancelAnimationFrame(t),t=requestAnimationFrame(d)}},s.prototype.unobserve=function(i){if(i instanceof window.Element&&r.has(i)){var n=r.get(i);n.has(this)&&(n.delete(this),n.size||(r.delete(i),o.delete(i))),n.size||r.delete(i),o.size||cancelAnimationFrame(t)}},A.DOMRectReadOnly=c,A.ResizeObserver=s,A.ResizeObserverEntry=h,A}; // eslint-disable-line\nmpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Left button pans, Right button zooms\\nx/y fixes axis, CTRL fixes aspect\", \"fa fa-arrows\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\\nx/y fixes axis\", \"fa fa-square-o\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o\", \"download\"]];\n\nmpl.extensions = [\"eps\", \"jpeg\", \"pgf\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\", \"webp\"];\n\nmpl.default_extension = \"png\";/* global mpl */\n\nvar comm_websocket_adapter = function (comm) {\n    // Create a \"websocket\"-like object which calls the given IPython comm\n    // object with the appropriate methods. Currently this is a non binary\n    // socket, so there is still some room for performance tuning.\n    var ws = {};\n\n    ws.binaryType = comm.kernel.ws.binaryType;\n    ws.readyState = comm.kernel.ws.readyState;\n    function updateReadyState(_event) {\n        if (comm.kernel.ws) {\n            ws.readyState = comm.kernel.ws.readyState;\n        } else {\n            ws.readyState = 3; // Closed state.\n        }\n    }\n    comm.kernel.ws.addEventListener('open', updateReadyState);\n    comm.kernel.ws.addEventListener('close', updateReadyState);\n    comm.kernel.ws.addEventListener('error', updateReadyState);\n\n    ws.close = function () {\n        comm.close();\n    };\n    ws.send = function (m) {\n        //console.log('sending', m);\n        comm.send(m);\n    };\n    // Register the callback with on_msg.\n    comm.on_msg(function (msg) {\n        //console.log('receiving', msg['content']['data'], msg);\n        var data = msg['content']['data'];\n        if (data['blob'] !== undefined) {\n            data = {\n                data: new Blob(msg['buffers'], { type: data['blob'] }),\n            };\n        }\n        // Pass the mpl event to the overridden (by mpl) onmessage function.\n        ws.onmessage(data);\n    });\n    return ws;\n};\n\nmpl.mpl_figure_comm = function (comm, msg) {\n    // This is the function which gets called when the mpl process\n    // starts-up an IPython Comm through the \"matplotlib\" channel.\n\n    var id = msg.content.data.id;\n    // Get hold of the div created by the display call when the Comm\n    // socket was opened in Python.\n    var element = document.getElementById(id);\n    var ws_proxy = comm_websocket_adapter(comm);\n\n    function ondownload(figure, _format) {\n        window.open(figure.canvas.toDataURL());\n    }\n\n    var fig = new mpl.figure(id, ws_proxy, ondownload, element);\n\n    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n    // web socket which is closed, not our websocket->open comm proxy.\n    ws_proxy.onopen();\n\n    fig.parent_element = element;\n    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n    if (!fig.cell_info) {\n        console.error('Failed to find cell for figure', id, fig);\n        return;\n    }\n    fig.cell_info[0].output_area.element.on(\n        'cleared',\n        { fig: fig },\n        fig._remove_fig_handler\n    );\n};\n\nmpl.figure.prototype.handle_close = function (fig, msg) {\n    var width = fig.canvas.width / fig.ratio;\n    fig.cell_info[0].output_area.element.off(\n        'cleared',\n        fig._remove_fig_handler\n    );\n    fig.resizeObserverInstance.unobserve(fig.canvas_div);\n\n    // Update the output cell to use the data from the current canvas.\n    fig.push_to_output();\n    var dataURL = fig.canvas.toDataURL();\n    // Re-enable the keyboard manager in IPython - without this line, in FF,\n    // the notebook keyboard shortcuts fail.\n    IPython.keyboard_manager.enable();\n    fig.parent_element.innerHTML =\n        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n    fig.close_ws(fig, msg);\n};\n\nmpl.figure.prototype.close_ws = function (fig, msg) {\n    fig.send_message('closing', msg);\n    // fig.ws.close()\n};\n\nmpl.figure.prototype.push_to_output = function (_remove_interactive) {\n    // Turn the data on the canvas into data in the output cell.\n    var width = this.canvas.width / this.ratio;\n    var dataURL = this.canvas.toDataURL();\n    this.cell_info[1]['text/html'] =\n        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n};\n\nmpl.figure.prototype.updated_canvas_event = function () {\n    // Tell IPython that the notebook contents must change.\n    IPython.notebook.set_dirty(true);\n    this.send_message('ack', {});\n    var fig = this;\n    // Wait a second, then push the new image to the DOM so\n    // that it is saved nicely (might be nice to debounce this).\n    setTimeout(function () {\n        fig.push_to_output();\n    }, 1000);\n};\n\nmpl.figure.prototype._init_toolbar = function () {\n    var fig = this;\n\n    var toolbar = document.createElement('div');\n    toolbar.classList = 'btn-toolbar';\n    this.root.appendChild(toolbar);\n\n    function on_click_closure(name) {\n        return function (_event) {\n            return fig.toolbar_button_onclick(name);\n        };\n    }\n\n    function on_mouseover_closure(tooltip) {\n        return function (event) {\n            if (!event.currentTarget.disabled) {\n                return fig.toolbar_button_onmouseover(tooltip);\n            }\n        };\n    }\n\n    fig.buttons = {};\n    var buttonGroup = document.createElement('div');\n    buttonGroup.classList = 'btn-group';\n    var button;\n    for (var toolbar_ind in mpl.toolbar_items) {\n        var name = mpl.toolbar_items[toolbar_ind][0];\n        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n        var image = mpl.toolbar_items[toolbar_ind][2];\n        var method_name = mpl.toolbar_items[toolbar_ind][3];\n\n        if (!name) {\n            /* Instead of a spacer, we start a new button group. */\n            if (buttonGroup.hasChildNodes()) {\n                toolbar.appendChild(buttonGroup);\n            }\n            buttonGroup = document.createElement('div');\n            buttonGroup.classList = 'btn-group';\n            continue;\n        }\n\n        button = fig.buttons[name] = document.createElement('button');\n        button.classList = 'btn btn-default';\n        button.href = '#';\n        button.title = name;\n        button.innerHTML = '<i class=\"fa ' + image + ' fa-lg\"></i>';\n        button.addEventListener('click', on_click_closure(method_name));\n        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n        buttonGroup.appendChild(button);\n    }\n\n    if (buttonGroup.hasChildNodes()) {\n        toolbar.appendChild(buttonGroup);\n    }\n\n    // Add the status bar.\n    var status_bar = document.createElement('span');\n    status_bar.classList = 'mpl-message pull-right';\n    toolbar.appendChild(status_bar);\n    this.message = status_bar;\n\n    // Add the close button to the window.\n    var buttongrp = document.createElement('div');\n    buttongrp.classList = 'btn-group inline pull-right';\n    button = document.createElement('button');\n    button.classList = 'btn btn-mini btn-primary';\n    button.href = '#';\n    button.title = 'Stop Interaction';\n    button.innerHTML = '<i class=\"fa fa-power-off icon-remove icon-large\"></i>';\n    button.addEventListener('click', function (_evt) {\n        fig.handle_close(fig, {});\n    });\n    button.addEventListener(\n        'mouseover',\n        on_mouseover_closure('Stop Interaction')\n    );\n    buttongrp.appendChild(button);\n    var titlebar = this.root.querySelector('.ui-dialog-titlebar');\n    titlebar.insertBefore(buttongrp, titlebar.firstChild);\n};\n\nmpl.figure.prototype._remove_fig_handler = function (event) {\n    var fig = event.data.fig;\n    if (event.target !== this) {\n        // Ignore bubbled events from children.\n        return;\n    }\n    fig.close_ws(fig, {});\n};\n\nmpl.figure.prototype._root_extra_style = function (el) {\n    el.style.boxSizing = 'content-box'; // override notebook setting of border-box.\n};\n\nmpl.figure.prototype._canvas_extra_style = function (el) {\n    // this is important to make the div 'focusable\n    el.setAttribute('tabindex', 0);\n    // reach out to IPython and tell the keyboard manager to turn it's self\n    // off when our div gets focus\n\n    // location in version 3\n    if (IPython.notebook.keyboard_manager) {\n        IPython.notebook.keyboard_manager.register_events(el);\n    } else {\n        // location in version 2\n        IPython.keyboard_manager.register_events(el);\n    }\n};\n\nmpl.figure.prototype._key_event_extra = function (event, _name) {\n    // Check for shift+enter\n    if (event.shiftKey && event.which === 13) {\n        this.canvas_div.blur();\n        // select the cell after this one\n        var index = IPython.notebook.find_cell_index(this.cell_info[0]);\n        IPython.notebook.select(index + 1);\n    }\n};\n\nmpl.figure.prototype.handle_save = function (fig, _msg) {\n    fig.ondownload(fig, null);\n};\n\nmpl.find_output_cell = function (html_output) {\n    // Return the cell and output element which can be found *uniquely* in the notebook.\n    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n    // IPython event is triggered only after the cells have been serialised, which for\n    // our purposes (turning an active figure into a static one), is too late.\n    var cells = IPython.notebook.get_cells();\n    var ncells = cells.length;\n    for (var i = 0; i < ncells; i++) {\n        var cell = cells[i];\n        if (cell.cell_type === 'code') {\n            for (var j = 0; j < cell.output_area.outputs.length; j++) {\n                var data = cell.output_area.outputs[j];\n                if (data.data) {\n                    // IPython >= 3 moved mimebundle to data attribute of output\n                    data = data.data;\n                }\n                if (data['text/html'] === html_output) {\n                    return [cell, data, j];\n                }\n            }\n        }\n    }\n};\n\n// Register the function which deals with the matplotlib target/channel.\n// The kernel may be null if the page has been refreshed.\nif (IPython.notebook.kernel !== null) {\n    IPython.notebook.kernel.comm_manager.register_target(\n        'matplotlib',\n        mpl.mpl_figure_comm\n    );\n}\n", "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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\" width=\"640\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["PyPlot3D backend, t = 0.05, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 129, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.plot(x[0][0:5]+y, block=False)"]}, {"cell_type": "code", "execution_count": 125, "id": "55cb5222", "metadata": {}, "outputs": [], "source": ["# Save the model\n", "weights_path = DFbot.name + \"_IK.weights\"\n", "torch.save(net.state_dict(), weights_path)"]}, {"cell_type": "code", "execution_count": 3, "id": "d2ef5c2e", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'DFbot' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[3], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m weights_path \u001b[38;5;241m=\u001b[39m DFbot\u001b[38;5;241m.\u001b[39mname \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_IK.weights\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28mprint\u001b[39m(weights_path)\n\u001b[1;32m      3\u001b[0m net\u001b[38;5;241m.\u001b[39mload_state_dict(torch\u001b[38;5;241m.\u001b[39mload(weights_path, map_location\u001b[38;5;241m=\u001b[39mdevice))\n", "\u001b[0;31mNameError\u001b[0m: name 'DFbot' is not defined"]}], "source": ["weights_path = DFbot.name + \"_IK.weights\"\n", "print(weights_path)\n", "net.load_state_dict(torch.load(weights_path, map_location=device))"]}, {"cell_type": "code", "execution_count": null, "id": "8627435c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "40b648f0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}