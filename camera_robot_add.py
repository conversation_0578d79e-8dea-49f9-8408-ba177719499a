import numpy as np
import math
from spatialmath import SE3
from spatialmath.base import e2h, h2e
from roboticstoolbox import *


#相机
DFbot = DHRobot(
    [
        RevoluteMDH(d=0.065, qlim=np.array([-np.pi*2/3, np.pi*2/3]),offset=np.pi/2),
        RevoluteMDH(alpha=np.pi / 2, qlim=np.array([-np.pi/2, np.pi/2]),offset=-np.pi/2),
        RevoluteMDH(a=-0.1, qlim=np.array([-np.pi*2/3, np.pi*2/3])),
        RevoluteMDH(a=-0.095, qlim=np.array([-np.pi/2, np.pi/2]),offset=np.pi/2),
        RevoluteMDH(alpha=-np.pi/2,d=0.04, qlim=np.array([-np.pi/2, np.pi/2])),
        RevoluteMDH(a=0.05, qlim=np.array([-np.pi, np.pi]))

    ],
    name="DFbot",
)


#夹爪
DFbot2 = DHRobot(
    [
        RevoluteMDH(d=0.065, qlim=np.array([-np.pi, np.pi])),
        RevoluteMDH(alpha=np.pi / 2, qlim=np.array([-np.pi, np.pi])),
        RevoluteMDH(a=-0.1, qlim=np.array([-np.pi, np.pi])),
        RevoluteMDH(a=-0.095, qlim=np.array([-np.pi, np.pi])),
        RevoluteMDH(alpha=-np.pi / 2, d=0.08, qlim=np.array([-np.pi, np.pi]))

    ],
    name="DFbot",
)

T1=DFbot.fkine([0,np.pi/3,0,np.pi/2,0,0])
DFbot.plot([0,np.pi/3,0,np.pi/2,0,0],block=True)
#相机标定的，内参矩阵
mtx=np.array([[641.43945303     ,    0.         , 307.9468488 ],
              [  0.             ,   663.28643594,231.53404289 ],
              [  0.             ,         0.    ,    1.      ]])
print(T1)
# 内参矩阵
K = mtx

# 位姿矩阵（简化为只有平移的情况）
pose = T1

# 外参矩阵（位姿矩阵的逆）
extrinsic = np.linalg.inv(pose)

# 3D点（世界坐标系）
point_3d = np.array([0, -0.28, 0, 1])

# 将点从世界坐标系转换到相机坐标系
point_camera = extrinsic @ point_3d

# 投影到图像平面
point_2d = K @ point_camera[:3]
depth = point_2d[2]
point_2d /= depth  # 归一化，最后一行化为1
DFbot.plot([0,0,0,0,0,0],block=True)
print("2D 图像点:")
print(point_2d[:2])