{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1deb9fe5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using matplotlib backend: <object object at 0x104d6b4d0>\n"]}], "source": ["%matplotlib auto"]}, {"cell_type": "code", "execution_count": 2, "id": "fe249934", "metadata": {}, "outputs": [], "source": ["import time\n", "import random"]}, {"cell_type": "code", "execution_count": 3, "id": "421ef77a", "metadata": {}, "outputs": [], "source": ["from roboticstoolbox import *\n", "from spatialmath import *\n", "from math import pi\n", "import numpy as np\n", "from matplotlib import pyplot as plt"]}, {"cell_type": "code", "execution_count": 4, "id": "a6ad2185", "metadata": {}, "outputs": [], "source": ["DFbot = DHRobot(\n", "    [\n", "                    RevoluteDH(d=0.04145,alpha=np.pi/2,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),\n", "                    RevoluteDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),\n", "                  \n", "    ],\n", "    name=\"DF<PERSON>\",\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "3a1e9d0f", "metadata": {}, "outputs": [{"data": {"text/plain": ["PyPlot3D backend, t = 0.05, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.plot([0,0,0])"]}, {"cell_type": "code", "execution_count": 6, "id": "b3b58c26", "metadata": {}, "outputs": [{"data": {"text/plain": ["PyPlot3D backend, t = 0.05, scene:\n", "  robot: Text(0.0, 0.0, 'DFbot')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.plot([np.pi/3,np.pi/3,-np.pi/3*2])"]}, {"cell_type": "code", "execution_count": 7, "id": "8c16b224", "metadata": {"notebookRunGroups": {"groupValue": "2"}}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 0.25    \u001b[0m \u001b[38;5;1m 0.433   \u001b[0m \u001b[38;5;1m 0.866   \u001b[0m \u001b[38;5;4m-0.04143 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.433   \u001b[0m \u001b[38;5;1m 0.75    \u001b[0m \u001b[38;5;1m-0.5     \u001b[0m \u001b[38;5;4m-0.07175 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m-0.866   \u001b[0m \u001b[38;5;1m 0.5     \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0.04145 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["DFbot.fkine([np.pi/3,np.pi/3,-np.pi/3*2])"]}, {"cell_type": "code", "execution_count": 8, "id": "1404e53e", "metadata": {}, "outputs": [], "source": ["#Q-learning"]}, {"cell_type": "code", "execution_count": null, "id": "e0ec9578", "metadata": {}, "outputs": [], "source": ["class Env():\n", "    def __init__(self, num_deg,target,robot,unit):\n", "        self.num_deg=num_deg           #关节数量\n", "        self.d = tuple([0]*self.num_deg) #初始化角度，根据初始化的关节数量，来设置几个角度初始化\n", "        self.target=target#可能是目标点\n", "        self.robot=robot#将运动学公式封装进去\n", "        self.unit=unit#单位转换因子\n", "        \n", "        \n", "    def step(self, action):\n", "        \"\"\"6个可执行action，3个自由度，每个自由度正转1个单位或反转1个单位\"\"\"\n", "        change = [[0, 1], [0, -1], [1, 1],[1, -1],[2, 1],[2, -1]]#/6个可执行action【0关节, 1正反转，或者说每一步，正走还是反转】\n", "        index=change[action][0] #获取控制关节\n", "        move=change[action][1]  #获取正反转，或者说每一步，正走还是反转\n", "        dtemp=list(self.d)      #转化为列表\n", "        dtemp[index] = dtemp[index] + move #存取度数\n", "        self.d=tuple(dtemp)               #转化为元组，存储回去\n", "        pose=self.robot.fkine([x*self.unit for x in self.d])#通过取度数转化为笛卡尔坐标\n", "\n", "        states = self.d\n", "        reward = -1 #每走一步，减少1分\n", "        terminal = False   #终极目标未达到\n", "        distance= sum([x*x for x in np.array(pose)[:,-1][0:3]-np.array(self.target)])#取矩阵的右上角的三个坐标，获取位置信息，并且求取与目标位置的距离\n", "        ##print(pose)\n", "        if distance<=0.005:#判断是否接近目标位置，\n", "            terminal = True\n", "            reward=100\n", "        check_boundary=[((self.d[i]*self.unit>=self.robot.qlim[0][i]) and (self.d[i]*self.unit<=self.robot.qlim[1][i])) for i in range(len(self.d)) ]            \n", "        #print(check_boundary)\n", "        if not (all(check_boundary)):#检查是否超出限制角度\n", "            reward = -1000\n", "            terminal = True\n", "            \n", "        return reward, states, terminal\n", "\n", "    def reset(self):\n", "        self.d = tuple([0]*self.num_deg)"]}, {"cell_type": "code", "execution_count": null, "id": "75e9450d", "metadata": {}, "outputs": [], "source": ["class Q_table():\n", "    def __init__(self, steps, actions, alpha, gamma,unit):\n", "        self.d=tuple([0]*3)\n", "        self.steps=5                 #每个关节允许走的最多步数\n", "        self.actions = actions       #可执行的动作数量3个关节*2个方向\n", "        self.table = dict() # initialize all Q(d,a) dictionary\n", "        self.alpha = alpha  #学习率\n", "        self.gamma = gamma  #折扣因子\n", "        self.unit=unit      #单位转换因子\n", "        \n", "    def _init_table(self):         #初始化q表\n", "        steps=self.steps           #将初始化步数赋值给steps\n", "        d0=np.mgrid[-steps:(steps+1):1]  #d1关节，生成-steps到steps的步数\n", "        d1=np.mgrid[-steps:(steps+1):1]  #d2关节生成-steps到steps的步数\n", "        d2=np.mgrid[-steps:(steps+1):1]  #生成-steps到steps的步数\n", "        for i1 in d0.flatten():          # 关节0的所有可能位置（flatten是为了确保d为一维数组）\n", "            for i2 in d1.flatten():      #关节1的所有可能位置\n", "                for i3 in d2.flatten():  #关节2的所有可能位置\n", "                    for i4 in range(self.actions):       #所有可能的动作\n", "                        self.table[tuple([i1,i2,i3,i4])]=0      #创建q表\n", "\n", "    def _epsilon(self,num_episode):\n", "        #return 0.1\n", "        # version for better convergence:\n", "        # \"\"\"At the beginning epsilon is 0.2, after 300 episodes decades to 0.05, and eventually go to 0.\"\"\"\n", "        return 20. / (num_episode + 100) #实现更好的回收函数，因为越接近与拟合的终点，收敛值越小\n", "\n", "    #在状态s下采用epsi-greedy方法选出一个概率action（有随机性）\n", "    def take_action(self, s, num_episode):\n", "        \"\"\"epsilon-greedy action selection\"\"\"\n", "        if random.random() < self._epsilon(1000): #生成一个随机数，如果小于这个值，则开始探索\n", "            return int(random.random() * 6)    #少部分时间去获取更好的解\n", "        else:\n", "            actions_value = [self.table[(s[0],s[1],s[2],a)] for a in range(self.actions)]\n", "            return actions_value.index(max(actions_value))              #不知道是啥\n", "    \n", "    #在状态s下方法选取价值最大的action（没有随机性）\n", "    def excute_policy(self,s):\n", "            actions_value = [self.table[(s[0],s[1],s[2],a)] for a in range(self.actions)]\n", "            return actions_value.index(max(actions_value))\n", "            \n", "    #在状态s下找到下一步最大的价值的数值\n", "    def max_q(self, s):\n", "        actions_value = [self.table[(s[0],s[1],s[2],a)] for a in range(self.actions)]\n", "        return max(actions_value)\n", "    \n", "    #更新Q-table\n", "    def update(self, a, s0, s1, r, is_terminated):\n", "        q_current = self.table[(s0[0],s0[1],s0[2],a)]\n", "        #如果没有终止，就更新\n", "        if not is_terminated:\n", "            q_target = r + self.gamma * self.max_q(s1) #gamma 0只关心即时奖励，1未来的奖励和即时的奖励同等重要，0。9重视未来，但有所折扣\n", "        #如果终止了，更新值为0\n", "        else:\n", "            q_target = r\n", "        self.table[(s0[0],s0[1],s0[2],a)] += self.alpha * (q_target - q_current) # alpha 0就是不学习 1就是完全相信新的经验 0.1平衡历史经验"]}, {"cell_type": "code", "execution_count": null, "id": "5467315e", "metadata": {}, "outputs": [], "source": ["env = Env(3,[-0.04,-0.07,0.04],<PERSON><PERSON>bot,np.pi/6)"]}, {"cell_type": "code", "execution_count": null, "id": "472d0445", "metadata": {}, "outputs": [], "source": ["table = Q_table(6, 6, 0.1,0.9,np.pi/6)"]}, {"cell_type": "code", "execution_count": 13, "id": "bee24ac0", "metadata": {}, "outputs": [], "source": ["table._init_table()"]}, {"cell_type": "code", "execution_count": 1, "id": "6a9f8189", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'table' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mtable\u001b[49m\u001b[38;5;241m.\u001b[39mtable\n", "\u001b[1;31mNameError\u001b[0m: name 'table' is not defined"]}], "source": ["table.table"]}, {"cell_type": "code", "execution_count": null, "id": "7bfa461b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Episode: 0, Score: -1004\n", "Episode: 1, Score: -1006\n", "Episode: 2, Score: -1004\n", "Episode: 3, Score: -1010\n", "Episode: 4, Score: -1006\n", "Episode: 5, Score: -1003\n", "Episode: 6, Score: -1009\n", "Episode: 7, Score: -1013\n", "Episode: 8, Score: -1010\n", "Episode: 9, Score: -1005\n", "Episode: 10, Score: -1029\n", "Episode: 11, Score: -1007\n", "Episode: 12, Score: -1006\n", "Episode: 13, Score: -1005\n", "Episode: 14, Score: -1007\n", "Episode: 15, Score: -1014\n", "Episode: 16, Score: -1009\n", "Episode: 17, Score: -1014\n", "Episode: 18, Score: -1008\n", "Episode: 19, Score: -1012\n", "Episode: 20, Score: -1009\n", "Episode: 21, Score: -1009\n", "Episode: 22, Score: -1011\n", "Episode: 23, Score: -1025\n", "Episode: 24, Score: 85\n", "Episode: 25, Score: -1030\n", "Episode: 26, Score: 87\n", "Episode: 27, Score: -1017\n", "Episode: 28, Score: 97\n", "Episode: 29, Score: 95\n", "Episode: 30, Score: -1038\n", "Episode: 31, Score: 91\n", "Episode: 32, Score: -1027\n", "Episode: 33, Score: 85\n", "Episode: 34, Score: 85\n", "Episode: 35, Score: 85\n", "Episode: 36, Score: 83\n", "Episode: 37, Score: 97\n", "Episode: 38, Score: 69\n", "Episode: 39, Score: -1014\n", "Episode: 40, Score: 95\n", "Episode: 41, Score: 93\n", "Episode: 42, Score: -1006\n", "Episode: 43, Score: 97\n", "Episode: 44, Score: 97\n", "Episode: 45, Score: 97\n", "Episode: 46, Score: 97\n", "Episode: 47, Score: 97\n", "Episode: 48, Score: 97\n", "Episode: 49, Score: 97\n", "Episode: 50, Score: 97\n", "Episode: 51, Score: 97\n", "Episode: 52, Score: 97\n", "Episode: 53, Score: 97\n", "Episode: 54, Score: 97\n", "Episode: 55, Score: 97\n", "Episode: 56, Score: 95\n", "Episode: 57, Score: -1012\n", "Episode: 58, Score: 95\n", "Episode: 59, Score: 93\n", "Episode: 60, Score: -1007\n", "Episode: 61, Score: 97\n", "Episode: 62, Score: 97\n", "Episode: 63, Score: -1029\n", "Episode: 64, Score: 97\n", "Episode: 65, Score: -1020\n", "Episode: 66, Score: 97\n", "Episode: 67, Score: 97\n", "Episode: 68, Score: 97\n", "Episode: 69, Score: -1029\n", "Episode: 70, Score: 97\n", "Episode: 71, Score: 97\n", "Episode: 72, Score: -1009\n", "Episode: 73, Score: 97\n", "Episode: 74, Score: 95\n", "Episode: 75, Score: 97\n", "Episode: 76, Score: 97\n", "Episode: 77, Score: 97\n", "Episode: 78, Score: 97\n", "Episode: 79, Score: 97\n", "Episode: 80, Score: 95\n", "Episode: 81, Score: 97\n", "Episode: 82, Score: 95\n", "Episode: 83, Score: 97\n", "Episode: 84, Score: 97\n", "Episode: 85, Score: 95\n", "Episode: 86, Score: 97\n", "Episode: 87, Score: 95\n", "Episode: 88, Score: 97\n", "Episode: 89, Score: 97\n", "Episode: 90, Score: 97\n", "Episode: 91, Score: 97\n", "Episode: 92, Score: 97\n", "Episode: 93, Score: 97\n", "Episode: 94, Score: 97\n", "Episode: 95, Score: 97\n", "Episode: 96, Score: 85\n", "Episode: 97, Score: 97\n", "Episode: 98, Score: 97\n", "Episode: 99, Score: 97\n", "Episode: 100, Score: 97\n", "Episode: 101, Score: 97\n", "Episode: 102, Score: 95\n", "Episode: 103, Score: 97\n", "Episode: 104, Score: 97\n", "Episode: 105, Score: 97\n", "Episode: 106, Score: 83\n", "Episode: 107, Score: 97\n", "Episode: 108, Score: 95\n", "Episode: 109, Score: -1012\n", "Episode: 110, Score: 97\n", "Episode: 111, Score: 97\n", "Episode: 112, Score: -1011\n", "Episode: 113, Score: 97\n", "Episode: 114, Score: 97\n", "Episode: 115, Score: 97\n", "Episode: 116, Score: 97\n", "Episode: 117, Score: 97\n", "Episode: 118, Score: 89\n", "Episode: 119, Score: 97\n", "Episode: 120, Score: 95\n", "Episode: 121, Score: 97\n", "Episode: 122, Score: 97\n", "Episode: 123, Score: -1009\n", "Episode: 124, Score: 97\n", "Episode: 125, Score: -1009\n", "Episode: 126, Score: 97\n", "Episode: 127, Score: 97\n", "Episode: 128, Score: 97\n", "Episode: 129, Score: 97\n", "Episode: 130, Score: 97\n", "Episode: 131, Score: -1016\n", "Episode: 132, Score: 97\n", "Episode: 133, Score: 97\n", "Episode: 134, Score: 97\n", "Episode: 135, Score: 97\n", "Episode: 136, Score: 97\n", "Episode: 137, Score: 97\n", "Episode: 138, Score: 97\n", "Episode: 139, Score: 93\n", "Episode: 140, Score: 97\n", "Episode: 141, Score: 95\n", "Episode: 142, Score: 97\n", "Episode: 143, Score: 95\n", "Episode: 144, Score: 97\n", "Episode: 145, Score: 97\n", "Episode: 146, Score: 97\n", "Episode: 147, Score: 97\n", "Episode: 148, Score: 97\n", "Episode: 149, Score: 97\n", "Episode: 150, Score: 97\n", "Episode: 151, Score: 95\n", "Episode: 152, Score: 97\n", "Episode: 153, Score: 97\n", "Episode: 154, Score: 97\n", "Episode: 155, Score: 97\n", "Episode: 156, Score: 97\n", "Episode: 157, Score: 97\n", "Episode: 158, Score: 97\n", "Episode: 159, Score: 97\n", "Episode: 160, Score: 97\n", "Episode: 161, Score: 97\n", "Episode: 162, Score: 97\n", "Episode: 163, Score: 97\n", "Episode: 164, Score: 97\n", "Episode: 165, Score: 97\n", "Episode: 166, Score: 97\n", "Episode: 167, Score: 95\n", "Episode: 168, Score: 97\n", "Episode: 169, Score: 97\n", "Episode: 170, Score: 97\n", "Episode: 171, Score: 97\n", "Episode: 172, Score: 91\n", "Episode: 173, Score: 97\n", "Episode: 174, Score: 93\n", "Episode: 175, Score: 97\n", "Episode: 176, Score: 97\n", "Episode: 177, Score: 97\n", "Episode: 178, Score: 93\n", "Episode: 179, Score: 97\n", "Episode: 180, Score: 97\n", "Episode: 181, Score: 97\n", "Episode: 182, Score: 95\n", "Episode: 183, Score: 95\n", "Episode: 184, Score: 97\n", "Episode: 185, Score: 97\n", "Episode: 186, Score: 97\n", "Episode: 187, Score: 97\n", "Episode: 188, Score: -1006\n", "Episode: 189, Score: 91\n", "Episode: 190, Score: 97\n", "Episode: 191, Score: 97\n", "Episode: 192, Score: 97\n", "Episode: 193, Score: 97\n", "Episode: 194, Score: 97\n", "Episode: 195, Score: 97\n", "Episode: 196, Score: -1023\n", "Episode: 197, Score: 97\n", "Episode: 198, Score: 97\n", "Episode: 199, Score: 97\n", "Episode: 200, Score: 97\n", "Episode: 201, Score: 97\n", "Episode: 202, Score: 97\n", "Episode: 203, Score: 97\n", "Episode: 204, Score: -1008\n", "Episode: 205, Score: 97\n", "Episode: 206, Score: 97\n", "Episode: 207, Score: 93\n", "Episode: 208, Score: 97\n", "Episode: 209, Score: 97\n", "Episode: 210, Score: 97\n", "Episode: 211, Score: 97\n", "Episode: 212, Score: 97\n", "Episode: 213, Score: 97\n", "Episode: 214, Score: 95\n", "Episode: 215, Score: 97\n", "Episode: 216, Score: 97\n", "Episode: 217, Score: 97\n", "Episode: 218, Score: 97\n", "Episode: 219, Score: 97\n", "Episode: 220, Score: 97\n", "Episode: 221, Score: 97\n", "Episode: 222, Score: 97\n", "Episode: 223, Score: 97\n", "Episode: 224, Score: 97\n", "Episode: 225, Score: 97\n", "Episode: 226, Score: 95\n", "Episode: 227, Score: 95\n", "Episode: 228, Score: 97\n", "Episode: 229, Score: 95\n", "Episode: 230, Score: 97\n", "Episode: 231, Score: 97\n", "Episode: 232, Score: 93\n", "Episode: 233, Score: 97\n", "Episode: 234, Score: 97\n", "Episode: 235, Score: 97\n", "Episode: 236, Score: 97\n", "Episode: 237, Score: 97\n", "Episode: 238, Score: 97\n", "Episode: 239, Score: 97\n", "Episode: 240, Score: 97\n", "Episode: 241, Score: 97\n", "Episode: 242, Score: 95\n", "Episode: 243, Score: 97\n", "Episode: 244, Score: 97\n", "Episode: 245, Score: 97\n", "Episode: 246, Score: 97\n", "Episode: 247, Score: 91\n", "Episode: 248, Score: 97\n", "Episode: 249, Score: 97\n", "Episode: 250, Score: 95\n", "Episode: 251, Score: 97\n", "Episode: 252, Score: 97\n", "Episode: 253, Score: 97\n", "Episode: 254, Score: 93\n", "Episode: 255, Score: 97\n", "Episode: 256, Score: 89\n", "Episode: 257, Score: 97\n", "Episode: 258, Score: 91\n", "Episode: 259, Score: 97\n", "Episode: 260, Score: 97\n", "Episode: 261, Score: 97\n", "Episode: 262, Score: 97\n", "Episode: 263, Score: -1015\n", "Episode: 264, Score: 97\n", "Episode: 265, Score: 97\n", "Episode: 266, Score: 97\n", "Episode: 267, Score: 97\n", "Episode: 268, Score: 97\n", "Episode: 269, Score: 97\n", "Episode: 270, Score: 97\n", "Episode: 271, Score: 97\n", "Episode: 272, Score: 97\n", "Episode: 273, Score: 97\n", "Episode: 274, Score: 97\n", "Episode: 275, Score: 97\n", "Episode: 276, Score: 97\n", "Episode: 277, Score: 97\n", "Episode: 278, Score: 91\n", "Episode: 279, Score: 97\n", "Episode: 280, Score: 95\n", "Episode: 281, Score: 93\n", "Episode: 282, Score: 97\n", "Episode: 283, Score: 95\n", "Episode: 284, Score: 97\n", "Episode: 285, Score: 97\n", "Episode: 286, Score: 97\n", "Episode: 287, Score: 97\n", "Episode: 288, Score: 97\n", "Episode: 289, Score: 97\n", "Episode: 290, Score: 97\n", "Episode: 291, Score: 97\n", "Episode: 292, Score: 97\n", "Episode: 293, Score: 97\n", "Episode: 294, Score: 93\n", "Episode: 295, Score: 97\n", "Episode: 296, Score: 97\n", "Episode: 297, Score: 97\n", "Episode: 298, Score: 97\n", "Episode: 299, Score: 97\n", "Episode: 300, Score: 97\n", "Episode: 301, Score: 97\n", "Episode: 302, Score: 97\n", "Episode: 303, Score: 97\n", "Episode: 304, Score: 95\n", "Episode: 305, Score: -1009\n", "Episode: 306, Score: 95\n", "Episode: 307, Score: 97\n", "Episode: 308, Score: 97\n", "Episode: 309, Score: 95\n", "Episode: 310, Score: 97\n", "Episode: 311, Score: 97\n", "Episode: 312, Score: 93\n", "Episode: 313, Score: 95\n", "Episode: 314, Score: 97\n", "Episode: 315, Score: 95\n", "Episode: 316, Score: 97\n", "Episode: 317, Score: 97\n", "Episode: 318, Score: 97\n", "Episode: 319, Score: 97\n", "Episode: 320, Score: 97\n", "Episode: 321, Score: 95\n", "Episode: 322, Score: 97\n", "Episode: 323, Score: 97\n", "Episode: 324, Score: 97\n", "Episode: 325, Score: 95\n", "Episode: 326, Score: 97\n", "Episode: 327, Score: 97\n", "Episode: 328, Score: 97\n", "Episode: 329, Score: 97\n", "Episode: 330, Score: 97\n", "Episode: 331, Score: 97\n", "Episode: 332, Score: 97\n", "Episode: 333, Score: 97\n", "Episode: 334, Score: 97\n", "Episode: 335, Score: 97\n", "Episode: 336, Score: 97\n", "Episode: 337, Score: 93\n", "Episode: 338, Score: 97\n", "Episode: 339, Score: 97\n", "Episode: 340, Score: 97\n", "Episode: 341, Score: 97\n", "Episode: 342, Score: 97\n", "Episode: 343, Score: 97\n", "Episode: 344, Score: 97\n", "Episode: 345, Score: 95\n", "Episode: 346, Score: 95\n", "Episode: 347, Score: 97\n", "Episode: 348, Score: 97\n", "Episode: 349, Score: 93\n", "Episode: 350, Score: 97\n", "Episode: 351, Score: 93\n", "Episode: 352, Score: 97\n", "Episode: 353, Score: 95\n", "Episode: 354, Score: 95\n", "Episode: 355, Score: 97\n", "Episode: 356, Score: 97\n", "Episode: 357, Score: 95\n", "Episode: 358, Score: 95\n", "Episode: 359, Score: 97\n", "Episode: 360, Score: 97\n", "Episode: 361, Score: 97\n", "Episode: 362, Score: 97\n", "Episode: 363, Score: 97\n", "Episode: 364, Score: 97\n", "Episode: 365, Score: 97\n", "Episode: 366, Score: 93\n", "Episode: 367, Score: 97\n", "Episode: 368, Score: 97\n", "Episode: 369, Score: 97\n", "Episode: 370, Score: 97\n", "Episode: 371, Score: 97\n", "Episode: 372, Score: 97\n", "Episode: 373, Score: 97\n", "Episode: 374, Score: -1008\n", "Episode: 375, Score: 95\n", "Episode: 376, Score: 97\n", "Episode: 377, Score: 93\n", "Episode: 378, Score: 97\n", "Episode: 379, Score: 97\n", "Episode: 380, Score: 97\n", "Episode: 381, Score: 97\n", "Episode: 382, Score: 97\n", "Episode: 383, Score: 97\n", "Episode: 384, Score: 97\n", "Episode: 385, Score: 97\n", "Episode: 386, Score: 97\n", "Episode: 387, Score: 97\n", "Episode: 388, Score: 97\n", "Episode: 389, Score: 97\n", "Episode: 390, Score: 97\n", "Episode: 391, Score: 97\n", "Episode: 392, Score: 97\n", "Episode: 393, Score: 95\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Episode: 394, Score: 97\n", "Episode: 395, Score: 97\n", "Episode: 396, Score: 97\n", "Episode: 397, Score: 97\n", "Episode: 398, Score: 97\n", "Episode: 399, Score: 97\n", "Episode: 400, Score: 97\n", "Episode: 401, Score: 95\n", "Episode: 402, Score: 97\n", "Episode: 403, Score: 97\n", "Episode: 404, Score: 97\n", "Episode: 405, Score: 97\n", "Episode: 406, Score: 95\n", "Episode: 407, Score: 93\n", "Episode: 408, Score: 97\n", "Episode: 409, Score: 97\n", "Episode: 410, Score: 95\n", "Episode: 411, Score: 97\n", "Episode: 412, Score: 97\n", "Episode: 413, Score: 97\n", "Episode: 414, Score: 97\n", "Episode: 415, Score: 97\n", "Episode: 416, Score: 97\n", "Episode: 417, Score: 97\n", "Episode: 418, Score: 95\n", "Episode: 419, Score: 97\n", "Episode: 420, Score: 95\n", "Episode: 421, Score: 95\n", "Episode: 422, Score: 97\n", "Episode: 423, Score: 95\n", "Episode: 424, Score: 95\n", "Episode: 425, Score: 97\n", "Episode: 426, Score: 97\n", "Episode: 427, Score: 95\n", "Episode: 428, Score: 97\n", "Episode: 429, Score: 97\n", "Episode: 430, Score: 97\n", "Episode: 431, Score: 97\n", "Episode: 432, Score: 95\n", "Episode: 433, Score: 95\n", "Episode: 434, Score: 95\n", "Episode: 435, Score: 97\n", "Episode: 436, Score: 97\n", "Episode: 437, Score: 97\n", "Episode: 438, Score: 97\n", "Episode: 439, Score: 97\n", "Episode: 440, Score: 97\n", "Episode: 441, Score: 97\n", "Episode: 442, Score: 97\n", "Episode: 443, Score: 97\n", "Episode: 444, Score: 97\n", "Episode: 445, Score: 97\n", "Episode: 446, Score: 95\n", "Episode: 447, Score: 97\n", "Episode: 448, Score: 95\n", "Episode: 449, Score: 97\n", "Episode: 450, Score: 97\n", "Episode: 451, Score: 97\n", "Episode: 452, Score: 95\n", "Episode: 453, Score: 97\n", "Episode: 454, Score: 97\n", "Episode: 455, Score: 97\n", "Episode: 456, Score: 97\n", "Episode: 457, Score: 95\n", "Episode: 458, Score: 97\n", "Episode: 459, Score: 95\n", "Episode: 460, Score: 97\n", "Episode: 461, Score: 97\n", "Episode: 462, Score: 97\n", "Episode: 463, Score: 97\n", "Episode: 464, Score: 97\n", "Episode: 465, Score: 97\n", "Episode: 466, Score: 97\n", "Episode: 467, Score: 95\n", "Episode: 468, Score: 97\n", "Episode: 469, Score: 97\n", "Episode: 470, Score: 97\n", "Episode: 471, Score: 97\n", "Episode: 472, Score: 97\n", "Episode: 473, Score: 97\n", "Episode: 474, Score: 97\n", "Episode: 475, Score: 97\n", "Episode: 476, Score: 97\n", "Episode: 477, Score: 95\n", "Episode: 478, Score: 97\n", "Episode: 479, Score: 97\n", "Episode: 480, Score: 97\n", "Episode: 481, Score: 95\n", "Episode: 482, Score: 97\n", "Episode: 483, Score: 97\n", "Episode: 484, Score: 97\n", "Episode: 485, Score: 97\n", "Episode: 486, Score: 97\n", "Episode: 487, Score: -1008\n", "Episode: 488, Score: -1004\n", "Episode: 489, Score: 97\n", "Episode: 490, Score: 97\n", "Episode: 491, Score: 95\n", "Episode: 492, Score: 97\n", "Episode: 493, Score: 97\n", "Episode: 494, Score: 97\n", "Episode: 495, Score: 97\n", "Episode: 496, Score: 97\n", "Episode: 497, Score: 97\n", "Episode: 498, Score: 97\n", "Episode: 499, Score: 95\n"]}], "source": ["for num_episode in range(500):\n", "        episodic_reward = 0\n", "        is_terminated = False\n", "        s0 = [0, 0,0]\n", "        while not is_terminated:\n", "            # within one episode\n", "            action = table.take_action(s0, num_episode)\n", "            r, s1, is_terminated = env.step(action)  #r代表奖励值，s1代表新状态，is_terminated代表是否终止\n", "            ##print(r, s1, action,is_terminated)\n", "            table.update(action, s0, s1, r, is_terminated)\n", "            episodic_reward += r\n", "            # env.render(frames=100)\n", "            s0 = s1\n", "        print(\"Episode: {}, Score: {}\".format(num_episode, episodic_reward))\n", "        env.reset()"]}, {"cell_type": "code", "execution_count": 17, "id": "987f6120", "metadata": {}, "outputs": [{"data": {"text/plain": ["{(-3, -3, -3, 0): 0,\n", " (-3, -3, -3, 1): 0,\n", " (-3, -3, -3, 2): 0,\n", " (-3, -3, -3, 3): 0,\n", " (-3, -3, -3, 4): 0,\n", " (-3, -3, -3, 5): 0,\n", " (-3, -3, -2, 0): 0,\n", " (-3, -3, -2, 1): 0,\n", " (-3, -3, -2, 2): 0,\n", " (-3, -3, -2, 3): 0,\n", " (-3, -3, -2, 4): 0,\n", " (-3, -3, -2, 5): 0,\n", " (-3, -3, -1, 0): 0,\n", " (-3, -3, -1, 1): 0,\n", " (-3, -3, -1, 2): 0,\n", " (-3, -3, -1, 3): 0,\n", " (-3, -3, -1, 4): 0,\n", " (-3, -3, -1, 5): 0,\n", " (-3, -3, 0, 0): -0.1,\n", " (-3, -3, 0, 1): 0,\n", " (-3, -3, 0, 2): 0,\n", " (-3, -3, 0, 3): 0,\n", " (-3, -3, 0, 4): 0,\n", " (-3, -3, 0, 5): 0,\n", " (-3, -3, 1, 0): 0,\n", " (-3, -3, 1, 1): 0,\n", " (-3, -3, 1, 2): 0,\n", " (-3, -3, 1, 3): 0,\n", " (-3, -3, 1, 4): 0,\n", " (-3, -3, 1, 5): 0,\n", " (-3, -3, 2, 0): 0,\n", " (-3, -3, 2, 1): 0,\n", " (-3, -3, 2, 2): 0,\n", " (-3, -3, 2, 3): 0,\n", " (-3, -3, 2, 4): 0,\n", " (-3, -3, 2, 5): 0,\n", " (-3, -3, 3, 0): 0,\n", " (-3, -3, 3, 1): 0,\n", " (-3, -3, 3, 2): 0,\n", " (-3, -3, 3, 3): 0,\n", " (-3, -3, 3, 4): 0,\n", " (-3, -3, 3, 5): 0,\n", " (-3, -2, -3, 0): 0,\n", " (-3, -2, -3, 1): 0,\n", " (-3, -2, -3, 2): 0,\n", " (-3, -2, -3, 3): 0,\n", " (-3, -2, -3, 4): 0,\n", " (-3, -2, -3, 5): 0,\n", " (-3, -2, -2, 0): 0,\n", " (-3, -2, -2, 1): 0,\n", " (-3, -2, -2, 2): 0,\n", " (-3, -2, -2, 3): 0,\n", " (-3, -2, -2, 4): 0,\n", " (-3, -2, -2, 5): 0,\n", " (-3, -2, -1, 0): 0,\n", " (-3, -2, -1, 1): 0,\n", " (-3, -2, -1, 2): 0,\n", " (-3, -2, -1, 3): 0,\n", " (-3, -2, -1, 4): 0,\n", " (-3, -2, -1, 5): 0,\n", " (-3, -2, 0, 0): -0.1,\n", " (-3, -2, 0, 1): 0,\n", " (-3, -2, 0, 2): 0,\n", " (-3, -2, 0, 3): 0,\n", " (-3, -2, 0, 4): 0,\n", " (-3, -2, 0, 5): 0,\n", " (-3, -2, 1, 0): -0.1,\n", " (-3, -2, 1, 1): 0,\n", " (-3, -2, 1, 2): 0,\n", " (-3, -2, 1, 3): 0,\n", " (-3, -2, 1, 4): 0,\n", " (-3, -2, 1, 5): 0,\n", " (-3, -2, 2, 0): -0.1,\n", " (-3, -2, 2, 1): 0,\n", " (-3, -2, 2, 2): 0,\n", " (-3, -2, 2, 3): 0,\n", " (-3, -2, 2, 4): 0,\n", " (-3, -2, 2, 5): 0,\n", " (-3, -2, 3, 0): -0.1,\n", " (-3, -2, 3, 1): 0,\n", " (-3, -2, 3, 2): 0,\n", " (-3, -2, 3, 3): 0,\n", " (-3, -2, 3, 4): 0,\n", " (-3, -2, 3, 5): 0,\n", " (-3, -1, -3, 0): 0,\n", " (-3, -1, -3, 1): 0,\n", " (-3, -1, -3, 2): 0,\n", " (-3, -1, -3, 3): 0,\n", " (-3, -1, -3, 4): 0,\n", " (-3, -1, -3, 5): 0,\n", " (-3, -1, -2, 0): 0,\n", " (-3, -1, -2, 1): 0,\n", " (-3, -1, -2, 2): 0,\n", " (-3, -1, -2, 3): 0,\n", " (-3, -1, -2, 4): 0,\n", " (-3, -1, -2, 5): 0,\n", " (-3, -1, -1, 0): -0.1,\n", " (-3, -1, -1, 1): 0,\n", " (-3, -1, -1, 2): 0,\n", " (-3, -1, -1, 3): 0,\n", " (-3, -1, -1, 4): 0,\n", " (-3, -1, -1, 5): 0,\n", " (-3, -1, 0, 0): -0.1,\n", " (-3, -1, 0, 1): -100.0,\n", " (-3, -1, 0, 2): 0,\n", " (-3, -1, 0, 3): 0,\n", " (-3, -1, 0, 4): 0,\n", " (-3, -1, 0, 5): 0,\n", " (-3, -1, 1, 0): -0.1,\n", " (-3, -1, 1, 1): 0,\n", " (-3, -1, 1, 2): 0,\n", " (-3, -1, 1, 3): 0,\n", " (-3, -1, 1, 4): 0,\n", " (-3, -1, 1, 5): 0,\n", " (-3, -1, 2, 0): -0.1,\n", " (-3, -1, 2, 1): 0,\n", " (-3, -1, 2, 2): 0,\n", " (-3, -1, 2, 3): 0,\n", " (-3, -1, 2, 4): 0,\n", " (-3, -1, 2, 5): 0,\n", " (-3, -1, 3, 0): 0,\n", " (-3, -1, 3, 1): 0,\n", " (-3, -1, 3, 2): 0,\n", " (-3, -1, 3, 3): 0,\n", " (-3, -1, 3, 4): 0,\n", " (-3, -1, 3, 5): 0,\n", " (-3, 0, -3, 0): 0,\n", " (-3, 0, -3, 1): 0,\n", " (-3, 0, -3, 2): 0,\n", " (-3, 0, -3, 3): 0,\n", " (-3, 0, -3, 4): 0,\n", " (-3, 0, -3, 5): 0,\n", " (-3, 0, -2, 0): 0,\n", " (-3, 0, -2, 1): 0,\n", " (-3, 0, -2, 2): 0,\n", " (-3, 0, -2, 3): 0,\n", " (-3, 0, -2, 4): 0,\n", " (-3, 0, -2, 5): 0,\n", " (-3, 0, -1, 0): -0.1,\n", " (-3, 0, -1, 1): 0,\n", " (-3, 0, -1, 2): 0,\n", " (-3, 0, -1, 3): 0,\n", " (-3, 0, -1, 4): 0,\n", " (-3, 0, -1, 5): 0,\n", " (-3, 0, 0, 0): -0.1,\n", " (-3, 0, 0, 1): -100.0,\n", " (-3, 0, 0, 2): 0,\n", " (-3, 0, 0, 3): 0,\n", " (-3, 0, 0, 4): 0,\n", " (-3, 0, 0, 5): 0,\n", " (-3, 0, 1, 0): -0.1,\n", " (-3, 0, 1, 1): 0,\n", " (-3, 0, 1, 2): 0,\n", " (-3, 0, 1, 3): 0,\n", " (-3, 0, 1, 4): 0,\n", " (-3, 0, 1, 5): 0,\n", " (-3, 0, 2, 0): -0.1,\n", " (-3, 0, 2, 1): 0,\n", " (-3, 0, 2, 2): 0,\n", " (-3, 0, 2, 3): 0,\n", " (-3, 0, 2, 4): 0,\n", " (-3, 0, 2, 5): 0,\n", " (-3, 0, 3, 0): -0.1,\n", " (-3, 0, 3, 1): 0,\n", " (-3, 0, 3, 2): 0,\n", " (-3, 0, 3, 3): 0,\n", " (-3, 0, 3, 4): 0,\n", " (-3, 0, 3, 5): 0,\n", " (-3, 1, -3, 0): 0,\n", " (-3, 1, -3, 1): 0,\n", " (-3, 1, -3, 2): 0,\n", " (-3, 1, -3, 3): 0,\n", " (-3, 1, -3, 4): 0,\n", " (-3, 1, -3, 5): 0,\n", " (-3, 1, -2, 0): 0,\n", " (-3, 1, -2, 1): 0,\n", " (-3, 1, -2, 2): 0,\n", " (-3, 1, -2, 3): 0,\n", " (-3, 1, -2, 4): 0,\n", " (-3, 1, -2, 5): 0,\n", " (-3, 1, -1, 0): -0.1,\n", " (-3, 1, -1, 1): 0,\n", " (-3, 1, -1, 2): 0,\n", " (-3, 1, -1, 3): 0,\n", " (-3, 1, -1, 4): 0,\n", " (-3, 1, -1, 5): 0,\n", " (-3, 1, 0, 0): -0.1,\n", " (-3, 1, 0, 1): 0,\n", " (-3, 1, 0, 2): 0,\n", " (-3, 1, 0, 3): 0,\n", " (-3, 1, 0, 4): 0,\n", " (-3, 1, 0, 5): 0,\n", " (-3, 1, 1, 0): -0.1,\n", " (-3, 1, 1, 1): 0,\n", " (-3, 1, 1, 2): 0,\n", " (-3, 1, 1, 3): 0,\n", " (-3, 1, 1, 4): 0,\n", " (-3, 1, 1, 5): 0,\n", " (-3, 1, 2, 0): -0.1,\n", " (-3, 1, 2, 1): 0,\n", " (-3, 1, 2, 2): 0,\n", " (-3, 1, 2, 3): 0,\n", " (-3, 1, 2, 4): 0,\n", " (-3, 1, 2, 5): 0,\n", " (-3, 1, 3, 0): -0.1,\n", " (-3, 1, 3, 1): 0,\n", " (-3, 1, 3, 2): 0,\n", " (-3, 1, 3, 3): 0,\n", " (-3, 1, 3, 4): 0,\n", " (-3, 1, 3, 5): 0,\n", " (-3, 2, -3, 0): 0,\n", " (-3, 2, -3, 1): 0,\n", " (-3, 2, -3, 2): 0,\n", " (-3, 2, -3, 3): 0,\n", " (-3, 2, -3, 4): 0,\n", " (-3, 2, -3, 5): 0,\n", " (-3, 2, -2, 0): 0,\n", " (-3, 2, -2, 1): 0,\n", " (-3, 2, -2, 2): 0,\n", " (-3, 2, -2, 3): 0,\n", " (-3, 2, -2, 4): 0,\n", " (-3, 2, -2, 5): 0,\n", " (-3, 2, -1, 0): -0.1,\n", " (-3, 2, -1, 1): 0,\n", " (-3, 2, -1, 2): 0,\n", " (-3, 2, -1, 3): 0,\n", " (-3, 2, -1, 4): 0,\n", " (-3, 2, -1, 5): 0,\n", " (-3, 2, 0, 0): -0.1,\n", " (-3, 2, 0, 1): 0,\n", " (-3, 2, 0, 2): 0,\n", " (-3, 2, 0, 3): 0,\n", " (-3, 2, 0, 4): 0,\n", " (-3, 2, 0, 5): 0,\n", " (-3, 2, 1, 0): -0.1,\n", " (-3, 2, 1, 1): 0,\n", " (-3, 2, 1, 2): 0,\n", " (-3, 2, 1, 3): 0,\n", " (-3, 2, 1, 4): 0,\n", " (-3, 2, 1, 5): 0,\n", " (-3, 2, 2, 0): 0,\n", " (-3, 2, 2, 1): 0,\n", " (-3, 2, 2, 2): 0,\n", " (-3, 2, 2, 3): 0,\n", " (-3, 2, 2, 4): 0,\n", " (-3, 2, 2, 5): 0,\n", " (-3, 2, 3, 0): -0.1,\n", " (-3, 2, 3, 1): 0,\n", " (-3, 2, 3, 2): 0,\n", " (-3, 2, 3, 3): 0,\n", " (-3, 2, 3, 4): 0,\n", " (-3, 2, 3, 5): 0,\n", " (-3, 3, -3, 0): 0,\n", " (-3, 3, -3, 1): 0,\n", " (-3, 3, -3, 2): 0,\n", " (-3, 3, -3, 3): 0,\n", " (-3, 3, -3, 4): 0,\n", " (-3, 3, -3, 5): 0,\n", " (-3, 3, -2, 0): 0,\n", " (-3, 3, -2, 1): 0,\n", " (-3, 3, -2, 2): 0,\n", " (-3, 3, -2, 3): 0,\n", " (-3, 3, -2, 4): 0,\n", " (-3, 3, -2, 5): 0,\n", " (-3, 3, -1, 0): -0.1,\n", " (-3, 3, -1, 1): 0,\n", " (-3, 3, -1, 2): 0,\n", " (-3, 3, -1, 3): 0,\n", " (-3, 3, -1, 4): 0,\n", " (-3, 3, -1, 5): 0,\n", " (-3, 3, 0, 0): -0.1,\n", " (-3, 3, 0, 1): 0,\n", " (-3, 3, 0, 2): 0,\n", " (-3, 3, 0, 3): 0,\n", " (-3, 3, 0, 4): 0,\n", " (-3, 3, 0, 5): 0,\n", " (-3, 3, 1, 0): 0,\n", " (-3, 3, 1, 1): 0,\n", " (-3, 3, 1, 2): 0,\n", " (-3, 3, 1, 3): 0,\n", " (-3, 3, 1, 4): 0,\n", " (-3, 3, 1, 5): 0,\n", " (-3, 3, 2, 0): 0,\n", " (-3, 3, 2, 1): 0,\n", " (-3, 3, 2, 2): 0,\n", " (-3, 3, 2, 3): 0,\n", " (-3, 3, 2, 4): 0,\n", " (-3, 3, 2, 5): 0,\n", " (-3, 3, 3, 0): 0,\n", " (-3, 3, 3, 1): 0,\n", " (-3, 3, 3, 2): 0,\n", " (-3, 3, 3, 3): 0,\n", " (-3, 3, 3, 4): 0,\n", " (-3, 3, 3, 5): 0,\n", " (-2, -3, -3, 0): 0,\n", " (-2, -3, -3, 1): 0,\n", " (-2, -3, -3, 2): 0,\n", " (-2, -3, -3, 3): 0,\n", " (-2, -3, -3, 4): 0,\n", " (-2, -3, -3, 5): 0,\n", " (-2, -3, -2, 0): 0,\n", " (-2, -3, -2, 1): 0,\n", " (-2, -3, -2, 2): 0,\n", " (-2, -3, -2, 3): 0,\n", " (-2, -3, -2, 4): 0,\n", " (-2, -3, -2, 5): 0,\n", " (-2, -3, -1, 0): 0,\n", " (-2, -3, -1, 1): 0,\n", " (-2, -3, -1, 2): 0,\n", " (-2, -3, -1, 3): 0,\n", " (-2, -3, -1, 4): 0,\n", " (-2, -3, -1, 5): 0,\n", " (-2, -3, 0, 0): -0.1,\n", " (-2, -3, 0, 1): -0.1,\n", " (-2, -3, 0, 2): -0.1,\n", " (-2, -3, 0, 3): -100.0,\n", " (-2, -3, 0, 4): 0,\n", " (-2, -3, 0, 5): 0,\n", " (-2, -3, 1, 0): 0,\n", " (-2, -3, 1, 1): 0,\n", " (-2, -3, 1, 2): 0,\n", " (-2, -3, 1, 3): 0,\n", " (-2, -3, 1, 4): 0,\n", " (-2, -3, 1, 5): -0.1,\n", " (-2, -3, 2, 0): 0,\n", " (-2, -3, 2, 1): 0,\n", " (-2, -3, 2, 2): -0.1,\n", " (-2, -3, 2, 3): 0,\n", " (-2, -3, 2, 4): 0,\n", " (-2, -3, 2, 5): 0,\n", " (-2, -3, 3, 0): -0.1,\n", " (-2, -3, 3, 1): 0,\n", " (-2, -3, 3, 2): 0,\n", " (-2, -3, 3, 3): 0,\n", " (-2, -3, 3, 4): 0,\n", " (-2, -3, 3, 5): 0,\n", " (-2, -2, -3, 0): -0.1,\n", " (-2, -2, -3, 1): 0,\n", " (-2, -2, -3, 2): 0,\n", " (-2, -2, -3, 3): 0,\n", " (-2, -2, -3, 4): 0,\n", " (-2, -2, -3, 5): 0,\n", " (-2, -2, -2, 0): 0,\n", " (-2, -2, -2, 1): 0,\n", " (-2, -2, -2, 2): 0,\n", " (-2, -2, -2, 3): 0,\n", " (-2, -2, -2, 4): 0,\n", " (-2, -2, -2, 5): 0,\n", " (-2, -2, -1, 0): -0.1,\n", " (-2, -2, -1, 1): 0,\n", " (-2, -2, -1, 2): 0,\n", " (-2, -2, -1, 3): 0,\n", " (-2, -2, -1, 4): 0,\n", " (-2, -2, -1, 5): 0,\n", " (-2, -2, 0, 0): -0.1,\n", " (-2, -2, 0, 1): -0.1,\n", " (-2, -2, 0, 2): -0.1,\n", " (-2, -2, 0, 3): -0.1,\n", " (-2, -2, 0, 4): -0.1,\n", " (-2, -2, 0, 5): 0,\n", " (-2, -2, 1, 0): -0.1,\n", " (-2, -2, 1, 1): -0.1,\n", " (-2, -2, 1, 2): -0.1,\n", " (-2, -2, 1, 3): -0.1,\n", " (-2, -2, 1, 4): 0,\n", " (-2, -2, 1, 5): 0,\n", " (-2, -2, 2, 0): -0.1,\n", " (-2, -2, 2, 1): -0.1,\n", " (-2, -2, 2, 2): -0.1,\n", " (-2, -2, 2, 3): -0.1,\n", " (-2, -2, 2, 4): -0.1,\n", " (-2, -2, 2, 5): 0,\n", " (-2, -2, 3, 0): -0.1,\n", " (-2, -2, 3, 1): -0.1,\n", " (-2, -2, 3, 2): 0,\n", " (-2, -2, 3, 3): -0.1,\n", " (-2, -2, 3, 4): 0,\n", " (-2, -2, 3, 5): 0,\n", " (-2, -1, -3, 0): 0,\n", " (-2, -1, -3, 1): 0,\n", " (-2, -1, -3, 2): 0,\n", " (-2, -1, -3, 3): 0,\n", " (-2, -1, -3, 4): 0,\n", " (-2, -1, -3, 5): 0,\n", " (-2, -1, -2, 0): -0.1,\n", " (-2, -1, -2, 1): 0,\n", " (-2, -1, -2, 2): 0,\n", " (-2, -1, -2, 3): 0,\n", " (-2, -1, -2, 4): 0,\n", " (-2, -1, -2, 5): 0,\n", " (-2, -1, -1, 0): -0.1,\n", " (-2, -1, -1, 1): -0.1,\n", " (-2, -1, -1, 2): -0.1,\n", " (-2, -1, -1, 3): 0,\n", " (-2, -1, -1, 4): -0.1,\n", " (-2, -1, -1, 5): 0,\n", " (-2, -1, 0, 0): -0.2071,\n", " (-2, -1, 0, 1): -0.19,\n", " (-2, -1, 0, 2): -0.1,\n", " (-2, -1, 0, 3): -0.1,\n", " (-2, -1, 0, 4): -0.1,\n", " (-2, -1, 0, 5): -0.1,\n", " (-2, -1, 1, 0): -0.1,\n", " (-2, -1, 1, 1): -0.1,\n", " (-2, -1, 1, 2): -0.1,\n", " (-2, -1, 1, 3): -0.1,\n", " (-2, -1, 1, 4): -0.1,\n", " (-2, -1, 1, 5): -0.10900000000000001,\n", " (-2, -1, 2, 0): -0.1,\n", " (-2, -1, 2, 1): -0.1,\n", " (-2, -1, 2, 2): -0.1,\n", " (-2, -1, 2, 3): -0.1,\n", " (-2, -1, 2, 4): -0.1,\n", " (-2, -1, 2, 5): 0,\n", " (-2, -1, 3, 0): -0.1,\n", " (-2, -1, 3, 1): 0,\n", " (-2, -1, 3, 2): -0.1,\n", " (-2, -1, 3, 3): 0,\n", " (-2, -1, 3, 4): 0,\n", " (-2, -1, 3, 5): 0,\n", " (-2, 0, -3, 0): -0.1,\n", " (-2, 0, -3, 1): 0,\n", " (-2, 0, -3, 2): 0,\n", " (-2, 0, -3, 3): 0,\n", " (-2, 0, -3, 4): 0,\n", " (-2, 0, -3, 5): 0,\n", " (-2, 0, -2, 0): 0,\n", " (-2, 0, -2, 1): 0,\n", " (-2, 0, -2, 2): 0,\n", " (-2, 0, -2, 3): 0,\n", " (-2, 0, -2, 4): 0,\n", " (-2, 0, -2, 5): -0.1,\n", " (-2, 0, -1, 0): -0.1,\n", " (-2, 0, -1, 1): -0.1,\n", " (-2, 0, -1, 2): -0.1,\n", " (-2, 0, -1, 3): -0.1,\n", " (-2, 0, -1, 4): -0.10900000000000001,\n", " (-2, 0, -1, 5): 0,\n", " (-2, 0, 0, 0): -0.2071,\n", " (-2, 0, 0, 1): -0.19,\n", " (-2, 0, 0, 2): -0.1,\n", " (-2, 0, 0, 3): -0.1,\n", " (-2, 0, 0, 4): -0.1,\n", " (-2, 0, 0, 5): -0.1,\n", " (-2, 0, 1, 0): -0.1,\n", " (-2, 0, 1, 1): -0.1,\n", " (-2, 0, 1, 2): -0.1,\n", " (-2, 0, 1, 3): -0.1,\n", " (-2, 0, 1, 4): -0.1,\n", " (-2, 0, 1, 5): -0.10900000000000001,\n", " (-2, 0, 2, 0): -0.1,\n", " (-2, 0, 2, 1): -0.1,\n", " (-2, 0, 2, 2): -0.1,\n", " (-2, 0, 2, 3): -0.1,\n", " (-2, 0, 2, 4): 0,\n", " (-2, 0, 2, 5): 0,\n", " (-2, 0, 3, 0): -0.1,\n", " (-2, 0, 3, 1): -0.1,\n", " (-2, 0, 3, 2): -0.1,\n", " (-2, 0, 3, 3): 0,\n", " (-2, 0, 3, 4): 0,\n", " (-2, 0, 3, 5): 0,\n", " (-2, 1, -3, 0): 0,\n", " (-2, 1, -3, 1): 0,\n", " (-2, 1, -3, 2): 0,\n", " (-2, 1, -3, 3): 0,\n", " (-2, 1, -3, 4): 0,\n", " (-2, 1, -3, 5): 0,\n", " (-2, 1, -2, 0): 0,\n", " (-2, 1, -2, 1): 0,\n", " (-2, 1, -2, 2): 0,\n", " (-2, 1, -2, 3): 0,\n", " (-2, 1, -2, 4): 0,\n", " (-2, 1, -2, 5): 0,\n", " (-2, 1, -1, 0): -0.1,\n", " (-2, 1, -1, 1): -0.1,\n", " (-2, 1, -1, 2): -0.1,\n", " (-2, 1, -1, 3): 0,\n", " (-2, 1, -1, 4): 0,\n", " (-2, 1, -1, 5): 0,\n", " (-2, 1, 0, 0): -0.1,\n", " (-2, 1, 0, 1): -0.1,\n", " (-2, 1, 0, 2): -0.1,\n", " (-2, 1, 0, 3): -0.1,\n", " (-2, 1, 0, 4): -0.1,\n", " (-2, 1, 0, 5): 0,\n", " (-2, 1, 1, 0): -0.1,\n", " (-2, 1, 1, 1): -0.1,\n", " (-2, 1, 1, 2): -0.1,\n", " (-2, 1, 1, 3): -0.1,\n", " (-2, 1, 1, 4): 0,\n", " (-2, 1, 1, 5): 0,\n", " (-2, 1, 2, 0): -0.1,\n", " (-2, 1, 2, 1): -0.1,\n", " (-2, 1, 2, 2): 10.0,\n", " (-2, 1, 2, 3): 0,\n", " (-2, 1, 2, 4): 0,\n", " (-2, 1, 2, 5): 0,\n", " (-2, 1, 3, 0): -0.1,\n", " (-2, 1, 3, 1): -0.1,\n", " (-2, 1, 3, 2): -0.1,\n", " (-2, 1, 3, 3): 0,\n", " (-2, 1, 3, 4): 0,\n", " (-2, 1, 3, 5): 0,\n", " (-2, 2, -3, 0): 0,\n", " (-2, 2, -3, 1): 0,\n", " (-2, 2, -3, 2): 0,\n", " (-2, 2, -3, 3): 0,\n", " (-2, 2, -3, 4): 0,\n", " (-2, 2, -3, 5): 0,\n", " (-2, 2, -2, 0): 0,\n", " (-2, 2, -2, 1): 0,\n", " (-2, 2, -2, 2): 0,\n", " (-2, 2, -2, 3): 0,\n", " (-2, 2, -2, 4): 0,\n", " (-2, 2, -2, 5): 0,\n", " (-2, 2, -1, 0): -0.1,\n", " (-2, 2, -1, 1): -0.1,\n", " (-2, 2, -1, 2): -0.1,\n", " (-2, 2, -1, 3): 0,\n", " (-2, 2, -1, 4): 0,\n", " (-2, 2, -1, 5): 0,\n", " (-2, 2, 0, 0): -0.1,\n", " (-2, 2, 0, 1): -0.1,\n", " (-2, 2, 0, 2): -0.1,\n", " (-2, 2, 0, 3): -0.1,\n", " (-2, 2, 0, 4): 0,\n", " (-2, 2, 0, 5): 0,\n", " (-2, 2, 1, 0): -0.1,\n", " (-2, 2, 1, 1): -0.1,\n", " (-2, 2, 1, 2): -0.1,\n", " (-2, 2, 1, 3): 0,\n", " (-2, 2, 1, 4): 0,\n", " (-2, 2, 1, 5): 0,\n", " (-2, 2, 2, 0): 0,\n", " (-2, 2, 2, 1): 0,\n", " (-2, 2, 2, 2): 0,\n", " (-2, 2, 2, 3): 0,\n", " (-2, 2, 2, 4): 0,\n", " (-2, 2, 2, 5): 0,\n", " (-2, 2, 3, 0): -0.1,\n", " (-2, 2, 3, 1): -0.1,\n", " (-2, 2, 3, 2): -0.1,\n", " (-2, 2, 3, 3): 0,\n", " (-2, 2, 3, 4): 0,\n", " (-2, 2, 3, 5): 0,\n", " (-2, 3, -3, 0): 0,\n", " (-2, 3, -3, 1): 0,\n", " (-2, 3, -3, 2): 0,\n", " (-2, 3, -3, 3): 0,\n", " (-2, 3, -3, 4): 0,\n", " (-2, 3, -3, 5): 0,\n", " (-2, 3, -2, 0): 0,\n", " (-2, 3, -2, 1): 0,\n", " (-2, 3, -2, 2): 0,\n", " (-2, 3, -2, 3): 0,\n", " (-2, 3, -2, 4): 0,\n", " (-2, 3, -2, 5): 0,\n", " (-2, 3, -1, 0): -0.1,\n", " (-2, 3, -1, 1): -0.1,\n", " (-2, 3, -1, 2): -100.0,\n", " (-2, 3, -1, 3): 0,\n", " (-2, 3, -1, 4): 0,\n", " (-2, 3, -1, 5): 0,\n", " (-2, 3, 0, 0): -0.1,\n", " (-2, 3, 0, 1): -0.1,\n", " (-2, 3, 0, 2): -100.0,\n", " (-2, 3, 0, 3): -0.1,\n", " (-2, 3, 0, 4): 0,\n", " (-2, 3, 0, 5): 0,\n", " (-2, 3, 1, 0): -0.1,\n", " (-2, 3, 1, 1): 0,\n", " (-2, 3, 1, 2): 0,\n", " (-2, 3, 1, 3): 0,\n", " (-2, 3, 1, 4): 0,\n", " (-2, 3, 1, 5): -0.1,\n", " (-2, 3, 2, 0): 0,\n", " (-2, 3, 2, 1): 0,\n", " (-2, 3, 2, 2): 0,\n", " (-2, 3, 2, 3): 0,\n", " (-2, 3, 2, 4): 0,\n", " (-2, 3, 2, 5): 0,\n", " (-2, 3, 3, 0): -0.1,\n", " (-2, 3, 3, 1): 0,\n", " (-2, 3, 3, 2): 0,\n", " (-2, 3, 3, 3): 0,\n", " (-2, 3, 3, 4): 0,\n", " (-2, 3, 3, 5): 0,\n", " (-1, -3, -3, 0): 0,\n", " (-1, -3, -3, 1): 0,\n", " (-1, -3, -3, 2): 0,\n", " (-1, -3, -3, 3): 0,\n", " (-1, -3, -3, 4): 0,\n", " (-1, -3, -3, 5): 0,\n", " (-1, -3, -2, 0): 0,\n", " (-1, -3, -2, 1): 0,\n", " (-1, -3, -2, 2): 0,\n", " (-1, -3, -2, 3): 0,\n", " (-1, -3, -2, 4): 0,\n", " (-1, -3, -2, 5): 0,\n", " (-1, -3, -1, 0): -0.1,\n", " (-1, -3, -1, 1): 0,\n", " (-1, -3, -1, 2): 0,\n", " (-1, -3, -1, 3): 0,\n", " (-1, -3, -1, 4): 0,\n", " (-1, -3, -1, 5): 0,\n", " (-1, -3, 0, 0): -0.1,\n", " (-1, -3, 0, 1): -0.1,\n", " (-1, -3, 0, 2): -0.1,\n", " (-1, -3, 0, 3): 0,\n", " (-1, -3, 0, 4): 0,\n", " (-1, -3, 0, 5): 0,\n", " (-1, -3, 1, 0): -0.1,\n", " (-1, -3, 1, 1): 0,\n", " (-1, -3, 1, 2): 0,\n", " (-1, -3, 1, 3): 0,\n", " (-1, -3, 1, 4): 0,\n", " (-1, -3, 1, 5): 0,\n", " (-1, -3, 2, 0): 0,\n", " (-1, -3, 2, 1): 0,\n", " (-1, -3, 2, 2): 0,\n", " (-1, -3, 2, 3): 0,\n", " (-1, -3, 2, 4): 0,\n", " (-1, -3, 2, 5): 0,\n", " (-1, -3, 3, 0): -0.1,\n", " (-1, -3, 3, 1): 0,\n", " (-1, -3, 3, 2): 0,\n", " (-1, -3, 3, 3): 0,\n", " (-1, -3, 3, 4): 0,\n", " (-1, -3, 3, 5): 0,\n", " (-1, -2, -3, 0): -0.1,\n", " (-1, -2, -3, 1): -0.1,\n", " (-1, -2, -3, 2): -0.1,\n", " (-1, -2, -3, 3): 0,\n", " (-1, -2, -3, 4): 0,\n", " (-1, -2, -3, 5): 0,\n", " (-1, -2, -2, 0): -0.1,\n", " (-1, -2, -2, 1): 0,\n", " (-1, -2, -2, 2): 0,\n", " (-1, -2, -2, 3): 0,\n", " (-1, -2, -2, 4): 0,\n", " (-1, -2, -2, 5): 0,\n", " (-1, -2, -1, 0): -0.1,\n", " (-1, -2, -1, 1): -0.1,\n", " (-1, -2, -1, 2): -0.1,\n", " (-1, -2, -1, 3): -0.1,\n", " (-1, -2, -1, 4): 0,\n", " (-1, -2, -1, 5): 0,\n", " (-1, -2, 0, 0): -0.19,\n", " (-1, -2, 0, 1): -0.19,\n", " (-1, -2, 0, 2): -0.1,\n", " (-1, -2, 0, 3): -0.1,\n", " (-1, -2, 0, 4): -0.1,\n", " (-1, -2, 0, 5): -0.1,\n", " (-1, -2, 1, 0): -0.1,\n", " (-1, -2, 1, 1): -0.1,\n", " (-1, -2, 1, 2): -0.1,\n", " (-1, -2, 1, 3): -0.1,\n", " (-1, -2, 1, 4): -0.1,\n", " (-1, -2, 1, 5): 0,\n", " (-1, -2, 2, 0): -0.1,\n", " (-1, -2, 2, 1): -0.19,\n", " (-1, -2, 2, 2): 0.2988224121280776,\n", " (-1, -2, 2, 3): 0,\n", " (-1, -2, 2, 4): 0,\n", " (-1, -2, 2, 5): 0,\n", " (-1, -2, 3, 0): 0,\n", " (-1, -2, 3, 1): -0.1,\n", " (-1, -2, 3, 2): -0.1,\n", " (-1, -2, 3, 3): 0,\n", " (-1, -2, 3, 4): 0,\n", " (-1, -2, 3, 5): 0,\n", " (-1, -1, -3, 0): -0.1,\n", " (-1, -1, -3, 1): 0,\n", " (-1, -1, -3, 2): 0,\n", " (-1, -1, -3, 3): -0.1,\n", " (-1, -1, -3, 4): 0,\n", " (-1, -1, -3, 5): 0,\n", " (-1, -1, -2, 0): -0.1,\n", " (-1, -1, -2, 1): -0.1,\n", " (-1, -1, -2, 2): -0.1,\n", " (-1, -1, -2, 3): 0,\n", " (-1, -1, -2, 4): 0,\n", " (-1, -1, -2, 5): 0,\n", " (-1, -1, -1, 0): -0.199,\n", " (-1, -1, -1, 1): -0.1,\n", " (-1, -1, -1, 2): -0.1,\n", " (-1, -1, -1, 3): -0.1,\n", " (-1, -1, -1, 4): -0.10900000000000001,\n", " (-1, -1, -1, 5): -0.1,\n", " (-1, -1, 0, 0): -0.19981000000000002,\n", " (-1, -1, 0, 1): -0.28,\n", " (-1, -1, 0, 2): -0.2071,\n", " (-1, -1, 0, 3): -0.28,\n", " (-1, -1, 0, 4): 18.40262496861342,\n", " (-1, -1, 0, 5): -0.199,\n", " (-1, -1, 1, 0): 65.86334336319774,\n", " (-1, -1, 1, 1): -0.19,\n", " (-1, -1, 1, 2): -0.1,\n", " (-1, -1, 1, 3): -0.1,\n", " (-1, -1, 1, 4): -0.1,\n", " (-1, -1, 1, 5): -0.21520000000000003,\n", " (-1, -1, 2, 0): -0.1,\n", " (-1, -1, 2, 1): -0.1,\n", " (-1, -1, 2, 2): -0.1,\n", " (-1, -1, 2, 3): -0.1,\n", " (-1, -1, 2, 4): -0.1,\n", " (-1, -1, 2, 5): 14.668814426991721,\n", " (-1, -1, 3, 0): -0.19,\n", " (-1, -1, 3, 1): -0.1,\n", " (-1, -1, 3, 2): -0.1,\n", " (-1, -1, 3, 3): 0,\n", " (-1, -1, 3, 4): 0,\n", " (-1, -1, 3, 5): 0,\n", " (-1, 0, -3, 0): -0.19,\n", " (-1, 0, -3, 1): 0,\n", " (-1, 0, -3, 2): 0,\n", " (-1, 0, -3, 3): 0,\n", " (-1, 0, -3, 4): 0,\n", " (-1, 0, -3, 5): 0,\n", " (-1, 0, -2, 0): -0.1,\n", " (-1, 0, -2, 1): -0.1,\n", " (-1, 0, -2, 2): 0.11051000000000002,\n", " (-1, 0, -2, 3): 0,\n", " (-1, 0, -2, 4): 0,\n", " (-1, 0, -2, 5): 0,\n", " (-1, 0, -1, 0): -0.2071,\n", " (-1, 0, -1, 1): -0.19,\n", " (-1, 0, -1, 2): -0.19,\n", " (-1, 0, -1, 3): -0.1,\n", " (-1, 0, -1, 4): -0.10900000000000001,\n", " (-1, 0, -1, 5): -0.1,\n", " (-1, 0, 0, 0): 27.464163038655308,\n", " (-1, 0, 0, 1): -0.271,\n", " (-1, 0, 0, 2): -0.4274200000000001,\n", " (-1, 0, 0, 3): -0.28,\n", " (-1, 0, 0, 4): -0.28,\n", " (-1, 0, 0, 5): -0.28,\n", " (-1, 0, 1, 0): -0.199,\n", " (-1, 0, 1, 1): -0.19,\n", " (-1, 0, 1, 2): -0.1,\n", " (-1, 0, 1, 3): -0.1,\n", " (-1, 0, 1, 4): -0.1,\n", " (-1, 0, 1, 5): -0.10900000000000001,\n", " (-1, 0, 2, 0): -0.1,\n", " (-1, 0, 2, 1): -0.1,\n", " (-1, 0, 2, 2): -0.1,\n", " (-1, 0, 2, 3): -0.1,\n", " (-1, 0, 2, 4): -0.1,\n", " (-1, 0, 2, 5): 0,\n", " (-1, 0, 3, 0): -0.1,\n", " (-1, 0, 3, 1): -0.1,\n", " (-1, 0, 3, 2): -0.1,\n", " (-1, 0, 3, 3): 0,\n", " (-1, 0, 3, 4): -100.0,\n", " (-1, 0, 3, 5): 0,\n", " (-1, 1, -3, 0): 0,\n", " (-1, 1, -3, 1): 0,\n", " (-1, 1, -3, 2): 0,\n", " (-1, 1, -3, 3): 0,\n", " (-1, 1, -3, 4): 0,\n", " (-1, 1, -3, 5): 0,\n", " (-1, 1, -2, 0): 11.958052071671002,\n", " (-1, 1, -2, 1): 0,\n", " (-1, 1, -2, 2): 0,\n", " (-1, 1, -2, 3): 0,\n", " (-1, 1, -2, 4): 0,\n", " (-1, 1, -2, 5): 0,\n", " (-1, 1, -1, 0): -0.1,\n", " (-1, 1, -1, 1): -0.1,\n", " (-1, 1, -1, 2): -0.1,\n", " (-1, 1, -1, 3): -0.10900000000000001,\n", " (-1, 1, -1, 4): -0.10900000000000001,\n", " (-1, 1, -1, 5): 0.4599917900000001,\n", " (-1, 1, 0, 0): -0.2071,\n", " (-1, 1, 0, 1): -0.19,\n", " (-1, 1, 0, 2): -0.19,\n", " (-1, 1, 0, 3): 1.1124963822979703,\n", " (-1, 1, 0, 4): -0.19,\n", " (-1, 1, 0, 5): -0.19,\n", " (-1, 1, 1, 0): -0.1,\n", " (-1, 1, 1, 1): -0.1,\n", " (-1, 1, 1, 2): -0.1,\n", " (-1, 1, 1, 3): -0.1,\n", " (-1, 1, 1, 4): -0.1,\n", " (-1, 1, 1, 5): -0.10900000000000001,\n", " (-1, 1, 2, 0): -0.1,\n", " (-1, 1, 2, 1): -0.1,\n", " (-1, 1, 2, 2): -0.1,\n", " (-1, 1, 2, 3): -0.1,\n", " (-1, 1, 2, 4): 0,\n", " (-1, 1, 2, 5): 0,\n", " (-1, 1, 3, 0): -0.1,\n", " (-1, 1, 3, 1): -0.1,\n", " (-1, 1, 3, 2): -0.1,\n", " (-1, 1, 3, 3): 0,\n", " (-1, 1, 3, 4): 0,\n", " (-1, 1, 3, 5): 0,\n", " (-1, 2, -3, 0): 0,\n", " (-1, 2, -3, 1): 0,\n", " (-1, 2, -3, 2): 0,\n", " (-1, 2, -3, 3): 0,\n", " (-1, 2, -3, 4): 0,\n", " (-1, 2, -3, 5): 0,\n", " (-1, 2, -2, 0): -0.1,\n", " (-1, 2, -2, 1): 0,\n", " (-1, 2, -2, 2): 0,\n", " (-1, 2, -2, 3): 0,\n", " (-1, 2, -2, 4): 0,\n", " (-1, 2, -2, 5): 0,\n", " (-1, 2, -1, 0): -0.19,\n", " (-1, 2, -1, 1): -0.1,\n", " (-1, 2, -1, 2): 0,\n", " (-1, 2, -1, 3): 0,\n", " (-1, 2, -1, 4): 0,\n", " (-1, 2, -1, 5): 0,\n", " (-1, 2, 0, 0): -0.1,\n", " (-1, 2, 0, 1): -0.1,\n", " (-1, 2, 0, 2): -0.1,\n", " (-1, 2, 0, 3): -0.10900000000000001,\n", " (-1, 2, 0, 4): 0,\n", " (-1, 2, 0, 5): 0,\n", " (-1, 2, 1, 0): -0.1,\n", " (-1, 2, 1, 1): -0.1,\n", " (-1, 2, 1, 2): -0.1,\n", " (-1, 2, 1, 3): 0,\n", " (-1, 2, 1, 4): 0,\n", " (-1, 2, 1, 5): 0,\n", " (-1, 2, 2, 0): -0.1,\n", " (-1, 2, 2, 1): 10.0,\n", " (-1, 2, 2, 2): 0,\n", " (-1, 2, 2, 3): 0,\n", " (-1, 2, 2, 4): 0,\n", " (-1, 2, 2, 5): 0,\n", " (-1, 2, 3, 0): -0.1,\n", " (-1, 2, 3, 1): -0.1,\n", " (-1, 2, 3, 2): 0,\n", " (-1, 2, 3, 3): 0,\n", " (-1, 2, 3, 4): 0,\n", " (-1, 2, 3, 5): 0,\n", " (-1, 3, -3, 0): 0,\n", " (-1, 3, -3, 1): 0,\n", " (-1, 3, -3, 2): 0,\n", " (-1, 3, -3, 3): 0,\n", " (-1, 3, -3, 4): 0,\n", " (-1, 3, -3, 5): 0,\n", " (-1, 3, -2, 0): 0,\n", " (-1, 3, -2, 1): 0,\n", " (-1, 3, -2, 2): 0,\n", " (-1, 3, -2, 3): 0,\n", " (-1, 3, -2, 4): 0,\n", " (-1, 3, -2, 5): 0,\n", " (-1, 3, -1, 0): -0.1,\n", " (-1, 3, -1, 1): -0.1,\n", " (-1, 3, -1, 2): 0,\n", " (-1, 3, -1, 3): 0,\n", " (-1, 3, -1, 4): 0,\n", " (-1, 3, -1, 5): 0,\n", " (-1, 3, 0, 0): -0.1,\n", " (-1, 3, 0, 1): -0.1,\n", " (-1, 3, 0, 2): -100.0,\n", " (-1, 3, 0, 3): 0,\n", " (-1, 3, 0, 4): 0,\n", " (-1, 3, 0, 5): 0,\n", " (-1, 3, 1, 0): -0.1,\n", " (-1, 3, 1, 1): -0.1,\n", " (-1, 3, 1, 2): -100.0,\n", " (-1, 3, 1, 3): 0,\n", " (-1, 3, 1, 4): 0,\n", " (-1, 3, 1, 5): 0,\n", " (-1, 3, 2, 0): 0,\n", " (-1, 3, 2, 1): 0,\n", " (-1, 3, 2, 2): 0,\n", " (-1, 3, 2, 3): 0,\n", " (-1, 3, 2, 4): 0,\n", " (-1, 3, 2, 5): 0,\n", " (-1, 3, 3, 0): -0.1,\n", " (-1, 3, 3, 1): 0,\n", " (-1, 3, 3, 2): 0,\n", " (-1, 3, 3, 3): 0,\n", " (-1, 3, 3, 4): 0,\n", " (-1, 3, 3, 5): 0,\n", " (0, -3, -3, 0): 0,\n", " (0, -3, -3, 1): 0,\n", " (0, -3, -3, 2): 0,\n", " (0, -3, -3, 3): 0,\n", " (0, -3, -3, 4): 0,\n", " (0, -3, -3, 5): 0,\n", " (0, -3, -2, 0): -0.1,\n", " (0, -3, -2, 1): 0,\n", " (0, -3, -2, 2): 0,\n", " (0, -3, -2, 3): 0,\n", " (0, -3, -2, 4): 0,\n", " (0, -3, -2, 5): 0,\n", " (0, -3, -1, 0): -0.1,\n", " (0, -3, -1, 1): 0,\n", " (0, -3, -1, 2): 0,\n", " (0, -3, -1, 3): 0,\n", " (0, -3, -1, 4): 0,\n", " (0, -3, -1, 5): 0,\n", " (0, -3, 0, 0): -0.1,\n", " (0, -3, 0, 1): -0.1,\n", " (0, -3, 0, 2): -0.10900000000000001,\n", " (0, -3, 0, 3): 0,\n", " (0, -3, 0, 4): 0,\n", " (0, -3, 0, 5): 0,\n", " (0, -3, 1, 0): -0.1,\n", " (0, -3, 1, 1): 0,\n", " (0, -3, 1, 2): 0,\n", " (0, -3, 1, 3): 0,\n", " (0, -3, 1, 4): 0,\n", " (0, -3, 1, 5): 0,\n", " (0, -3, 2, 0): -0.1,\n", " (0, -3, 2, 1): 0,\n", " (0, -3, 2, 2): 0,\n", " (0, -3, 2, 3): 0,\n", " (0, -3, 2, 4): 0,\n", " (0, -3, 2, 5): 0,\n", " (0, -3, 3, 0): 0,\n", " (0, -3, 3, 1): 0,\n", " (0, -3, 3, 2): 0,\n", " (0, -3, 3, 3): -100.0,\n", " (0, -3, 3, 4): 0,\n", " (0, -3, 3, 5): 0,\n", " (0, -2, -3, 0): -0.1,\n", " (0, -2, -3, 1): -0.1,\n", " (0, -2, -3, 2): 0,\n", " (0, -2, -3, 3): 0,\n", " (0, -2, -3, 4): 0,\n", " (0, -2, -3, 5): 0,\n", " (0, -2, -2, 0): -0.1,\n", " (0, -2, -2, 1): -0.1,\n", " (0, -2, -2, 2): -0.1,\n", " (0, -2, -2, 3): -0.1,\n", " (0, -2, -2, 4): 0,\n", " (0, -2, -2, 5): 0,\n", " (0, -2, -1, 0): -0.1,\n", " (0, -2, -1, 1): -0.1,\n", " (0, -2, -1, 2): 2.050400310947255,\n", " (0, -2, -1, 3): 0,\n", " (0, -2, -1, 4): 0,\n", " (0, -2, -1, 5): -0.1,\n", " (0, -2, 0, 0): -0.19,\n", " (0, -2, 0, 1): -0.28,\n", " (0, -2, 0, 2): -0.10900000000000001,\n", " (0, -2, 0, 3): -0.19,\n", " (0, -2, 0, 4): 8.274155903896565,\n", " (0, -2, 0, 5): -0.14424720350457854,\n", " (0, -2, 1, 0): -0.1,\n", " (0, -2, 1, 1): -0.19,\n", " (0, -2, 1, 2): 48.45146727888372,\n", " (0, -2, 1, 3): 0,\n", " (0, -2, 1, 4): 2.070709843795268,\n", " (0, -2, 1, 5): -0.1,\n", " (0, -2, 2, 0): -0.1,\n", " (0, -2, 2, 1): -0.1,\n", " (0, -2, 2, 2): 36.446388594117,\n", " (0, -2, 2, 3): -0.1,\n", " (0, -2, 2, 4): 0,\n", " (0, -2, 2, 5): 1.2519378768773166,\n", " (0, -2, 3, 0): 0,\n", " (0, -2, 3, 1): -0.1,\n", " (0, -2, 3, 2): 0,\n", " (0, -2, 3, 3): 0,\n", " (0, -2, 3, 4): 0,\n", " (0, -2, 3, 5): 0,\n", " (0, -1, -3, 0): -0.1,\n", " (0, -1, -3, 1): -0.1,\n", " (0, -1, -3, 2): -0.1,\n", " (0, -1, -3, 3): -0.1,\n", " (0, -1, -3, 4): 0,\n", " (0, -1, -3, 5): 0,\n", " (0, -1, -2, 0): -0.1,\n", " (0, -1, -2, 1): -0.1,\n", " (0, -1, -2, 2): -0.1,\n", " (0, -1, -2, 3): -0.1,\n", " (0, -1, -2, 4): -0.10981000000000002,\n", " (0, -1, -2, 5): -0.1,\n", " (0, -1, -1, 0): -0.19,\n", " (0, -1, -1, 1): -0.19,\n", " (0, -1, -1, 2): -0.2071,\n", " (0, -1, -1, 3): -0.19,\n", " (0, -1, -1, 4): 45.61495732258227,\n", " (0, -1, -1, 5): -0.19,\n", " (0, -1, 0, 0): 1.5260213577053514,\n", " (0, -1, 0, 1): 1.7356897087921597,\n", " (0, -1, 0, 2): 27.049643885930028,\n", " (0, -1, 0, 3): -0.05359942334174134,\n", " (0, -1, 0, 4): 79.09999999999982,\n", " (0, -1, 0, 5): 14.751684730084431,\n", " (0, -1, 1, 0): 41.392937365377065,\n", " (0, -1, 1, 1): 20.85122104625053,\n", " (0, -1, 1, 2): 11.16102082300838,\n", " (0, -1, 1, 3): 10.889174133863674,\n", " (0, -1, 1, 4): 88.99999999999989,\n", " (0, -1, 1, 5): 32.888154923544086,\n", " (0, -1, 2, 0): 99.99999999999994,\n", " (0, -1, 2, 1): 1.2143224251392555,\n", " (0, -1, 2, 2): 20.08365816344115,\n", " (0, -1, 2, 3): 7.579850544839898,\n", " ...}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["table.table"]}, {"cell_type": "code", "execution_count": 42, "id": "20d46ac8", "metadata": {}, "outputs": [], "source": ["#学习完成后，我们来进行实战\n", "#这个时候不用更新Qtable了"]}, {"cell_type": "code", "execution_count": 43, "id": "a7eca8fe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n", "4\n", "4\n", "0\n", "Episode: 499, Score: 97\n"]}], "source": ["s0 = [0, 0,0]\n", "is_terminated=False\n", "while not is_terminated:\n", "    # within one episode\n", "    action = table.excute_policy(s0)\n", "    print(action)\n", "    r, s1, is_terminated = env.step(action)\n", "    ##print(r, s1, action,is_terminated)\n", "    #table.update(action, s0, s1, r, is_terminated)\n", "    #episodic_reward += r\n", "    # env.render(frames=100)\n", "    s0 = s1\n", "print(\"Score: {}\".format(episodic_reward))\n", "env.reset()"]}, {"cell_type": "code", "execution_count": 44, "id": "55bec82a", "metadata": {}, "outputs": [], "source": ["change = [[0, 1], [0, -1], [1, 1],[1, -1],[2, 1],[2, -1]]"]}, {"cell_type": "code", "execution_count": 46, "id": "ebe2f2fc", "metadata": {}, "outputs": [{"data": {"text/plain": ["([1, -1], [2, 1], [2, 1], [0, 1])"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["change[3],change[4],change[4],change[0]"]}, {"cell_type": "code", "execution_count": null, "id": "38fa4a21", "metadata": {}, "outputs": [], "source": ["(1,-1,2)"]}, {"cell_type": "code", "execution_count": 60, "id": "8afbd012", "metadata": {}, "outputs": [{"data": {"text/plain": ["  \u001b[38;5;1m 0.25    \u001b[0m \u001b[38;5;1m-0.433   \u001b[0m \u001b[38;5;1m 0.866   \u001b[0m \u001b[38;5;4m-0.04143 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.433   \u001b[0m \u001b[38;5;1m-0.75    \u001b[0m \u001b[38;5;1m-0.5     \u001b[0m \u001b[38;5;4m-0.07175 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;1m 0.866   \u001b[0m \u001b[38;5;1m 0.5     \u001b[0m \u001b[38;5;1m 0       \u001b[0m \u001b[38;5;4m 0.04145 \u001b[0m  \u001b[0m\n", "  \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 0       \u001b[0m \u001b[38;5;244m 1       \u001b[0m  \u001b[0m\n"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["T2=DFbot.fkine([np.pi/3,-np.pi/3,np.pi/3*2])\n", "T2"]}, {"cell_type": "code", "execution_count": 33, "id": "be201fa8", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.006738225000000001"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["target=[-0.04,-0.07,0.04]\n", "sum([x*x for x in np.array(T2)[:,-1][0:3]-np.array(target) ])"]}, {"cell_type": "code", "execution_count": null, "id": "2c0079e6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "yolov11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}