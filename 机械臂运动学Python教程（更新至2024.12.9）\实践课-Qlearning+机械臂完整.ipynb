%matplotlib auto

import time
import random

from roboticstoolbox import *
from spatialmath import *
from math import pi
import numpy as np
from matplotlib import pyplot as plt

DFbot = DHRobot(
    [
                    RevoluteDH(d=0.04145,alpha=np.pi/2,qlim=np.array([-np.pi,np.pi])),
                    RevoluteDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),
                    RevoluteDH(a=-0.08285,qlim=np.array([-np.pi,np.pi])),
                  
    ],
    name="DFbot",
)

DFbot.plot([0,0,0])

DFbot.plot([np.pi/3,np.pi/3,-np.pi/3*2])

DFbot.fkine([np.pi/3,np.pi/3,-np.pi/3*2])

#Q-learning

class Env():
    def __init__(self, num_deg,target,robot,unit):
        self.num_deg=num_deg           #关节数量
        self.d = tuple([0]*self.num_deg) #初始化角度，根据初始化的关节数量，来设置几个角度初始化
        self.target=target#可能是目标点
        self.robot=robot#将运动学公式封装进去
        self.unit=unit#单位转换因子
        
        
    def step(self, action):
        """6个可执行action，3个自由度，每个自由度正转1个单位或反转1个单位"""
        change = [[0, 1], [0, -1], [1, 1],[1, -1],[2, 1],[2, -1]]#/6个可执行action【0关节, 1正反转，或者说每一步，正走还是反转】
        index=change[action][0] #获取控制关节
        move=change[action][1]  #获取正反转，或者说每一步，正走还是反转
        dtemp=list(self.d)      #转化为列表
        dtemp[index] = dtemp[index] + move #存取度数
        self.d=tuple(dtemp)               #转化为元组，存储回去
        pose=self.robot.fkine([x*self.unit for x in self.d])#通过取度数转化为笛卡尔坐标

        states = self.d
        reward = -1 #每走一步，减少1分
        terminal = False   #终极目标未达到
        distance= sum([x*x for x in np.array(pose)[:,-1][0:3]-np.array(self.target)])#取矩阵的右上角的三个坐标，获取位置信息，并且求取与目标位置的距离
        ##print(pose)
        if distance<=0.005:#判断是否接近目标位置，
            terminal = True
            reward=100
        check_boundary=[((self.d[i]*self.unit>=self.robot.qlim[0][i]) and (self.d[i]*self.unit<=self.robot.qlim[1][i])) for i in range(len(self.d)) ]            
        #print(check_boundary)
        if not (all(check_boundary)):#检查是否超出限制角度
            reward = -1000
            terminal = True
            
        return reward, states, terminal

    def reset(self):
        self.d = tuple([0]*self.num_deg)

class Q_table():
    def __init__(self, steps, actions, alpha, gamma,unit):
        self.d=tuple([0]*3)
        self.steps=5                 #每个关节允许走的最多步数
        self.actions = actions       #可执行的动作数量3个关节*2个方向
        self.table = dict() # initialize all Q(d,a) dictionary
        self.alpha = alpha  #学习率
        self.gamma = gamma  #折扣因子
        self.unit=unit      #单位转换因子
        
    def _init_table(self):         #初始化q表
        steps=self.steps           #将初始化步数赋值给steps
        d0=np.mgrid[-steps:(steps+1):1]  #d1关节，生成-steps到steps的步数
        d1=np.mgrid[-steps:(steps+1):1]  #d2关节生成-steps到steps的步数
        d2=np.mgrid[-steps:(steps+1):1]  #生成-steps到steps的步数
        for i1 in d0.flatten():          # 关节0的所有可能位置（flatten是为了确保d为一维数组）
            for i2 in d1.flatten():      #关节1的所有可能位置
                for i3 in d2.flatten():  #关节2的所有可能位置
                    for i4 in range(self.actions):       #所有可能的动作
                        self.table[tuple([i1,i2,i3,i4])]=0      #创建q表

    def _epsilon(self,num_episode):
        #return 0.1
        # version for better convergence:
        # """At the beginning epsilon is 0.2, after 300 episodes decades to 0.05, and eventually go to 0."""
        return 20. / (num_episode + 100) #实现更好的回收函数，因为越接近与拟合的终点，收敛值越小

    #在状态s下采用epsi-greedy方法选出一个概率action（有随机性）
    def take_action(self, s, num_episode):
        """epsilon-greedy action selection"""
        if random.random() < self._epsilon(1000): #生成一个随机数，如果小于这个值，则开始探索
            return int(random.random() * 6)    #少部分时间去获取更好的解
        else:
            actions_value = [self.table[(s[0],s[1],s[2],a)] for a in range(self.actions)]
            return actions_value.index(max(actions_value))              #不知道是啥
    
    #在状态s下方法选取价值最大的action（没有随机性）
    def excute_policy(self,s):
            actions_value = [self.table[(s[0],s[1],s[2],a)] for a in range(self.actions)]
            return actions_value.index(max(actions_value))
            
    #在状态s下找到下一步最大的价值的数值
    def max_q(self, s):
        actions_value = [self.table[(s[0],s[1],s[2],a)] for a in range(self.actions)]
        return max(actions_value)
    
    #更新Q-table
    def update(self, a, s0, s1, r, is_terminated):
        q_current = self.table[(s0[0],s0[1],s0[2],a)]
        #如果没有终止，就更新
        if not is_terminated:
            q_target = r + self.gamma * self.max_q(s1) #gamma 0只关心即时奖励，1未来的奖励和即时的奖励同等重要，0。9重视未来，但有所折扣
        #如果终止了，更新值为0
        else:
            q_target = r
        self.table[(s0[0],s0[1],s0[2],a)] += self.alpha * (q_target - q_current) # alpha 0就是不学习 1就是完全相信新的经验 0.1平衡历史经验

env = Env(3,[-0.04,-0.07,0.04],DFbot,np.pi/6)

table = Q_table(6, 6, 0.1,0.9,np.pi/6)

table._init_table()

table.table

for num_episode in range(500):
        episodic_reward = 0
        is_terminated = False
        s0 = [0, 0,0]
        while not is_terminated:
            # within one episode
            action = table.take_action(s0, num_episode)
            r, s1, is_terminated = env.step(action)  #r代表奖励值，s1代表新状态，is_terminated代表是否终止
            ##print(r, s1, action,is_terminated)
            table.update(action, s0, s1, r, is_terminated)
            episodic_reward += r
            # env.render(frames=100)
            s0 = s1
        print("Episode: {}, Score: {}".format(num_episode, episodic_reward))
        env.reset()

table.table

#学习完成后，我们来进行实战
#这个时候不用更新Qtable了

s0 = [0, 0,0]
is_terminated=False
while not is_terminated:
    # within one episode
    action = table.excute_policy(s0)
    print(action)
    r, s1, is_terminated = env.step(action)
    ##print(r, s1, action,is_terminated)
    #table.update(action, s0, s1, r, is_terminated)
    #episodic_reward += r
    # env.render(frames=100)
    s0 = s1
print("Score: {}".format(episodic_reward))
env.reset()

change = [[0, 1], [0, -1], [1, 1],[1, -1],[2, 1],[2, -1]]

change[3],change[4],change[4],change[0]

(1,-1,2)

T2=DFbot.fkine([np.pi/3,-np.pi/3,np.pi/3*2])
T2

target=[-0.04,-0.07,0.04]
sum([x*x for x in np.array(T2)[:,-1][0:3]-np.array(target) ])

